import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import { obfuscator } from "rollup-obfuscator";

export default defineConfig(({ command }) => {
    const isProduction = command === "build";
    return {
        plugins: [
            laravel({
                input: [
                    "resources/css/webinar.css",
                    "resources/js/webinar/video-manager.js",
                    "resources/js/webinar/chat-manager.js",
                    "resources/js/webinar/seeding-util.js",
                    "resources/js/webinar/advertisement-manager.js",
                    "resources/js/common/modal-util.js",
                    "resources/js/common/webinar-common.js",
                    "resources/js/elearning/interaction-modal.js",
                    "resources/js/elearning/template-loader.js",
                ],
                refresh: false,
            }),
            // JavaScript obfuscation for production builds
            obfuscator({
                include: ["**/resources/js/webinar/**/*.js"],
                exclude: ["node_modules/**"],
                options: {
                    compact: true,
                    controlFlowFlattening: true,
                    controlFlowFlatteningThreshold: 0.75,
                    deadCodeInjection: true,
                    deadCodeInjectionThreshold: 0.4,
                    debugProtection: true,
                    debugProtectionInterval: true,
                    disableConsoleOutput: true,
                    identifierNamesGenerator: "hexadecimal",
                    renameGlobals: false,
                    rotateStringArray: true,
                    selfDefending: true,
                    splitStrings: true,
                    splitStringsChunkLength: 10,
                    stringArray: true,
                    stringArrayEncoding: ["base64"],
                    stringArrayThreshold: 0.75,
                    transformObjectKeys: true,
                    unicodeEscapeSequence: false,
                },
            }),
        ],
        build: {
            sourcemap: false, // disable sourcemaps in production for better obfuscation
            minify: "terser", // Use terser for JS minification
            terserOptions: {
                compress: {
                    drop_console: isProduction, // Remove console.log in production
                    drop_debugger: true,
                    pure_funcs: [
                        "console.log",
                        "console.info",
                        "console.debug",
                        "console.warn",
                    ],
                },
                mangle: true,
                format: {
                    comments: false,
                },
            },
            cssMinify: true, // Enable CSS minification
            cssCodeSplit: true, // Split CSS into chunks
            rollupOptions: {
                output: {
                    manualChunks: {
                        vendor: ["jquery", "axios", "sweetalert2"],
                        webinar: [
                            "resources/js/webinar/video-manager.js",
                            "resources/js/webinar/chat-manager.js",
                        ],
                        advertisements: [
                            "resources/js/webinar/modal-util.js",
                            "resources/js/webinar/seeding-util.js",
                            "resources/js/webinar/advertisement-manager.js",
                            "resources/js/webinar/webinar-common.js",
                        ],
                    },
                },
            },
        },
        server: {
            host: "0.0.0.0",
            hmr: {
                host: "0.0.0.0",
                protocol: "ws",
            },
            port: 5173,
            // cors: {
            //     origin: "*", // Allow all origins
            //     methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
            //     preflightContinue: false,
            //     optionsSuccessStatus: 204,
            // },
            // headers: {
            //     "Access-Control-Allow-Origin": "*",
            //     "Access-Control-Allow-Methods":
            //         "GET,HEAD,PUT,PATCH,POST,DELETE",
            //     "Access-Control-Allow-Headers":
            //         "Origin, X-Requested-With, Content-Type, Accept",
            // },
        },
    };
});
