<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');
Schedule::command('queue:work --stop-when-empty')->everyMinute();
Schedule::command('app:create-file-comment-webinar')->everyMinute();
Schedule::command('app:disable-livestream')->everyMinute();

// Cleanup deleted webinars daily at 2 AM
Schedule::command('webinar:cleanup-deleted --force')->dailyAt('02:00');
//php artisan webinar:cleanup-deleted --days=0 --force
