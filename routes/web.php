<?php

use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\QuestionController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebinarController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\JoinController;
use App\Http\Controllers\ParticipantController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\AdvertisementController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\SmartLinkController;
use App\Http\Controllers\TelegramSetupController;
use App\Http\Controllers\WebinarAnalyticsController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\SepayController;
use App\Http\Controllers\Admin\ModuleController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Auth::routes(['register' => false]);

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Webinar Routes
Route::group(['middleware' => ['auth', 'check.storage.limit']], function () {
    Route::resource('webinars', WebinarController::class);

    // Trashed webinars routes
    Route::get('webinars-trashed', [WebinarController::class, 'trashed'])->name('webinars.trashed');
    Route::post('webinars/{id}/restore', [WebinarController::class, 'restore'])->name('webinars.restore');
    Route::delete('webinars/{id}/force-delete', [WebinarController::class, 'forceDelete'])->name('webinars.force-delete');

    // Smart Link Routes
    Route::resource('smart-links', SmartLinkController::class);

    Route::get('webinars/{webinar}/livestream', [WebinarController::class, 'livestreamManager'])->name('webinars.livestream');
    Route::get('webinars/{webinar}/livestream-learn', [WebinarController::class, 'livestreamLearningManager'])->name('webinars.livestream.class');
    Route::get('webinars/{webinar}/get-data-live', [WebinarController::class, 'getDataLivestream'])->name('webinars.get.data.livestream');
    Route::post('webinars/{webinar}/livestream', [WebinarController::class, 'UpdateLivestreamManager'])->name('webinars.livestream.post');
    Route::post('webinars/{webinar}/push-advertisement', [WebinarController::class, 'pushAdvertisement'])->name('webinars.livestream.push.advertisement');
    Route::post('webinars/{webinar}/toggle-question', [WebinarController::class, 'toggleQuestion'])->name('webinars.livestream.toggle-question');
    Route::get('webinars/{webinar}/upload', [WebinarController::class, 'showUploadForm'])->name('webinars.upload');
    Route::post('webinars/{webinar}/upload', [WebinarController::class, 'uploadVideo'])->name('webinars.upload.post');
    Route::post('webinars/{webinar}/regenerate-url', [WebinarController::class, 'regenerateJoinUrl'])->name('webinars.regenerate-url');

    // Student Management Routes
    Route::get('webinars/{webinar}/students', [WebinarController::class, 'studentsManager'])->name('webinars.students');
    Route::get('webinars/{webinar}/students/search', [WebinarController::class, 'searchUsers'])->name('webinars.students.search');
    Route::get('webinars/{webinar}/students/list', [WebinarController::class, 'listStudents'])->name('webinars.students.list');
    Route::post('webinars/{webinar}/students/add', [WebinarController::class, 'addStudent'])->name('webinars.students.add');
    Route::post('webinars/{webinar}/students/create', [WebinarController::class, 'createStudent'])->name('webinars.students.create');
    Route::put('webinars/{webinar}/students/update', [WebinarController::class, 'updateStudent'])->name('webinars.students.update');
    Route::delete('webinars/{webinar}/students/remove', [WebinarController::class, 'removeStudent'])->name('webinars.students.remove');

    // Analytics routes
    Route::get('webinars/{webinar}/analytics', [WebinarAnalyticsController::class, 'show'])->name('webinars.analytics');
    Route::get('webinars/{webinar}/analytics/data', [WebinarAnalyticsController::class, 'getAnalyticsData'])->name('webinars.analytics.data');

    // Hash ID routes
    Route::post('/webinars/regenerate-hash-id', [WebinarController::class, 'regenerateHashId'])->name('webinars.regenerate-hash-id');
    Route::post('/webinars/check-hash-id', [WebinarController::class, 'checkHashIdExists'])->name('webinars.check-hash-id');

    // Participant routes
    Route::get('/webinars/{webinar}/participants', [ParticipantController::class, 'index'])->name('participants.index');
    Route::get('/webinars/{webinar}/participants/export', [ParticipantController::class, 'export'])->name('participants.export');
    Route::delete('/webinars/{webinar}/participants/{participant}', [ParticipantController::class, 'destroy'])->name('participants.destroy');

    // Comment routes
    Route::get('/webinars/{webinar}/comments', [CommentController::class, 'index'])->name('comments.index');
    Route::get('/webinars/{webinar}/comments/by-schedule', [CommentController::class, 'bySchedule'])->name('comments.by_schedule');
    Route::delete('/webinars/{webinar}/comments/by-schedule/{comment}', [CommentController::class, 'destroyBySchedule'])->name('comments.by_schedule.destroy');
    Route::get('/webinars/{webinar}/comments/export', [CommentController::class, 'export'])->name('comments.export');
    Route::delete('/webinars/{webinar}/comments/{comment}', [CommentController::class, 'destroy'])->name('comments.destroy');
    Route::get('/comments/template', [CommentController::class, 'downloadTemplate'])->name('comments.template');

    // Product routes
    Route::get('/products/search', [ProductController::class, 'search'])->name('products.search');
    Route::resource('products', ProductController::class);

    // Order routes
    Route::resource('orders', OrderController::class);
    Route::get('/orders/{order}/details', [OrderController::class, 'getDetails'])->name('orders.details');
    Route::post('/orders/{order}/update-status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::post('/orders/{order}/update-care-status', [OrderController::class, 'updateCareStatus'])->name('orders.update-care-status');
    Route::post('/orders/{order}/add-care-history', [OrderController::class, 'addCareHistory'])->name('orders.add-care-history');
    Route::post('/orders/{order}/add-care-history-with-status', [OrderController::class, 'addCareHistoryWithStatus'])->name('orders.add-care-history-with-status');
    Route::get('/orders/{order}/care-history', [OrderController::class, 'getCareHistory'])->name('orders.care-history');
    Route::post('/orders/{order}/add-care-note', [OrderController::class, 'addCareNote'])->name('orders.add-care-note');
    Route::get('/orders/{order}/care-notes', [OrderController::class, 'getCareNotes'])->name('orders.care-notes');
    Route::post('/orders/{order}/assign', [OrderController::class, 'assignOrder'])->name('orders.assign');
    Route::post('/orders/{order}/quick-assign', [OrderController::class, 'quickAssignToTelesale'])->name('orders.quick-assign');
    Route::post('/orders/{order}/quick-update-care', [OrderController::class, 'quickUpdateCareStatus'])->name('orders.quick-update-care');
    Route::get('/orders/stats', [OrderController::class, 'getStats'])->name('orders.stats');
    Route::get('/orders/export', [OrderController::class, 'export'])->name('orders.export');

    // Telesales routes
    Route::prefix('telesales')->name('telesales.')->group(function () {
        Route::get('/dashboard', [OrderController::class, 'telesaleDashboard'])->name('dashboard');
        Route::get('/stats', [OrderController::class, 'getTelesaleStats'])->name('stats');
        Route::post('/auto-assign', [OrderController::class, 'autoAssignOrders'])->name('auto-assign');
    });

    // Advertisement routes
    Route::resource('advertisements', AdvertisementController::class);
});

// Admin Routes
Route::group(['prefix' => 'admin', 'middleware' => ['auth'], 'as' => 'admin.'], function () {
    Route::resource('users', UserController::class);
    Route::resource('roles', RoleController::class);

    // Module management routes
    Route::get('modules', [ModuleController::class, 'index'])->name('modules.index');
    Route::post('modules/{module}/toggle', [ModuleController::class, 'toggle'])->name('modules.toggle');
    Route::post('modules/{module}/config', [ModuleController::class, 'updateConfig'])->name('modules.config');
    Route::post('modules/update-order', [ModuleController::class, 'updateOrder'])->name('modules.update-order');
    Route::post('modules/install-defaults', [ModuleController::class, 'installDefaults'])->name('modules.install-defaults');
});

// Questions Routes (Old - replaced by Question Sets)
Route::group(['prefix' => 'questions'], function () {
    // Redirect old questions index to question-sets
    Route::get('/', function () {
        return redirect()->route('question-sets.index');
    })->name('questions.index');

    Route::get('/create', [QuestionController::class, 'create'])->name('questions.create');
    Route::post('/', [QuestionController::class, 'store'])->name('questions.store');
    Route::get('/{question}', [QuestionController::class, 'show'])->name('questions.show');
    Route::get('/{question}/edit', [QuestionController::class, 'edit'])->name('questions.edit');
    Route::put('/{question}', [QuestionController::class, 'update'])->name('questions.update');
    Route::delete('/{question}', [QuestionController::class, 'destroy'])->name('questions.destroy');

    // AJAX routes
    Route::post('/{question}/toggle-status', [QuestionController::class, 'toggleStatus'])->name('questions.toggle-status');
    Route::post('/toggle-all-status', [QuestionController::class, 'toggleAllStatus'])->name('questions.toggle-all-status');

    // Dashboard routes
    Route::get('/dashboard', [App\Http\Controllers\QuestionDashboardController::class, 'index'])->name('questions.dashboard');
    Route::get('/{question}/dashboard', [App\Http\Controllers\QuestionDashboardController::class, 'questionDetail'])->name('questions.dashboard.detail');
});

// Question Sets Routes
Route::group(['prefix' => 'question-sets'], function () {
    Route::get('/', [App\Http\Controllers\QuestionSetController::class, 'index'])->name('question-sets.index');
    Route::get('/create', [App\Http\Controllers\QuestionSetController::class, 'create'])->name('question-sets.create');
    Route::post('/', [App\Http\Controllers\QuestionSetController::class, 'store'])->name('question-sets.store');
    Route::get('/{questionSet}', [App\Http\Controllers\QuestionSetController::class, 'show'])->name('question-sets.show');
    Route::get('/{questionSet}/edit', [App\Http\Controllers\QuestionSetController::class, 'edit'])->name('question-sets.edit');
    Route::put('/{questionSet}', [App\Http\Controllers\QuestionSetController::class, 'update'])->name('question-sets.update');
    Route::delete('/{questionSet}', [App\Http\Controllers\QuestionSetController::class, 'destroy'])->name('question-sets.destroy');

    // AJAX routes
    Route::post('/{questionSet}/toggle-status', [App\Http\Controllers\QuestionSetController::class, 'toggleStatus'])->name('question-sets.toggle-status');
    Route::get('/{questionSet}/questions', [App\Http\Controllers\QuestionSetController::class, 'getQuestions'])->name('question-sets.questions');
});

// API Routes for real-time data
Route::group(['prefix' => 'api/questions'], function () {
    Route::get('/stats', [App\Http\Controllers\QuestionDashboardController::class, 'getRealTimeStats']);
    Route::get('/responses', [App\Http\Controllers\QuestionDashboardController::class, 'getRecentResponses']);
});

// Profile Routes
Route::group(['middleware' => 'auth', 'prefix' => 'profile'], function () {
    Route::get('/', [UserProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/', [UserProfileController::class, 'update'])->name('profile.update');
    Route::put('/password', [UserProfileController::class, 'updatePassword'])->name('profile.password.update');
});

// Public Webinar Join Routes

// Smart Link Redirect Route (must be before join routes)
Route::get('/s/{code}', [SmartLinkController::class, 'redirect'])->name('smart-link.redirect');

Route::get('/join/{code}', [JoinController::class, 'showJoinPage'])->name('join.show');
Route::post('join/{code}/login-learning', [JoinController::class, 'loginLearning'])->name('join.login.learning');
Route::get('/join/{code}/register', [JoinController::class, 'showRegistrationForm'])->name('join.show-form');
Route::post('/join/{code}/form', [JoinController::class, 'processJoinForm'])->name('join.form');
Route::get('/join/{code}/enter', [JoinController::class, 'enterWebinar'])->name('join.enter');
Route::get('/join/{code}/get-live', [JoinController::class, 'getLive'])->name('join.get.live');
Route::post('/join/{code}/comment', [JoinController::class, 'submitComment'])->name('join.comment');
Route::get('/join/{code}/comments', [JoinController::class, 'getComments'])->name('join.comments');
Route::post('/join/{code}/update-duration', [JoinController::class, 'updateViewDuration'])->name('join.update-duration');
Route::post('/join/{code}/question/{question_id}/responses', [JoinController::class, 'submitQuestionResponse'])->name('join.question.responses');
//Checkout Payment
Route::post('/checkout', [CheckoutController::class, 'checkoutHandle'])->name('checkout.payment');
Route::get('/check-payment', [CheckoutController::class, 'checkPayment'])->name('check.payment');
// Settings routes
Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
Route::post('/settings', [SettingController::class, 'update'])->name('settings.update');
Route::post('/settings/appearance', [SettingController::class, 'updateAppearance'])->name('settings.update.appearance');
Route::post('/settings/test-appearance', [SettingController::class, 'testAppearanceUpload'])->name('settings.test.appearance');
Route::post('/settings/{key}/delete-file', [SettingController::class, 'deleteFile'])->name('settings.delete-file');
Route::post('/settings/test-telegram', [SettingController::class, 'testTelegramConnection'])->name('settings.test-telegram');
Route::post('/settings/test-recaptcha', [SettingController::class, 'testRecaptcha'])->name('settings.test-recaptcha');
Route::post('/settings/regenerate-api-key', [SettingController::class, 'regenerateApiKey'])->name('settings.regenerate-api-key');

// Telegram Setup Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/settings/telegram-setup', [SettingController::class, 'showTelegramSetup'])->name('settings.telegram-setup');

    // API routes for the Telegram setup
    Route::prefix('api/settings/telegram')->group(function () {
        Route::post('/verify-token', [TelegramSetupController::class, 'verifyToken']);
        Route::get('/get-chat-ids', [TelegramSetupController::class, 'getChatIds']);
        Route::post('/test-connection', [TelegramSetupController::class, 'testConnection']);
        Route::post('/save-chat-ids', [TelegramSetupController::class, 'saveChatIds']);
        Route::post('/test-send-to-chat', [TelegramSetupController::class, 'testSendToChatId']);
        Route::post('/save-notification-settings', [TelegramSetupController::class, 'saveNotificationSettings']);
    });
});
