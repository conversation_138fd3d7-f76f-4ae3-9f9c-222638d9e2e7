<?php

namespace App\Helpers;

class FileSizeHelper
{
    /**
     * Format file size in bytes to human readable format.
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    public function format($bytes, $precision = 1)
    {
        if ($bytes >= 1024 * 1024 * 1024) {
            return number_format($bytes / (1024 * 1024 * 1024), $precision) . ' GB';
        } elseif ($bytes >= 1024 * 1024) {
            return number_format($bytes / (1024 * 1024), $precision) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, $precision) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
