<?php

namespace App\Services;

class TelegramApiService
{
    /**
     * Get the base URL for Telegram Bot API
     *
     * @return string
     */
    public static function getBaseUrl(): string
    {
        return config('telegram.api_base_url', 'https://api.telegram.org');
    }

    /**
     * Get the full API URL for a specific bot method
     *
     * @param string $botToken
     * @param string $method
     * @return string
     */
    public static function getBotMethodUrl(string $botToken, string $method): string
    {
        $baseUrl = self::getBaseUrl();
        return "{$baseUrl}/bot{$botToken}/{$method}";
    }

    /**
     * Get the URL for getMe method
     *
     * @param string $botToken
     * @return string
     */
    public static function getGetMeUrl(string $botToken): string
    {
        return self::getBotMethodUrl($botToken, 'getMe');
    }

    /**
     * Get the URL for getUpdates method
     *
     * @param string $botToken
     * @return string
     */
    public static function getGetUpdatesUrl(string $botToken): string
    {
        return self::getBotMethodUrl($botToken, 'getUpdates');
    }

    /**
     * Get the URL for sendMessage method
     *
     * @param string $botToken
     * @return string
     */
    public static function getSendMessageUrl(string $botToken): string
    {
        return self::getBotMethodUrl($botToken, 'sendMessage');
    }

    /**
     * Get the default parse mode for messages
     *
     * @return string
     */
    public static function getDefaultParseMode(): string
    {
        return config('telegram.default_parse_mode', 'Markdown');
    }

    /**
     * Get the API timeout setting
     *
     * @return int
     */
    public static function getApiTimeout(): int
    {
        return config('telegram.api_timeout', 30);
    }

    /**
     * Get the maximum updates limit
     *
     * @return int
     */
    public static function getMaxUpdatesLimit(): int
    {
        return config('telegram.max_updates_limit', 100);
    }
} 