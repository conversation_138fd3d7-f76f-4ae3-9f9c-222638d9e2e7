<?php

namespace App\Console\Commands;

use App\Models\Webinar;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class DisableLivestreamCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:disable-livestream';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tắt livestream khi không có cache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $webinars = Webinar::where("livestreams->is_live", 1)->get();
        foreach ($webinars as $webinar) {
            $time = Carbon::parse($webinar->livestreams["time_last_online"]);
            if (Carbon::now()->subMinutes(5) > $time) {
                $livestreams = $webinar->livestreams;
                $livestreams["is_live"] = 0;
                $webinar->update(["livestreams" => $livestreams]);
                $this->info("Đã tắt livestream cho webinar $webinar->id");
            }
        }
    }
}
