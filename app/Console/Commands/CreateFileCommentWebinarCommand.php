<?php

namespace App\Console\Commands;

use App\Models\FileComment;
use App\Models\Webinar;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CreateFileCommentWebinarCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-file-comment-webinar';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sau khi kết thúc buổi webinar thì sẽ tạo file chứa các comment và xóa comment ở bảng đi';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $webinars = Webinar::has("comments")->get();
        $now = Carbon::now();
        foreach ($webinars as $webinar) {
            $schedules = $webinar->schedules;
            $video_duration_minutes = $webinar->video_duration_minutes;
//            L<PERSON>y ra thời gian gần nhất
            $isLive = false;
            foreach ($schedules as $item) {
                if (empty($item['date']) || empty($item['time'])) {
                    continue;
                }

                try {
                    $time = Carbon::parse($item['date'] . ' ' . $item['time']);
                } catch (\Exception $e) {
                    continue; // hoặc log lỗi nếu cần
                }

                if ($time->copy()->addMinutes($video_duration_minutes) > $now) {
                    $isLive = true;
                    break;
                }
            }
            if (!$isLive) {
                $comments = $webinar->comments;
                $path = "webinar_comments/$webinar->id/" . $now->copy()->format("d-m-Y-H-i") . ".txt";
                $file = Storage::disk("public")->put($path, $comments);
                if ($file) {
                    $this->info("Created File Comment: " . Storage::url($path));
                    FileComment::create([
                        "webinar_id" => $webinar->id,
                        "date" => $now,
                        "file" => $path
                    ]);
                    $webinar->comments()->delete();

                } else {
                    $this->info("Not create File $path !!!");
                }
            }
        }
        exit(true);
    }

}
