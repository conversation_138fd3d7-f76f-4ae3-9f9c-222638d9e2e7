<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Webinar;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use AWS\S3\S3Client;
use AWS\Exception\AwsException;

class CleanupDeletedWebinars extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webinar:cleanup-deleted
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--force : Force deletion without confirmation}
                            {--days=7 : Number of days to wait before permanent deletion}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up deleted webinars by removing S3 files and permanently deleting records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $days = (int) $this->option('days');

        $this->info("🧹 Starting cleanup of deleted webinars...");
        $this->info("📅 Looking for webinars deleted more than {$days} days ago");

        if ($dryRun) {
            $this->warn("🔍 DRY RUN MODE - No actual deletions will be performed");
        }

        // Get soft deleted webinars older than specified days
        $deletedWebinars = Webinar::onlyTrashed()
            ->where('deleted_at', '<=', now()->subDays($days))
            ->get();

        if ($deletedWebinars->isEmpty()) {
            $this->info("✅ No deleted webinars found for cleanup.");
            return 0;
        }

        $this->info("📊 Found {$deletedWebinars->count()} deleted webinars to process");

        // Show summary table
        $this->table(
            ['ID', 'Title', 'Deleted At', 'Video Path', 'S3 URL'],
            $deletedWebinars->map(function ($webinar) {
                return [
                    $webinar->id,
                    \Str::limit($webinar->title, 30),
                    $webinar->deleted_at->format('Y-m-d H:i:s'),
                    $webinar->video_path ? \Str::limit($webinar->video_path, 30) : 'No video',
                    $webinar->s3_url ? 'Yes' : 'No'
                ];
            })->toArray()
        );

        if (!$force && !$dryRun) {
            if (!$this->confirm('Do you want to proceed with cleanup?')) {
                $this->info("❌ Cleanup cancelled.");
                return 0;
            }
        }

        $successCount = 0;
        $errorCount = 0;

        foreach ($deletedWebinars as $webinar) {
            try {
                $this->info("🔄 Processing webinar: {$webinar->title} (ID: {$webinar->id})");

                if (!$dryRun) {
                    // Delete S3 files if they exist
                    $this->deleteS3Files($webinar);

                    // Delete local files if they exist
                    $this->deleteLocalFiles($webinar);

                    // Permanently delete the webinar record
                    $webinar->forceDelete();
                }

                $this->info("✅ Successfully processed webinar: {$webinar->title}");
                $successCount++;

            } catch (\Exception $e) {
                $this->error("❌ Error processing webinar {$webinar->title}: " . $e->getMessage());
                Log::error("Webinar cleanup error", [
                    'webinar_id' => $webinar->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $errorCount++;
            }
        }

        $this->info("\n📈 Cleanup Summary:");
        $this->info("✅ Successfully processed: {$successCount}");
        if ($errorCount > 0) {
            $this->error("❌ Errors encountered: {$errorCount}");
        }

        if ($dryRun) {
            $this->warn("🔍 This was a dry run. No actual deletions were performed.");
            $this->info("💡 Run without --dry-run to perform actual cleanup.");
        }

        return 0;
    }

    /**
     * Delete S3 files for a webinar.
     *
     * @param Webinar $webinar
     * @return void
     */
    private function deleteS3Files(Webinar $webinar)
    {
        if (!$webinar->s3_url && !str_starts_with($webinar->video_path ?? '', 's3://')) {
            return;
        }

        try {
            // Extract S3 key from URL or path
            $s3Key = $this->extractS3Key($webinar);

            if (!$s3Key) {
                $this->warn("⚠️  Could not extract S3 key for webinar {$webinar->id}");
                return;
            }

            $this->info("🗑️  Deleting S3 file: {$s3Key}");

            // Use Laravel's S3 disk to delete
            if (Storage::disk('vns3')->exists($s3Key)) {
                Storage::disk('vns3')->delete($s3Key);
                $this->info("✅ S3 file deleted successfully");
            } else {
                $this->warn("⚠️  S3 file not found: {$s3Key}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error deleting S3 file: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete local files for a webinar.
     *
     * @param Webinar $webinar
     * @return void
     */
    private function deleteLocalFiles(Webinar $webinar)
    {
        if (!$webinar->video_path || str_starts_with($webinar->video_path, 's3://')) {
            return;
        }

        try {
            $localPath = str_replace('public/', '', $webinar->video_path);

            if (Storage::disk('public')->exists($localPath)) {
                $this->info("🗑️  Deleting local file: {$localPath}");
                Storage::disk('public')->delete($localPath);
                $this->info("✅ Local file deleted successfully");
            } else {
                $this->warn("⚠️  Local file not found: {$localPath}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error deleting local file: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract S3 key from webinar S3 URL or path.
     *
     * @param Webinar $webinar
     * @return string|null
     */
    private function extractS3Key(Webinar $webinar)
    {
        // Try to get from s3_url first
        if ($webinar->s3_url) {
            // Parse URL to get the key
            $parsedUrl = parse_url($webinar->s3_url);
            if (isset($parsedUrl['path'])) {
                // Remove leading slash and bucket name if present
                $path = ltrim($parsedUrl['path'], '/');
                $bucket = config('filesystems.disks.vns3.bucket');

                if (str_starts_with($path, $bucket . '/')) {
                    return substr($path, strlen($bucket) + 1);
                }

                return $path;
            }
        }

        // Try to get from video_path if it's an S3 path
        if ($webinar->video_path && str_starts_with($webinar->video_path, 's3://')) {
            return str_replace('s3://', '', $webinar->video_path);
        }

        return null;
    }
}
