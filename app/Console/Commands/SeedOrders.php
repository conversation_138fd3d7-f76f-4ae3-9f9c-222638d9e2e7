<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\OrderSeeder;

class SeedOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:orders {count=200000 : Number of orders to generate} {batch=1000 : Batch size for insert operations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed a large number of orders for load testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = (int)$this->argument('count');
        $batchSize = (int)$this->argument('batch');

        // Add some validation to prevent accidental overload
        if ($count > 1000000) {
            if (!$this->confirm("You're attempting to generate over 1 million orders. Are you sure you want to continue?")) {
                $this->info('Command aborted.');
                return 0;
            }
        }

        $this->info("Starting to generate {$count} orders with batch size of {$batchSize}");

        // Set memory limit higher to handle large data sets
        ini_set('memory_limit', '1G');

        // Before running the seeder, check if we have required data
        $this->checkPrerequisites();

        // Start timer
        $startTime = microtime(true);

        // Call the seeder
        (new OrderSeeder($count, $batchSize))->run();

        // Calculate execution time
        $executionTime = microtime(true) - $startTime;
        $minutes = floor($executionTime / 60);
        $seconds = $executionTime % 60;

        $this->info("Orders seeded successfully in {$minutes} minutes and {$seconds} seconds");

        return 0;
    }

    /**
     * Check if all prerequisites are met
     */
    protected function checkPrerequisites()
    {
        $this->info('Checking prerequisites...');

        // Check if we have telesale users
        $telesaleCount = \App\Models\User::role('telesales')->count();
        if ($telesaleCount == 0) {
            $this->warn('No telesale users found. Orders will be assigned to user ID 1 by default.');
        } else {
            $this->info("Found {$telesaleCount} telesale users for order assignment.");
        }

        // Check if we have webinars
        $webinarCount = \App\Models\Webinar::count();
        if ($webinarCount == 0) {
            $this->warn('No webinars found. A test webinar will be created automatically.');
        } else {
            $this->info("Found {$webinarCount} webinars for order creation.");
        }

        $this->newLine();
    }
}
