<?php

namespace App\Jobs;

use App\Models\FileComment;
use App\Models\Webinar;
use App\Models\WebinarComment;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Storage;

class saveCommentLivestreamWebninarJob implements ShouldQueue
{
    use Queueable;

    protected $webinar;

    /**
     * Create a new job instance.
     * @param $webinar
     */
    public function __construct(Webinar $webinar)
    {
        $this->webinar = $webinar;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $now = Carbon::now();
        $comments = $this->webinar->comments();
        if ($comments->clone()->count()) {
            $path = "webinar_comments/" . $this->webinar->id . "/" . $now->copy()->format("d-m-Y-H-i") . ".txt";
            $file = Storage::disk("public")->put($path, $comments->clone()->get());
            if ($file) {
                FileComment::create([
                    "webinar_id" => $this->webinar->id,
                    "date" => $now,
                    "file" => $path
                ]);
                $comments->delete();
            }
        }

    }
}
