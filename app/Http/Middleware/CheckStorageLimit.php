<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Setting;
use App\Models\Webinar;
use Illuminate\Support\Facades\DB;

class CheckStorageLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if storage limit checking is enabled
        $limitEnabled = Setting::get('storage_limit_enabled', '1') === '1';

        if (!$limitEnabled) {
            return $next($request);
        }

        // Allow access to home page and settings
        $allowedRoutes = [
            'home',
            'settings.index',
            'settings.update',
            'logout'
        ];

        if (in_array($request->route()->getName(), $allowedRoutes)) {
            return $next($request);
        }

        // Check if storage limits are exceeded
        if ($this->isStorageLimitExceeded()) {
            // If it's an AJAX request, return JSON response
            if ($request->ajax()) {
                return response()->json([
                    'error' => true,
                    'message' => Setting::get('storage_limit_message', 'Hệ thống đã vượt quá giới hạn dung lượng cho phép.')
                ], 403);
            }

            // Redirect to home with error message
            return redirect()->route('home')->with('storage_limit_exceeded', true);
        }

        return $next($request);
    }

    /**
     * Check if storage limit is exceeded.
     *
     * @return bool
     */
    private function isStorageLimitExceeded()
    {
        try {
            // Get limits from settings (in GB)
            $videoLimitGB = (float) Setting::get('video_storage_limit_gb', 10);
            $systemLimitGB = (float) Setting::get('system_storage_limit_gb', 5);

            // Calculate current usage
            $currentVideoStorage = Webinar::whereNotNull('video_file_size')->sum('video_file_size') ?? 0;
            $currentSystemStorage = $this->calculateWebsiteStorage();

            // Convert to GB
            $currentVideoGB = $currentVideoStorage / (1024 * 1024 * 1024);
            $currentSystemGB = $currentSystemStorage / (1024 * 1024 * 1024);

            // Check if either limit is exceeded
            return ($currentVideoGB > $videoLimitGB) || ($currentSystemGB > $systemLimitGB);
        } catch (\Exception $e) {
            // If calculation fails, don't block access
            return false;
        }
    }

    /**
     * Calculate total website storage (code + database).
     *
     * @return int
     */
    private function calculateWebsiteStorage()
    {
        try {
            $totalSize = 0;

            // Calculate code size (excluding storage and vendor folders)
            $basePath = base_path();
            $excludePaths = [
                'storage/app/public',
                'storage/logs',
                'vendor',
                'node_modules',
                '.git'
            ];

            $totalSize += $this->getDirectorySize($basePath, $excludePaths);

            // Calculate database size
            $databaseSize = $this->getDatabaseSize();
            $totalSize += $databaseSize;

            return $totalSize;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get directory size excluding specified paths.
     *
     * @param string $directory
     * @param array $excludePaths
     * @return int
     */
    private function getDirectorySize($directory, $excludePaths = [])
    {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $filePath = $file->getPathname();
                    $relativePath = str_replace($directory . DIRECTORY_SEPARATOR, '', $filePath);

                    // Check if file is in excluded paths
                    $isExcluded = false;
                    foreach ($excludePaths as $excludePath) {
                        if (strpos($relativePath, $excludePath) === 0) {
                            $isExcluded = true;
                            break;
                        }
                    }

                    if (!$isExcluded) {
                        $size += $file->getSize();
                    }
                }
            }
        } catch (\Exception $e) {
            return 0;
        }

        return $size;
    }

    /**
     * Get database size.
     *
     * @return int
     */
    private function getDatabaseSize()
    {
        try {
            $databaseName = config('database.connections.mysql.database');

            $result = DB::select("
                SELECT
                    SUM(data_length + index_length) as size
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [$databaseName]);

            return $result[0]->size ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
