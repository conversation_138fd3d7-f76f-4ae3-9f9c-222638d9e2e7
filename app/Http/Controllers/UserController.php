<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use RealRashid\SweetAlert\Facades\Alert;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasAnyRole(['admin', 'administrator'])) {
                abort(403, 'Bạn không có quyền truy cập chức năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the users.
     */
    public function index()
    {
        if (!auth()->user()->can('users index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        if (auth()->user()->hasRole('administrator')) {
            $users = User::with('roles')->paginate(10);
        } else {
            $excludedRoles = ['administrator'];
            $users = User::whereDoesntHave('roles', function ($query) use ($excludedRoles) {
                $query->whereIn('name', $excludedRoles);
            })->with('roles')->paginate(10);
        }
        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        if (!auth()->user()->can('users create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        if (auth()->user()->hasRole('administrator')) {
            $roles = Role::all();
        } else {
            // User chỉ được tạo tài khoản với role 'staff'
            $roles = Role::whereNotIn('name', ['administrator', 'admin'])->get();
        }
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('users create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        if (!auth()->user()->hasRole('administrator')) {
            if (in_array($request->input("role"), ["admin", "administrator"])) {
                Alert::error('Lỗi', 'Bạn không có quyền tạo user có quyền là administrator và admin!');
                return redirect()->back()->withInput();
            }

        }
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|exists:roles,name',
        ]);

        $data = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'created_by' => auth()->id(), // Lưu ID người tạo
            'webinar_limit' => 0,
        ];
        if (auth()->user()->hasRole('administrator')) {
            $data["webinar_limit"] = (int)$request->input("webinar_limit");
        }
        $user = User::create($data);

        $user->assignRole($validated['role']);

        Alert::success('Thành công', 'Tạo người dùng thành công!');
        return redirect()->route('admin.users.index');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        if (!auth()->user()->can('users show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     * @param User $user
     * @return \Illuminate\Container\Container|mixed|object
     */
    public function edit(User $user)
    {

        if (!auth()->user()->can('users update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        if (auth()->user()->hasRole('administrator')) {
            $roles = Role::all();
        } else {
            // User chỉ được tạo tài khoản với role 'staff'
            $roles = Role::whereNotIn('name', ['administrator', 'admin'])->get();
        }

        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified user in storage.
     * @param Request $request
     * @param User $user
     * @return mixed
     */
    public function update(Request $request, User $user)
    {


        if (!auth()->user()->can('users update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        // Kiểm tra quyền chỉnh sửa người dùng
        if (!auth()->user()->hasRole('administrator')) {
            if (!auth()->user()->hasRole('admin')) {
                abort(403, 'Bạn không có quyền chỉnh sửa thông tin người dùng này.');
            }
            if (in_array($request->input("role"), ["admin", "administrator"])) {
                Alert::error('Lỗi', 'Bạn không có quyền chỉ định là administrator và admin!');
                return redirect()->back()->withInput();
            }

        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'role' => 'required|exists:roles,name'
        ]);

        $data = [
            'name' => $validated['name'],
            'email' => $validated['email']
        ];
        if (auth()->user()->hasRole('administrator')) {
            $data["webinar_limit"] = (int)$request->input("webinar_limit");
        }

        $user->update($data);

        // Update password if provided
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8|confirmed',
            ]);
            $user->update(['password' => Hash::make($request->password)]);
        }

        // Sync roles
        $user->syncRoles([$validated['role']]);

        Alert::success('Thành công', 'Cập nhật người dùng thành công!');
        return redirect()->route('admin.users.index');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Kiểm tra quyền xóa người dùng

        if (!auth()->user()->can('users delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        // Prevent deleting the authenticated user
        if ($user->id === auth()->id()) {
            Alert::error('Lỗi', 'Bạn không thể xóa tài khoản của chính mình.');
            return redirect()->route('admin.users.index');
        }

        $user->delete();

        Alert::success('Thành công', 'Xóa người dùng thành công!');
        return redirect()->route('admin.users.index');
    }
}
