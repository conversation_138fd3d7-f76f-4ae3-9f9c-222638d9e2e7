<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ProductController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->ensureStorageDirectoriesExist();
    }

    /**
     * Ensure storage directories for product images exist
     */
    protected function ensureStorageDirectoriesExist()
    {
        try {
            $directory = storage_path('app/public/products');

            if (!file_exists($directory)) {
                \Log::info("Creating products directory in public storage");
                if (!mkdir($directory, 0755, true)) {
                    \Log::error("Failed to create products directory");
                }
            }

            // Double-check the directory exists
            if (!file_exists($directory)) {
                \Log::error("Products directory still doesn't exist after creation attempt", [
                    'directory' => $directory
                ]);
            }

            // Check write permissions
            if (!is_writable($directory)) {
                \Log::error("Products directory is not writable", [
                    'directory' => $directory,
                    'permissions' => substr(sprintf('%o', fileperms($directory)), -4)
                ]);

                // Try to fix permissions if possible
                chmod($directory, 0755);
            }
        } catch (\Exception $e) {
            \Log::error("Error ensuring storage directories exist: " . $e->getMessage());
        }
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (auth()->user()->can('product index')) {
            $products = Product::paginate(10);
        } elseif (auth()->user()->can('product view only you')) {
            $products = Product::where('user_id', auth()->id())->paginate(10);
        } else {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('products.index', compact('products'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->can('product create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('products.create');
    }

    /**
     * Handle file upload using a $_FILES global array approach to bypass path issues
     */
    protected function handleImageUpload($file)
    {
        try {

            // Create a unique filename
            $extension = $file->getClientOriginalExtension() ?: 'jpg';
            $filename = Str::random(20) . '.' . $extension;
            $path = 'products/' . $filename;

            // Method 1: Try to access the global $_FILES array directly
            $globalFiles = $_FILES['image'] ?? null;
            if ($globalFiles && isset($globalFiles['tmp_name']) && file_exists($globalFiles['tmp_name'])) {

                $contents = file_get_contents($globalFiles['tmp_name']);
                if ($contents && Storage::disk('public')->put($path, $contents)) {
                    return $path;
                }
            }

            // Method 2: Try using a new approach with manual file opening
            $tmpName = $file->getPathname();
            if ($tmpName && file_exists($tmpName)) {

                $contents = file_get_contents($tmpName);
                if ($contents && Storage::disk('public')->put($path, $contents)) {
                    return $path;
                }
            }

            // Method 3: Use Laravel's built-in methods as a fallback

            $result = $file->storeAs('products', $filename, 'public');
            if (!empty($result)) {
                return $result;
            }

            // Method 4: Last resort - try to manually move the uploaded file
            if (is_uploaded_file($file->getPathname())) {

                $uploadPath = storage_path('app/public/' . $path);

                // Ensure directory exists
                $dir = dirname($uploadPath);
                if (!file_exists($dir)) {
                    mkdir($dir, 0755, true);
                }

                if (move_uploaded_file($file->getPathname(), $uploadPath)) {
                    return $path;
                }
            }

            throw new \Exception("Failed to save file after trying multiple methods");
        } catch (\Exception $e) {
            \Log::error("Error in image upload: " . $e->getMessage(), [
                'exception' => $e,
                'file_info' => [
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime' => $file->getMimeType(),
                    'error' => $file->getError()
                ]
            ]);
            throw $e;
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('product create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'original_price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'image' => 'nullable|image|max:2048',
            'image_url' => 'nullable|url',
            'stock_quantity' => 'required|integer|min:0',
            'tags' => 'nullable|array',
            'tags.*' => 'string',
        ]);

        // Handle the image upload or URL
        $imagePath = null;

        try {
            if ($request->hasFile('image')) {
                $file = $request->file('image');



                // Check that the file is valid
                if (!$file->isValid()) {
                    throw new \Exception('Invalid file upload: ' . $file->getErrorMessage());
                }

                if ($file->getSize() <= 0) {
                    throw new \Exception('Upload failed: File is empty');
                }

                // Use our direct file handling method
                $imagePath = $this->handleImageUpload($file);

                // Check if path is empty after storage attempt
                if (empty($imagePath)) {
                    throw new \Exception('Failed to store image: Path is empty');
                }

            } elseif ($request->filled('image_url')) {
                $imagePath = $request->input('image_url');
            }
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Image upload failed (store): ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['image' => 'Unable to upload image: ' . $e->getMessage()]);
        }

        $product = new Product([
            'name' => $validated['name'],
            'original_price' => $validated['original_price'],
            'sale_price' => $validated['sale_price'] ?? null,
            'image' => $imagePath,
            'stock_quantity' => $validated['stock_quantity'],
            'tags' => $validated['tags'] ?? [],
            'user_id' => auth()->id(),
        ]);

        $product->save();

        return redirect()->route('products.index')
            ->with('success', 'Sản phẩm đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        if (!auth()->user()->can('product show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('view', $product);
        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        if (!auth()->user()->can('product update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $product);
        return view('products.edit', compact('product'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        if (!auth()->user()->can('product update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $product);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'original_price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'image' => 'nullable|image|max:2048',
            'image_url' => 'nullable|url',
            'stock_quantity' => 'required|integer|min:0',
            'tags' => 'nullable|array',
            'tags.*' => 'string',
            'remove_image' => 'nullable|boolean',
        ]);

        // Handle the image upload or URL
        $imagePath = $product->image;

        try {
            if ($request->filled('remove_image') && $request->boolean('remove_image')) {
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
                    Storage::disk('public')->delete($imagePath);
                }
                $imagePath = null;
            } elseif ($request->hasFile('image')) {
                $file = $request->file('image');



                // Check that the file is valid
                if (!$file->isValid()) {
                    throw new \Exception('Invalid file upload: ' . $file->getErrorMessage());
                }

                if ($file->getSize() <= 0) {
                    throw new \Exception('Upload failed: File is empty');
                }

                // Delete old image if it exists and is not a URL
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
                    Storage::disk('public')->delete($imagePath);
                }

                // Use our direct file handling method
                $imagePath = $this->handleImageUpload($file);

                // Check if path is empty after storage attempt
                if (empty($imagePath)) {
                    throw new \Exception('Failed to store image: Path is empty');
                }

            } elseif ($request->filled('image_url')) {
                // Delete old image if it exists and is not a URL
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
                    Storage::disk('public')->delete($imagePath);
                }
                $imagePath = $request->input('image_url');
            }
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Image upload failed (update): ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['image' => 'Unable to upload image: ' . $e->getMessage()]);
        }

        $product->update([
            'name' => $validated['name'],
            'original_price' => $validated['original_price'],
            'sale_price' => $validated['sale_price'] ?? null,
            'image' => $imagePath,
            'stock_quantity' => $validated['stock_quantity'],
            'tags' => $validated['tags'] ?? [],
        ]);

        return redirect()->route('products.index')
            ->with('success', 'Sản phẩm đã được cập nhật thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        if (!auth()->user()->can('product delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('delete', $product);

        // Delete the product image if it's stored locally
        if ($product->image && !filter_var($product->image, FILTER_VALIDATE_URL)) {
            Storage::disk('public')->delete($product->image);
        }

        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'Sản phẩm đã được xóa thành công.');
    }

    /**
     * Search for products (for select2 ajax).
     * @param Request $request
     * @return mixed
     */
    public function search(Request $request)
    {
        if (!auth()->user()->can('product index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $search = $request->get('q');
        $products = auth()->user()->products()
            ->where('name', 'like', "%{$search}%")
            ->select('id', 'name', 'image', 'original_price', 'sale_price', 'stock_quantity')
            ->take(10)
            ->get();
        return response()->json([
            'results' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'text' => $product->name,
                    'image' => $product->image,
                    'price' => $product->sale_price ?? $product->original_price,
                    'stock' => $product->stock_quantity,
                ];
            })
        ]);
    }
}
