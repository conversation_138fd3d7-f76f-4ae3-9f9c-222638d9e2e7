<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Webinar;
use App\Models\User;
use App\Models\CustomerCareHistory;
use App\Models\CustomerCareNote;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');

        $this->middleware(function ($request, $next) {
            // Share telesale reminders count with all views
            if (auth()->check() && auth()->user()->hasRole('telesales')) {
                $userId = auth()->id();
                $remindersCount = Order::assignedTo($userId)
                    ->where(function ($query) {
                        $query->followUpToday()
                            ->orWhere(function ($q) {
                                $q->overdueFollowUp();
                            });
                    })
                    ->count();

                view()->share('teleSaleReminders', $remindersCount);
            }

            return $next($request);
        });
    }

    /**
     * Display a listing of the orders.
     * @param Request $request
     * @return \Illuminate\Container\Container|mixed|object
     */
    public function index(Request $request)
    {

        if (!auth()->user()->can('order index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $query = Order::with(['webinar', 'assignedUser']);
        $filter = false;
        // Lọc theo webinar
        if ($request->has('webinar_id') && $request->webinar_id) {

            $filter = true;
            $query->fromWebinar($request->webinar_id);
        }

        // Lọc theo trạng thái chăm sóc
//        if ($request->has('care_status') && $request->care_status) {
//
//            $query->withCareStatus($request->care_status);
//        }
        // Lọc theo trạng thái chăm sóc
        if ($request->has('care_status_filter') && $request->care_status_filter) {

            $filter = true;
            $query->withCareStatus($request->care_status_filter);
        }

        // Lọc theo trạng thái thanh toán
        if ($request->has('payment_status') && $request->payment_status) {

            $filter = true;
            $query->where('payment_status', $request->payment_status);
        }

        // Lọc theo người được gán
        if ($request->has('assigned_to') && $request->assigned_to) {

            $filter = true;
            $query->assignedTo($request->assigned_to);
        }

        // Lọc theo mức ưu tiên
        if ($request->has('priority') && !empty($request->priority)) {

            $filter = true;
            $query->where('is_priority', $request->priority);
        }

        // Lọc theo loại theo dõi
        if ($request->has('follow_type') && $request->follow_type) {

            $filter = true;
            switch ($request->follow_type) {
                case 'today':
                    $query->whereDate('next_follow_date', now()->toDateString());
                    break;
                case 'tomorrow':
                    $query->whereDate('next_follow_date', now()->addDay()->toDateString());
                    break;
                case 'week':
                    $query->whereBetween('next_follow_date', [
                        now()->startOfWeek()->toDateString(),
                        now()->endOfWeek()->toDateString()
                    ]);
                    break;
                case 'overdue':
                    $query->whereDate('next_follow_date', '<', now()->toDateString());
                    break;
                case 'no_follow':
                    $query->whereNull('next_follow_date');
                    break;
            }
        }

        // Lọc theo ngày theo dõi cụ thể
        if ($request->has('follow_date') && $request->follow_date) {

            $filter = true;
            $query->whereDate('next_follow_date', $request->follow_date);
        }

        // Tìm kiếm theo tên, số điện thoại hoặc email
        if ($request->has('search') && $request->search) {

            $filter = true;
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%$search%")
                    ->orWhere('phone', 'like', "%$search%")
                    ->orWhere('email', 'like', "%$search%");
            });
        }

        // Sắp xếp kết quả với quy tắc ưu tiên
        if ($request->has('sort_by') && $request->sort_by) {

            // Sử dụng sắp xếp tùy chỉnh từ người dùng
            $direction = $request->has('sort_dir') && $request->sort_dir == 'asc' ? 'asc' : 'desc';
            $query->orderBy($request->sort_by, $direction);
        } else {
            // Chỉ áp dụng logic sắp xếp mặc định khi không chọn lọc theo care_status cụ thể
            if (!$request->has('care_status') || !empty($request->care_status)) {
                // Mặc định: Ưu tiên đơn mới và chưa xử lý lên trước, đơn đã chuyển đổi xuống cuối
                $query->orderByRaw("
                    CASE
                        WHEN care_status IS NULL THEN 1
                        WHEN care_status = 'new' THEN 2
                        WHEN care_status = 'contacted' THEN 3
                        WHEN care_status = 'interested' THEN 4
                        WHEN care_status = 'callback' THEN 5
                        WHEN care_status = 'not_interested' THEN 6
                        WHEN care_status = 'closed' THEN 7
                        WHEN care_status = 'converted' THEN 8
                        ELSE 9
                    END ASC
                ");
            }

            // Nếu cùng trạng thái, sắp xếp theo thời gian tạo mới nhất
            $query->latest();
        }

        // Thống kê tổng quan
        $stats = $this->getOrderStats($query, $filter);

        $orders = $query->paginate(15)->withQueryString();


        // Lấy danh sách webinar để hiển thị trong bộ lọc
        $webinars = Webinar::select('id', 'title')->orderBy('title')->get();

        // Lấy danh sách telesale (nếu có quyền admin)
        $teleSales = User::role('telesales')->orWhere('id', auth()->id())->get();

        return view('orders.index', compact(
            'orders', 'webinars', 'teleSales', 'stats'
        ));
    }

    /**
     * Show the form for creating a new order.
     */
    public function create()
    {

        if (!auth()->user()->can('order create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $webinars = Webinar::select('id', 'title')->get();
        return view('orders.create', compact('webinars'));
    }

    /**
     * Store a newly created order in storage.
     */
    public function store(Request $request)
    {

        if (!auth()->user()->can('order create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'product_name' => 'required|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'purchase_time' => 'required|date',
            'order_type' => 'required|in:form,qr',
            'transaction_id' => 'nullable|string|max:255',
            'payment_note' => 'nullable|string',
            'webinar_id' => 'nullable|exists:webinars,id',
            'payment_status' => 'required|in:pending,paid,failed',
        ]);

        // Đảm bảo price có giá trị mặc định là 0 nếu không được nhập
        if (empty($validated['price'])) {
            $validated['price'] = 0;
        }

        // Set initial care status and add to teleSale queue if requested
        if ($request->has('assign_to_telesale') && $request->assign_to_telesale) {
            $validated['care_status'] = 'new';

            // Option to assign to specific telesale or auto-assign
            if ($request->has('assigned_to') && $request->assigned_to) {
                $validated['assigned_to'] = $request->assigned_to;
            } else {
                // Auto-assign to a telesale agent with fewest current assignments
                $telesaleUser = $this->getTelesaleWithFewestAssignments();
                if ($telesaleUser) {
                    $validated['assigned_to'] = $telesaleUser->id;
                }
            }

            // Set next follow-up date to tomorrow
            $validated['next_follow_date'] = now()->addDay();
        }

        $order = Order::create($validated);

        // If the order was assigned to telesale, log the assignment
        if (isset($validated['assigned_to'])) {
            $this->logAssignment($validated['assigned_to'], 1);

            // Redirect to telesales view if requested
            if ($request->has('redirect_to_telesale') && $request->redirect_to_telesale) {
                return redirect()->route('telesales.dashboard')
                    ->with('success', 'Đơn hàng đã được tạo và gán cho nhân viên telesale.');
            }
        }

        return redirect()->route('orders.index')
            ->with('success', 'Order created successfully.');
    }

    /**
     * Get telesale user with the fewest current assignments.
     */
    private function getTelesaleWithFewestAssignments()
    {
        // Get all telesale users
        $telesaleUsers = User::role('telesales')->get();

        if ($telesaleUsers->isEmpty()) {
            return null;
        }

        $assignmentCounts = [];

        // Count current assignments for each telesale user
        foreach ($telesaleUsers as $user) {
            $count = Order::where('assigned_to', $user->id)
                ->where('care_status', '!=', 'closed')
                ->count();

            $assignmentCounts[$user->id] = $count;
        }

        // Find the user with the lowest count
        $minCount = min($assignmentCounts);
        $userId = array_search($minCount, $assignmentCounts);

        return $telesaleUsers->firstWhere('id', $userId);
    }

    /**
     * Quick assign to telesale from orders list.
     */
    public function quickAssignToTelesale(Request $request, Order $order)
    {

        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $request->validate([
            'telesale_id' => 'required|exists:users,id'
        ]);

        $order->care_status = 'new';
        $order->assigned_to = $request->telesale_id;
        $order->next_follow_date = now()->addDay();
        $order->save();

        $this->logAssignment($request->telesale_id, 1);

        return response()->json([
            'success' => true,
            'message' => 'Đơn hàng đã được gán cho nhân viên telesale'
        ]);
    }

    /**
     * Quick update care status from orders list.
     */
    public function quickUpdateCareStatus(Request $request, Order $order)
    {
        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $request->validate([
            'care_status' => 'required|in:new,contacted,interested,not_interested,callback,converted,closed'
        ]);

        $order->care_status = $request->care_status;

        // If status is callback, set next_follow_date if provided
        if ($request->care_status === 'callback' && $request->has('next_follow_date')) {
            $order->next_follow_date = $request->next_follow_date;
        }

        $order->save();

        return response()->json([
            'success' => true,
            'message' => 'Trạng thái chăm sóc đã được cập nhật'
        ]);
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {

        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified order.
     */
    public function edit(Order $order)
    {

        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $webinars = Webinar::select('id', 'title')->get();
        return view('orders.edit', compact('order', 'webinars'));
    }

    /**
     * Update the specified order in storage.
     */
    public function update(Request $request, Order $order)
    {

        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'product_name' => 'required|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'purchase_time' => 'required|date',
            'order_type' => 'required|in:form,qr',
            'transaction_id' => 'nullable|string|max:255',
            'payment_note' => 'nullable|string',
            'webinar_id' => 'nullable|exists:webinars,id',
            'payment_status' => 'required|in:pending,paid,failed',
        ]);

        // Đảm bảo price có giá trị mặc định là 0 nếu không được nhập
        if (empty($validated['price'])) {
            $validated['price'] = 0;
        }

        $order->update($validated);

        return redirect()->route('orders.index')
            ->with('success', 'Order updated successfully.');
    }

    /**
     * Remove the specified order from storage.
     * @param Order $order
     * @return mixed
     */
    public function destroy(Order $order)
    {
        if (!auth()->user()->can('order delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $order->delete();
        return redirect()->route('orders.index')
            ->with('success', 'Order deleted successfully.');
    }

    /**
     * Get order details for AJAX request.
     */
    public function getDetails(Order $order)
    {
        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $order->load(['webinar', 'assignedUser', 'careNotes' => function ($query) {
            $query->with('user')->orderBy('is_pinned', 'desc')->orderBy('created_at', 'desc');
        }, 'careHistory' => function ($query) {
            $query->with('user')->orderBy('created_at', 'desc');
        }]);

        // Thêm các trường văn bản cho các mã enum
        $order->care_status_text = $order->getCareStatusTextAttribute();
        $order->formatted_price = $order->getFormattedPriceAttribute();
        $order->time_until_follow_up = $order->getTimeUntilFollowUpAttribute();
        $order->needs_follow_up_today = $order->getNeedsFollowUpTodayAttribute();

        return response()->json($order);
    }

    /**
     * Update order status via AJAX.
     */
    public function updateStatus(Request $request, Order $order)
    {
        if (!auth()->user()->can('order update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $request->validate([
            'status' => 'required|in:pending,paid,failed'
        ]);

        $order->payment_status = $request->status;
        $order->save();

        return response()->json(['success' => true]);
    }

    /**
     * Update customer care status via AJAX.
     */
    public function updateCareStatus(Request $request, Order $order)
    {
        $request->validate([
            'care_status' => 'required|in:new,contacted,interested,not_interested,callback,converted,closed',
            'next_follow_date' => 'nullable|date'
        ]);

        $order->care_status = $request->care_status;

        if ($request->has('next_follow_date')) {
            $order->next_follow_date = $request->next_follow_date;
        }

        if ($request->has('assigned_to')) {
            $order->assigned_to = $request->assigned_to;
        }

        $order->save();

        return response()->json(['success' => true]);
    }

    /**
     * Add customer care history record via AJAX.
     * @param Request $request
     * @param Order $order
     * @return mixed
     */
    public function addCareHistory(Request $request, Order $order)
    {
        $request->validate([
            'contact_type' => 'required|in:phone,email,sms,zalo,other',
            'content' => 'required|string',
            'status' => 'required|in:success,no_answer,callback,not_interested,wrong_number,other',
            'callback_time' => 'nullable|date'
        ]);

        $history = new CustomerCareHistory();
        $history->order_id = $order->id;
        $history->user_id = auth()->id();
        $history->contact_type = $request->contact_type;
        $history->content = $request->content;
        $history->status = $request->status;

        if ($request->has('callback_time')) {
            $history->callback_time = $request->callback_time;

            // Cập nhật next_follow_date của đơn hàng
            $order->next_follow_date = $request->callback_time;
            $order->care_status = 'callback';
            $order->save();
        }

        $history->save();

        return response()->json([
            'success' => true,
            'history' => $history->load('user')
        ]);
    }

    /**
     * Get customer care history for an order.
     */
    public function getCareHistory(Order $order)
    {
        $history = $order->careHistory()->with('user')->latest()->get();

        return response()->json($history);
    }

    /**
     * Add customer care note via AJAX.
     */
    public function addCareNote(Request $request, Order $order)
    {
        $request->validate([
            'content' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'is_pinned' => 'boolean'
        ]);

        $note = new CustomerCareNote();
        $note->order_id = $order->id;
        $note->user_id = auth()->id();
        $note->content = $request->content;
        $note->priority = $request->priority;
        $note->is_pinned = $request->is_pinned ?? false;
        $note->save();

        return response()->json([
            'success' => true,
            'note' => $note->load('user')
        ]);
    }

    /**
     * Get customer care notes for an order.
     */
    public function getCareNotes(Order $order)
    {
        $notes = $order->careNotes()->with('user')->orderBy('is_pinned', 'desc')->latest()->get();

        return response()->json($notes);
    }

    /**
     * Assign order to a telesale employee.
     */
    public function assignOrder(Request $request, Order $order)
    {
        $request->validate([
            'assigned_to' => 'required|exists:users,id'
        ]);

        $order->assigned_to = $request->assigned_to;
        $order->save();

        return response()->json(['success' => true]);
    }

    /**
     * Get orders statistics.
     */
    public function getStats(Request $request)
    {
        $query = Order::query();

        // Lọc theo webinar nếu có
        if ($request->has('webinar_id') && $request->webinar_id) {
            $query->fromWebinar($request->webinar_id);
        }

        // Stats for filtered orders
        $stats = [
            'total' => $query->count(),
            'paid' => (clone $query)->where('payment_status', 'paid')->count(),
            'pending' => (clone $query)->where('payment_status', 'pending')->count(),
            'failed' => (clone $query)->where('payment_status', 'failed')->count(),

            // Thêm thống kê theo trạng thái chăm sóc
            'new' => (clone $query)->where('care_status', 'new')->count(),
            'contacted' => (clone $query)->where('care_status', 'contacted')->count(),
            'interested' => (clone $query)->where('care_status', 'interested')->count(),
            'not_interested' => (clone $query)->where('care_status', 'not_interested')->count(),
            'callback' => (clone $query)->where('care_status', 'callback')->count(),
            'converted' => (clone $query)->where('care_status', 'converted')->count(),
            'closed' => (clone $query)->where('care_status', 'closed')->count(),

            // Thêm thống kê đơn cần theo dõi hôm nay
            'follow_today' => (clone $query)->needFollowUp()->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Auto-assign unassigned orders to telesales agents.
     */
    public function autoAssignOrders(Request $request)
    {
        $request->validate([
            'webinar_id' => 'nullable|exists:webinars,id',
            'limit' => 'nullable|integer|min:1|max:100',
            'assigned_to' => 'required|exists:users,id'
        ]);
        $limit = $request->limit ?? 10;
        $userId = $request->assigned_to;
        $query = Order::unassigned()
            ->where(function ($q) {
                $q->where('care_status', 'new')
                    ->orWhereNull('care_status');
            });

        // Nếu có lọc theo webinar
        if ($request->has('webinar_id') && $request->webinar_id) {
            $query->fromWebinar($request->webinar_id);
        }

        // Ưu tiên đơn hàng đã thanh toán
        $query->orderByRaw("FIELD(payment_status, 'paid', 'pending', 'failed') ASC");

        // Lấy các đơn hàng cần gán
        $orders = $query->limit($limit)->get();
        $count = $orders->count();

        // Gán đơn hàng
        foreach ($orders as $order) {
            $order->assigned_to = $userId;
            $order->care_status = 'new';
            $order->next_follow_date = now()->addHours(24);
            $order->save();
        }

        // Lưu lịch sử phân công
        if ($count > 0) {
            $this->logAssignment($userId, $count);
        }

        return response()->json([
            'success' => true,
            'count' => $count,
            'message' => "Đã phân công $count đơn hàng cho nhân viên telesale"
        ]);
    }

    /**
     * Log the assignment of orders to a telesale agent.
     */
    private function logAssignment($userId, $count)
    {
        // Có thể sử dụng một bảng riêng để ghi lại việc phân công
        // Hoặc sử dụng hệ thống log của Laravel
        \Log::info("Auto-assigned $count orders to user ID: $userId");
    }

    /**
     * Get dashboard statistics for telesale agents.
     */
    public function getTelesaleStats()
    {
        $userId = auth()->id();

        $stats = [
            'total_assigned' => Order::assignedTo($userId)->count(),
            'follow_today' => Order::assignedTo($userId)->followUpToday()->count(),
            'overdue' => Order::assignedTo($userId)->overdueFollowUp()->count(),
            'contacted_today' => CustomerCareHistory::where('user_id', $userId)
                ->whereDate('created_at', now()->toDateString())
                ->count(),
            'success_rate' => $this->calculateSuccessRate($userId),
            'conversion_rate' => $this->calculateConversionRate($userId),
            'calls_this_week' => $this->getCallsCountThisWeek($userId),
            'performance_trend' => $this->getPerformanceTrend($userId),
        ];

        return response()->json($stats);
    }

    /**
     * Calculate conversion rate for telesale agent.
     */
    private function calculateConversionRate($userId)
    {
        $totalContacted = Order::assignedTo($userId)
            ->whereIn('care_status', ['contacted', 'interested', 'not_interested', 'callback', 'converted', 'closed'])
            ->count();

        if ($totalContacted == 0) {
            return 0;
        }

        $converted = Order::assignedTo($userId)
            ->where('care_status', 'converted')
            ->count();

        return round(($converted / $totalContacted) * 100, 1);
    }

    /**
     * Get call counts per day for the current week.
     */
    private function getCallsCountThisWeek($userId)
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        $results = [];

        for ($day = 0; $day < 7; $day++) {
            $date = $startOfWeek->copy()->addDays($day);
            $count = CustomerCareHistory::where('user_id', $userId)
                ->where('contact_type', 'phone')
                ->whereDate('created_at', $date)
                ->count();

            $results[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('D'),
                'count' => $count
            ];
        }

        return $results;
    }

    /**
     * Get performance trend over the last 30 days.
     */
    private function getPerformanceTrend($userId)
    {
        $thirtyDaysAgo = now()->subDays(30);

        $contactsPerDay = CustomerCareHistory::where('user_id', $userId)
            ->where('created_at', '>=', $thirtyDaysAgo)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        $successfulContactsPerDay = CustomerCareHistory::where('user_id', $userId)
            ->whereIn('status', ['success', 'callback'])
            ->where('created_at', '>=', $thirtyDaysAgo)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        $data = [];
        $current = $thirtyDaysAgo->copy();
        $today = now();

        while ($current->lte($today)) {
            $dateStr = $current->format('Y-m-d');
            $data[] = [
                'date' => $dateStr,
                'total' => $contactsPerDay[$dateStr] ?? 0,
                'successful' => $successfulContactsPerDay[$dateStr] ?? 0
            ];
            $current->addDay();
        }

        return $data;
    }

    /**
     * Calculate success rate for telesale.
     */
    private function calculateSuccessRate($userId)
    {
        $totalCalls = CustomerCareHistory::where('user_id', $userId)
            ->where('contact_type', 'phone')
            ->count();

        if ($totalCalls == 0) {
            return 0;
        }

        $successCalls = CustomerCareHistory::where('user_id', $userId)
            ->where('contact_type', 'phone')
            ->whereIn('status', ['success', 'callback'])
            ->count();

        return round(($successCalls / $totalCalls) * 100);
    }

    /**
     * Get order statistics for dashboard.
     */
    private function getOrderStats($query = null, $filter = false)
    {
        if ($query && $filter) {
            return [
                'total' => $query->count(),
                'paidCount' => (clone $query)->where('payment_status', 'paid')->count(),
                'pendingCount' => (clone $query)->where('payment_status', 'pending')->count(),
                'failedCount' => (clone $query)->where('payment_status', 'failed')->count(),

                // Thêm thống kê theo trạng thái chăm sóc
                'newCount' => (clone $query)->where('care_status', 'new')->count(),
                'contactedCount' => (clone $query)->where('care_status', 'contacted')->count(),
                'interestedCount' => (clone $query)->where('care_status', 'interested')->count(),
                'not_interestedCount' => (clone $query)->where('care_status', 'not_interested')->count(),
                'callbackCount' => (clone $query)->where('care_status', 'callback')->count(),
                'convertedCount' => (clone $query)->where('care_status', 'converted')->count(),
                'closedCount' => (clone $query)->where('care_status', 'closed')->count(),

                // Thêm thống kê đơn cần theo dõi hôm nay
                'followTodayCount' => (clone $query)->whereDate('next_follow_date', now()->toDateString())->count(),
                'total_money_converted' => (clone $query)->where('care_status', "converted")->sum("price")
            ];
        }

        return [
            'total' => Order::count(),
            'paidCount' => Order::where('payment_status', 'paid')->count(),
            'pendingCount' => Order::where('payment_status', 'pending')->count(),
            'failedCount' => Order::where('payment_status', 'failed')->count(),
            'newCount' => Order::where('care_status', 'new')->count(),
            'contactedCount' => Order::where('care_status', 'contacted')->count(),
            'interestedCount' => Order::where('care_status', 'interested')->count(),
            'not_interestedCount' => Order::where('care_status', 'not_interested')->count(),
            'callbackCount' => Order::where('care_status', 'callback')->count(),
            'convertedCount' => Order::where('care_status', 'converted')->count(),
            'closedCount' => Order::where('care_status', 'closed')->count(),
            'followTodayCount' => Order::whereDate('next_follow_date', now()->toDateString())->count(),
            'total_money_converted' => Order::where('care_status', "converted")->sum("price"),
        ];
    }

    /**
     * Show the telesale dashboard.
     */
    public function telesaleDashboard()
    {
        $userId = auth()->id();

        // Get stats for the dashboard
        $stats = [
            'total_assigned' => Order::assignedTo($userId)->count(),
            'follow_today' => Order::assignedTo($userId)->followUpToday()->count(),
            'contacted_today' => CustomerCareHistory::where('user_id', $userId)
                ->whereDate('created_at', now()->toDateString())
                ->count(),
            'overdue' => Order::assignedTo($userId)->overdueFollowUp()->count(),
            'success_rate' => $this->calculateSuccessRate($userId),
            'conversion_rate' => $this->calculateConversionRate($userId)
        ];

        // Get webinars for auto-assign form
        $webinars = Webinar::select('id', 'title')->orderBy('title')->get();

        // Get high-value prospects that are interested
        $highValueProspects = Order::assignedTo($userId)
            ->where('care_status', 'interested')
            ->highValue()
            ->with(['webinar', 'assignedUser'])
            ->limit(5)
            ->get();

        // Handle AJAX request for paginated follow-up data
        if (request()->ajax() && request()->has('section')) {
            $section = request()->section;
            $perPage = request()->get('length', 10);

            if ($section === 'followupToday') {
                $query = Order::assignedTo($userId)
                    ->followUpToday()
                    ->with(['webinar', 'assignedUser']);

                return datatables()->of($query)
                    ->addIndexColumn()
                    ->addColumn('actions', function ($order) {
                        return '<button type="button" class="btn btn-sm btn-success add-contact-btn" data-id="' . $order->id . '" data-name="' . $order->name . '">
                                    <i class="fas fa-plus me-1"></i> Thêm liên hệ
                                </button>';
                    })
                    ->rawColumns(['actions'])
                    ->make(true);
            } elseif ($section === 'overdueFollowup') {
                $query = Order::assignedTo($userId)
                    ->overdueFollowUp()
                    ->with(['webinar', 'assignedUser']);

                return datatables()->of($query)
                    ->addIndexColumn()
                    ->addColumn('actions', function ($order) {
                        return '<button type="button" class="btn btn-sm btn-success add-contact-btn" data-id="' . $order->id . '" data-name="' . $order->name . '">
                                    <i class="fas fa-plus me-1"></i> Thêm liên hệ
                                </button>';
                    })
                    ->rawColumns(['actions'])
                    ->make(true);
            }
        }

        return view('telesales.dashboard', compact(
            'stats', 'webinars', 'highValueProspects'
        ));
    }

    /**
     * Export orders to Excel.
     */
    public function export(Request $request)
    {
        // We'll implement the Excel export functionality here
        // For now, let's redirect to the index with a message
        return redirect()->route('orders.index')
            ->with('info', 'Tính năng xuất Excel sẽ sớm được triển khai');
    }

    /**
     * Add customer care history and update care status in one step.
     * This is used when updating care status from the order list view.
     */
    public function addCareHistoryWithStatus(Request $request, Order $order)
    {
        $request->validate([
            'care_status' => 'required|in:new,contacted,interested,not_interested,callback,converted,closed',
            'contact_type' => 'required|in:phone,email,sms,zalo,other',
            'content' => 'required|string',
            'status' => 'required|in:success,no_answer,callback,not_interested,wrong_number,other',
            'callback_time' => 'nullable|date',
            'is_priority' => 'boolean',
            'reminder_type' => 'nullable|in:none,email,notification,both'
        ]);

        // Create the care history record
        $history = new CustomerCareHistory();
        $history->order_id = $order->id;
        $history->user_id = auth()->id();
        $history->contact_type = $request->contact_type;
        $history->content = $request->content;
        $history->status = $request->status;

        if ($request->status === 'callback' && $request->filled('callback_time')) {
            $history->callback_time = $request->callback_time;

            // Update the order follow-up date
            $order->next_follow_date = $request->callback_time;
        }

        $history->save();

        // Update the order care status
        $order->care_status = $request->care_status;

        // Mark as priority if needed
        if ($request->filled('is_priority')) {
            $order->is_priority = $request->is_priority;
        }

        // Save reminder preferences if set
        if ($request->filled('reminder_type')) {
            $order->reminder_type = $request->reminder_type;
        }

        $order->save();
        // Create a care note with the same content if it's significant status change
        $significantStatuses = ['interested', 'not_interested', 'converted'];
        if (in_array($request->care_status, $significantStatuses)) {
            $priority = ($request->care_status === 'converted') ? 'high' : 'medium';

            $note = new CustomerCareNote();
            $note->order_id = $order->id;
            $note->user_id = auth()->id();
            $note->content = $request->content;
            $note->priority = $priority;
            $note->is_pinned = $request->care_status === 'converted';
            $note->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật trạng thái chăm sóc và lịch sử liên hệ'
        ]);
    }
}
