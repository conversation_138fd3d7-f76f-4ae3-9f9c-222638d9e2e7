<?php

namespace App\Http\Controllers;

use App\Models\FileComment;
use App\Models\Webinar;
use App\Models\WebinarComment;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

class CommentController extends Controller
{
    /**
     * Display a listing of comments for a webinar.
     */
    public function index(Request $request, Webinar $webinar)
    {
        // Authorize that user can view this webinar
        $this->authorize('view', $webinar);

        if ($request->ajax()) {
            $comments = $webinar->comments()->with('participant');

            return DataTables::of($comments)
                ->addColumn('comment_date', function ($comment) {
                    return $comment->created_at->format('d/m/Y');
                })
                ->addColumn('comment_time', function ($comment) {
                    return $comment->created_at->format('H:i');
                })
                ->editColumn('video_timestamp', function ($comment) {
                    return $comment->video_timestamp ?: '-';
                })
                ->make(true);
        }

        return view('comments.index', compact('webinar'));
    }

    /**
     * Display comments grouped by schedule for a webinar.
     */
    public function bySchedule(Request $request, Webinar $webinar)
    {
        // Authorize that user can view this webinar
        $this->authorize('view', $webinar);
        if ($request->ajax()) {
            $comment = FileComment::find($request->get("id"));
            if (is_object($comment)) {
                $list_comments = Storage::disk("public")->get($comment->file);
                $list_comments = collect(json_decode($list_comments, true));

                return view('comments.list_comments', ["selectedDate" => $comment->date, "comments" => $list_comments]);
            }

        }
        $data = [];
        $comments = $webinar->file_comments()->orderBy('date', 'desc')->get();
        //            return view('comments.by_schedule', $viewData);

        $data["comments"] = $comments;
        $data["webinar"] = $webinar;
        return view('comments.by_schedule', $data);
    }
    public function destroyBySchedule(Request $request, Webinar $webinar, FileComment $comment)
    {
        $this->authorize('update', $webinar);
        $file = $comment->file;
        Storage::disk("public")->delete($file);
        $comment->delete();
        return redirect()->back()->with('success', 'Bình luận đã được xóa thành công');
    }
    /**
     * Get all comment files for a webinar
     */
    private function getWebinarCommentFiles($webinarId)
    {
        $basePath = 'webinar_comments';
        $result = [];

        // Check if the directory exists
        if (!Storage::disk('public')->exists($basePath)) {
            return $result;
        }

        // Get all date directories
        $dateDirs = Storage::disk('public')->directories($basePath);

        foreach ($dateDirs as $dateDir) {
            $date = basename($dateDir);

            // Get all time directories for this date
            $timeDirs = Storage::disk('public')->directories($dateDir);

            foreach ($timeDirs as $timeDir) {
                $time = basename($timeDir);

                // Check if comment file exists for this webinar
                $filePath = "{$timeDir}/{$webinarId}.txt";

                if (Storage::disk('public')->exists($filePath)) {
                    $result[$date][$time] = $filePath;
                }
            }
        }

        return $result;
    }

    /**
     * Export comments list to Excel or CSV.
     */
    public function export(Request $request, Webinar $webinar)
    {
        // Authorize that user can view this webinar
        $this->authorize('view', $webinar);

        $format = $request->format ?? 'xlsx';

        $filename = 'comments_' . $webinar->join_code . '_' . now()->format('Ymd_His') . '.' . $format;

        return (new \App\Exports\CommentsExport($webinar->id))
            ->download($filename);
    }

    /**
     * Remove comment from the webinar.
     */
    public function destroy(Webinar $webinar, $commentId)
    {
        // Authorize that user can manage this webinar
        $this->authorize('update', $webinar);

        // Check if database comment
        $comment = WebinarComment::find($commentId);

        if ($comment) {
            // Check if comment belongs to this webinar
            if ($comment->webinar_id != $webinar->id) {
                return response()->json(['error' => 'Unauthorized access'], 403);
            }

            $comment->delete();
            return response()->json(['success' => true]);
        }

        // If no database comment found, try to find and delete from file
        // Decode commentId to get date, time, and index
        $parts = explode('_', $commentId);

        if (count($parts) === 3) {
            $date = $parts[0];
            $time = $parts[1];
            $index = intval($parts[2]);

            $filePath = "webinar_comments/{$date}/{$time}/{$webinar->id}.txt";

            if (Storage::disk('public')->exists($filePath)) {
                $commentsData = Storage::disk('public')->get($filePath);

                try {
                    // Try to parse as JSON first
                    $commentsArray = json_decode($commentsData, true);

                    // If JSON parsing failed, try to extract data from serialized content
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        // Extract comments from serialized Eloquent Collection
                        preg_match_all(
                            '/"id":\s*(\d+),"webinar_id":\s*\d+,"name":"(.*?)","participant_id":(null|\d+),"content":"(.*?)"(,"video_timestamp":(null|".*?")|),"created_at":"(.*?)","updated_at":"(.*?)"/',
                            $commentsData,
                            $matches,
                            PREG_SET_ORDER
                        );

                        $commentsArray = [];

                        foreach ($matches as $match) {
                            $comment = [
                                'id' => (int) $match[1],
                                'name' => str_replace('\\"', '"', $match[2]),
                                'participant_id' => $match[3] === 'null' ? null : (int) $match[3],
                                'content' => str_replace('\\"', '"', $match[4]),
                                'video_timestamp' => isset($match[6]) && $match[6] !== 'null' ? $this->parseVideoTimestamp(trim($match[6], '"')) : null,
                                'created_at' => $match[7],
                                'updated_at' => $match[8]
                            ];

                            $commentsArray[] = $comment;
                        }
                    }

                    if (is_array($commentsArray) && isset($commentsArray[$index])) {
                        // Remove the comment
                        array_splice($commentsArray, $index, 1);

                        // Convert back to JSON format
                        $newContents = json_encode($commentsArray);

                        // Save the updated file
                        Storage::disk('public')->put($filePath, $newContents);

                        return response()->json(['success' => true]);
                    }
                } catch (\Exception $e) {
                    \Log::error('Error processing comment file during deletion: ' . $e->getMessage());
                    return response()->json(['error' => 'Error processing file'], 500);
                }
            }
        }

        return response()->json(['error' => 'Comment not found'], 404);
    }

    /**
     * Download a template Excel file for comment imports.
     */
    public function downloadTemplate()
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'Thời gian');
        $sheet->setCellValue('B1', 'Tên người bình luận');
        $sheet->setCellValue('C1', 'Nội dung bình luận');

        // Example data
        $sheet->setCellValue('A2', '00:01:30');
        $sheet->setCellValue('B2', 'Đạt Tôi');
        $sheet->setCellValue('C2', 'Cần mua áo');

        $sheet->setCellValue('A3', '00:02:45');
        $sheet->setCellValue('B3', 'Mai Anh');
        $sheet->setCellValue('C3', 'Có ship COD không ạ?');

        // Make headers bold
        $sheet->getStyle('A1:C1')->getFont()->setBold(true);

        // Auto size columns
        $sheet->getColumnDimension('A')->setAutoSize(true);
        $sheet->getColumnDimension('B')->setAutoSize(true);
        $sheet->getColumnDimension('C')->setAutoSize(true);

        // Create the Excel file
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $filename = 'comments_template.xlsx';
        $path = public_path('templates/' . $filename);
        $writer->save($path);

        return response()->download($path, $filename);
    }

    /**
     * Parse video timestamp to ensure it's a numeric value in seconds
     *
     * @param string $timestamp
     * @return int|string
     */
    private function parseVideoTimestamp($timestamp)
    {
        // If already numeric, return as is
        if (is_numeric($timestamp)) {
            return (int) $timestamp;
        }

        // Try to parse hh:mm:ss format
        if (preg_match('/^(\d+):(\d+):(\d+)$/', $timestamp, $matches)) {
            $hours = (int) $matches[1];
            $minutes = (int) $matches[2];
            $seconds = (int) $matches[3];

            return $hours * 3600 + $minutes * 60 + $seconds;
        }

        // Try to parse mm:ss format
        if (preg_match('/^(\d+):(\d+)$/', $timestamp, $matches)) {
            $minutes = (int) $matches[1];
            $seconds = (int) $matches[2];

            return $minutes * 60 + $seconds;
        }

        // Return original if unable to parse
        return $timestamp;
    }
}
