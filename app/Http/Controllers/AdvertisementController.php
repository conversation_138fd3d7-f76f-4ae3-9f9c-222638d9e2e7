<?php

namespace App\Http\Controllers;

use App\Models\Advertisement;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class AdvertisementController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole(['administrator', 'admin'])) {
                abort(403, 'Bạn không có quyền truy cập chức năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (auth()->user()->can('advertisements index')) {
            $advertisements = Advertisement::with('product')->latest()->paginate(10);
        } elseif (auth()->user()->can('advertisements view only you')) {
            $advertisements = auth()->user()->advertisements()->with('product')->latest()->paginate(10);
        } else {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('advertisements.index', compact('advertisements'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->can('advertisements create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('advertisements.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('advertisements create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        try {
            // Check storage directory
            if (!is_dir(storage_path('app/public/advertisements'))) {
                Storage::disk('public')->makeDirectory('advertisements');
                Log::info('Created advertisements directory');
            }

            $validated = $request->validate([
                'name' => 'nullable|string|max:255',
                'type' => ['required', Rule::in(['image', 'product'])],
                'image' => 'nullable|file|image|max:2048',
                'url' => 'nullable|url',
                "original_price" => "required",
                "sale_price" => "required",
                'product_id' => 'nullable|exists:products,id',
                'virtual_quantity' => 'nullable|integer|min:0',
                'order_method' => ['required', Rule::in(['form', 'qr', 'url'])],
                'require_name' => 'nullable|boolean',
                'require_email' => 'nullable|boolean',
                'require_phone' => 'nullable|boolean',
                'display_time' => 'required|integer|min:1',
            ]);

            // Additional validation for specific types
            if ($validated['type'] === 'image') {
                if (!$request->hasFile('image') && !$request->filled('url')) {
                    return back()->withErrors(['image' => 'Vui lòng cung cấp hình ảnh hoặc URL.'])
                        ->withInput();
                }
            } elseif ($validated['type'] === 'product') {
                if (!$request->filled('product_id')) {
                    return back()->withErrors(['product_id' => 'Vui lòng chọn một sản phẩm.'])
                        ->withInput();
                }

                // Check if the product belongs to the authenticated user
                $product = Product::findOrFail($validated['product_id']);
                if ($product->user_id !== auth()->id()) {
                    return back()->withErrors(['product_id' => 'Bạn không có quyền chọn sản phẩm này.'])
                        ->withInput();
                }
            }

            // Handle the image upload
            $imagePath = null;
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                // Add additional validations and debug logging
                if ($file->isValid()) {
                    try {
                        // Ensure the advertisements directory exists
                        Storage::disk('public')->makeDirectory('advertisements');

                        // Try direct PHP move instead of Laravel's store method
                        $fileName = time() . '_' . $file->getClientOriginalName();
                        $fullPath = storage_path('app/public/advertisements/' . $fileName);

                        // Log the target path
                        Log::info('Attempting to save file', [
                            'source' => $file->getPathname(),
                            'destination' => $fullPath
                        ]);

                        // Move the file directly
                        if (move_uploaded_file($file->getPathname(), $fullPath)) {
                            $imagePath = 'advertisements/' . $fileName;
                            Log::info('File moved successfully', ['path' => $imagePath]);
                        } else {
                            Log::error('Failed to move uploaded file', [
                                'source' => $file->getPathname(),
                                'destination' => $fullPath,
                                'move_error' => error_get_last()
                            ]);
                            return back()->withErrors(['image' => 'Không thể di chuyển file tải lên.'])
                                ->withInput();
                        }

                        // Check path after upload
                        if (empty($imagePath)) {
                            Log::error('Image path is empty after successful upload');
                            return back()->withErrors(['image' => 'Không thể lưu trữ hình ảnh. Vui lòng thử lại.'])
                                ->withInput();
                        }
                    } catch (\Exception $e) {
                        Log::error('File upload failed', [
                            'error' => $e->getMessage(),
                            'file' => $file->getClientOriginalName(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        return back()->withErrors(['image' => 'Lỗi tải lên: ' . $e->getMessage()])
                            ->withInput();
                    }
                } else {
                    Log::warning('Invalid file upload', [
                        'error' => $file->getErrorMessage(),
                        'error_code' => $file->getError()
                    ]);
                    return back()->withErrors(['image' => 'File hình ảnh không hợp lệ: ' . $file->getErrorMessage()])
                        ->withInput();
                }
            }
            if ($validated['order_method'] == "form") {
                $redirect_url = null;
                $require_name = $request->has('require_name');
                $require_email = $request->has('require_email');
                $require_phone = $request->has('require_phone');
            } elseif ($validated['order_method'] == 'url') {

                $redirect_url = $request->input("redirect_url");
                $require_name = false;
                $require_email = false;
                $require_phone = false;
            } else {
                $redirect_url = null;
                $require_name = false;
                $require_email = false;
                $require_phone = false;
            }
            $advertisement = new Advertisement([
                'name' => $validated['name'] ?? null,
                'type' => $validated['type'],
                'image' => $imagePath,
                'url' => $validated['url'] ?? null,
                'product_id' => $validated['type'] === 'product' ? $validated['product_id'] : null,
                'virtual_quantity' => $validated['type'] === 'product' ? $validated['virtual_quantity'] : null,
                'order_method' => $validated['order_method'],
                'redirect_url' => $redirect_url,
                'require_name' => $require_name,
                'require_email' => $require_email,
                'original_price' => str_replace([",", ".", "đ", " "], "", $request->input('original_price')),
                'sale_price' => str_replace([",", ".", "đ", " "], "", $request->input('sale_price')),
                'require_phone' => $require_phone,
                'display_time' => $validated['display_time'],
                'user_id' => auth()->id(),
            ]);

            $advertisement->save();

            Log::info('Advertisement created successfully', [
                'id' => $advertisement->id,
                'type' => $advertisement->type,
                'image_path' => $advertisement->image
            ]);

            return redirect()->route('advertisements.index')
                ->with('success', 'Quảng cáo đã được tạo thành công.');
        } catch (\Exception $e) {
            Log::error('Exception in advertisement store method', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withErrors(['general' => 'Đã xảy ra lỗi: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Advertisement $advertisement)
    {
        if (!auth()->user()->can('advertisements update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('view', $advertisement);
        return view('advertisements.show', compact('advertisement'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Advertisement $advertisement)
    {
        if (!auth()->user()->can('advertisements update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $advertisement);
        return view('advertisements.edit', compact('advertisement'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Advertisement $advertisement)
    {
        if (!auth()->user()->can('advertisements update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $advertisement);

        try {
            // Check storage directory
            if (!is_dir(storage_path('app/public/advertisements'))) {
                Storage::disk('public')->makeDirectory('advertisements');
                Log::info('Created advertisements directory during update');
            }

            $validated = $request->validate([
                'name' => 'nullable|string|max:255',
                'type' => ['required', Rule::in(['image', 'product'])],
                'image' => 'nullable|file|image|max:2048',
                'url' => 'nullable|url',
                'product_id' => 'nullable|exists:products,id',
                'virtual_quantity' => 'nullable|integer|min:0',
                'order_method' => ['required', Rule::in(['form', 'qr', 'url'])],
                'redirect_url' => 'nullable|string|max:255',
                'require_name' => 'nullable|boolean',
                'require_email' => 'nullable|boolean',
                "original_price" => "required",
                "sale_price" => "required",
                'require_phone' => 'nullable|boolean',
                'remove_image' => 'nullable|boolean',
                'display_time' => 'required|integer|min:1',
            ]);
            // Additional validation for specific types
            if ($validated['type'] === 'image') {
                if (!$request->hasFile('image') && !$request->filled('url') && !$advertisement->image) {
                    return back()->withErrors(['image' => 'Vui lòng cung cấp hình ảnh hoặc URL.'])
                        ->withInput();
                }
            } elseif ($validated['type'] === 'product') {
                if (!$request->filled('product_id')) {
                    return back()->withErrors(['product_id' => 'Vui lòng chọn một sản phẩm.'])
                        ->withInput();
                }

                // Check if the product belongs to the authenticated user
                $product = Product::findOrFail($validated['product_id']);
                if ($product->user_id !== auth()->id()) {
                    return back()->withErrors(['product_id' => 'Bạn không có quyền chọn sản phẩm này.'])
                        ->withInput();
                }
            }

            // Handle the image upload
            $imagePath = $advertisement->image;
            if ($request->filled('remove_image') && $request->boolean('remove_image')) {
                if ($imagePath) {
                    Storage::disk('public')->delete($imagePath);
                }
                $imagePath = null;
            } elseif ($request->hasFile('image')) {
                $file = $request->file('image');
                if ($file->isValid()) {
                    try {
                        // Ensure the advertisements directory exists
                        Storage::disk('public')->makeDirectory('advertisements');

                        if ($imagePath) {
                            Storage::disk('public')->delete($imagePath);
                        }

                        // Try direct PHP move instead of Laravel's store method
                        $fileName = time() . '_' . $file->getClientOriginalName();
                        $fullPath = storage_path('app/public/advertisements/' . $fileName);

                        // Log the target path
                        Log::info('Attempting to save file during update', [
                            'source' => $file->getPathname(),
                            'destination' => $fullPath
                        ]);

                        // Move the file directly
                        if (move_uploaded_file($file->getPathname(), $fullPath)) {
                            $imagePath = 'advertisements/' . $fileName;
                            Log::info('File moved successfully during update', ['path' => $imagePath]);
                        } else {
                            Log::error('Failed to move uploaded file during update', [
                                'source' => $file->getPathname(),
                                'destination' => $fullPath,
                                'move_error' => error_get_last()
                            ]);
                            return back()->withErrors(['image' => 'Không thể di chuyển file tải lên.'])
                                ->withInput();
                        }

                        // Check path after upload
                        if (empty($imagePath)) {
                            Log::error('Image path is empty after successful upload during update');
                            return back()->withErrors(['image' => 'Không thể lưu trữ hình ảnh. Vui lòng thử lại.'])
                                ->withInput();
                        }
                    } catch (\Exception $e) {
                        Log::error('File update failed', [
                            'error' => $e->getMessage(),
                            'file' => $file->getClientOriginalName(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        return back()->withErrors(['image' => 'Lỗi tải lên: ' . $e->getMessage()])
                            ->withInput();
                    }
                } else {
                    Log::warning('Invalid file upload during update', [
                        'error' => $file->getErrorMessage(),
                        'error_code' => $file->getError()
                    ]);
                    return back()->withErrors(['image' => 'File hình ảnh không hợp lệ: ' . $file->getErrorMessage()])
                        ->withInput();
                }
            }
            if ($validated['order_method'] == "form") {
                $redirect_url = null;
                $require_name = $request->has('require_name');
                $require_email = $request->has('require_email');
                $require_phone = $request->has('require_phone');
            } elseif ($validated['order_method'] == 'url') {

                $redirect_url = $request->input("redirect_url");
                $require_name = false;
                $require_email = false;
                $require_phone = false;
            } else {
                $redirect_url = null;
                $require_name = false;
                $require_email = false;
                $require_phone = false;
            }
            $advertisement->update([
                'name' => $validated['name'] ?? null,
                'type' => $validated['type'],
                'image' => $imagePath,
                'url' => $validated['url'] ?? null,
                'product_id' => $validated['type'] === 'product' ? $validated['product_id'] : null,
                'virtual_quantity' => $validated['type'] === 'product' ? $validated['virtual_quantity'] : null,
                'order_method' => $validated['order_method'],
                'redirect_url' => $redirect_url,
                'require_name' => $require_name,
                'require_email' => $require_email,
                'require_phone' => $require_phone,
                'original_price' => str_replace([",", ".", "đ", " "], "", $request->get('original_price')),
                'sale_price' => str_replace([",", ".", "đ", " "], "", $request->get('sale_price')),
                'display_time' => $validated['display_time'],
            ]);

            Log::info('Advertisement updated successfully', [
                'id' => $advertisement->id,
                'type' => $advertisement->type,
                'image_path' => $advertisement->image
            ]);

            return redirect()->route('advertisements.index')
                ->with('success', 'Quảng cáo đã được cập nhật thành công.');
        } catch (\Exception $e) {
            Log::error('Exception in advertisement update method', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withErrors(['general' => 'Đã xảy ra lỗi: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Advertisement $advertisement)
    {
        if (!auth()->user()->can('advertisements delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('delete', $advertisement);

        // Delete the advertisement image if it exists
        if ($advertisement->image) {
            Storage::disk('public')->delete($advertisement->image);
        }

        $advertisement->delete();

        return redirect()->route('advertisements.index')
            ->with('success', 'Quảng cáo đã được xóa thành công.');
    }
}
