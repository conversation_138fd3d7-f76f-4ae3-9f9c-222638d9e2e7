<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');

        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole(['administrator', 'admin'])) {
                abort(403, 'Bạn không có quyền truy cập chức năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the roles.
     */
    public function index()
    {

        if (!auth()->user()->can('roles update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $roles = Role::where("name", "!=", "administrator")->with('permissions')->get();

        return view('admin.roles.index', compact('roles'));
    }

    private function formatPermissionName($permissionName)
    {
        $map = [
            'webinars' => 'Webinar',
            'product' => 'Sản phẩm',
            'advertisements' => 'Quảng cáo',
            'order' => 'Đơn hàng',
            'users' => 'Người dùng',
            'roles' => 'Vai trò',
            'index' => 'Xem danh sách',
            'create' => 'Thêm mới',
            'update' => 'Cập nhật',
            'delete' => 'Xóa',
            'show' => 'Xem chi tiết',
            'dashboard' => 'Bảng điều khiển',
            'settings' => 'Cài đặt',
            'telesales' => 'Telesales',
            'limit webinars' => 'Giới hạn webinar'
        ];

        $parts = explode(' ', $permissionName);
        $formattedParts = array_map(function($part) use ($map) {
            return $map[$part] ?? ucfirst($part);
        }, $parts);

        return implode(' ', $formattedParts);
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        if (!auth()->user()->can('roles create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $permissions = Permission::whereNotIn("name",[
            "webinars index",
            "product index",
            "advertisements index",
            "webinars view only you",
            "advertisements view only you",
            "product view only you",
        ])->get();

        // Group related permissions
        $groupedPermissions = [
            'webinars' => [
                'index' => Permission::where('name', 'webinars index')->first(),
                'view_only' => Permission::where('name', 'webinars view only you')->first(),
            ],
            'product' => [
                'index' => Permission::where('name', 'product index')->first(),
                'view_only' => Permission::where('name', 'product view only you')->first(),
            ],
            'advertisements' => [
                'index' => Permission::where('name', 'advertisements index')->first(),
                'view_only' => Permission::where('name', 'advertisements view only you')->first(),
            ],
            'other' => $permissions->map(function($permission) {
                $permission->display_name = $this->formatPermissionName($permission->name);
                return $permission;
            })
        ];

        return view('admin.roles.create', compact('groupedPermissions'));
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('roles create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'array',
        ]);

        $role = Role::create(['name' => $validated['name']]);

        if (!empty($validated['permissions'])) {
            $role->syncPermissions($validated['permissions']);
        }

        return redirect()->route('admin.roles.index')
            ->with('success', 'Tạo vai trò mới thành công!');
    }

    /**
     * Display the specified role.
     */
    public function show(Role $role)
    {
        if (!auth()->user()->can('roles show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $role->load('permissions');
        return view('admin.roles.show', compact('role'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(Role $role)
    {
        if (!auth()->user()->can('roles update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        // Lấy tất cả quyền
        $permissions = auth()->user()->getAllPermissions();

        $role->load('permissions');

        // Group related permissions
        $groupedPermissions = [
            'webinars' => [
                'index' => Permission::where('name', 'webinars index')->first(),
                'view_only' => Permission::where('name', 'webinars view only you')->first(),
            ],
            'product' => [
                'index' => Permission::where('name', 'product index')->first(),
                'view_only' => Permission::where('name', 'product view only you')->first(),
            ],
            'advertisements' => [
                'index' => Permission::where('name', 'advertisements index')->first(),
                'view_only' => Permission::where('name', 'advertisements view only you')->first(),
            ],
            'other' => $permissions->map(function($permission) {
                $permission->display_name = $this->formatPermissionName($permission->name);
                return $permission;
            })
        ];

        return view('admin.roles.edit', compact('role', 'groupedPermissions'));
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, Role $role)
    {
        if (!auth()->user()->can('roles update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'permissions' => 'array',
        ]);

        $role->update(['name' => $validated['name']]);

        $permissions = $request->permissions ?? [];
        $role->syncPermissions($permissions);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Cập nhật vai trò thành công!');
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy(Role $role)
    {
        if (!auth()->user()->can('roles delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        // Prevent deleting the admin role
        if ($role->name === 'administrator') {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Không thể xóa vai trò administrator.');
        }
        if (!auth()->user()->hasRole('administrator') && $role->name == 'admin') {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Không thể xóa vai trò admin.');
        }
        $role->delete();

        return redirect()->route('admin.roles.index')
            ->with('success', 'Xóa vai trò thành công!');
    }
}
