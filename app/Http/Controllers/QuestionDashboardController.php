<?php

namespace App\Http\Controllers;

use App\Models\Question;
use App\Models\QuestionResponse;
use App\Models\Webinar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class QuestionDashboardController extends Controller
{
    /**
     * Show main dashboard
     */
    public function index(Request $request)
    {
        $webinarId = $request->get('webinar_id');
        $webinar = $webinarId ? Webinar::find($webinarId) : Webinar::first();
        
        if (!$webinar) {
            return redirect()->route('webinars.index')->with('error', 'Không có webinar nào để hiển thị dashboard');
        }

        // Thống kê tổng quan
        $stats = $this->getOverallStats($webinar->id);
        
        // Câu hỏi đang active
        $activeQuestions = Question::where('webinar_id', $webinar->id)
            ->where('status', true)
            ->with('responses')
            ->orderBy('created_at', 'desc')
            ->get();

        $webinars = Webinar::all();

        return view('questions.dashboard.index', compact('webinar', 'webinars', 'stats', 'activeQuestions'));
    }

    /**
     * Show question detail dashboard
     */
    public function questionDetail(Question $question)
    {
        $question->load('responses', 'webinar');
        
        // Real-time stats
        $stats = [
            'total_responses' => $question->responses()->count(),
            'today_responses' => $question->responses()->today()->count(),
            'realtime_responses' => $question->responses()->realTime()->count(),
            'response_stats' => $question->response_stats
        ];

        // Recent responses (last 50)
        $recentResponses = $question->responses()
            ->orderBy('responded_at', 'desc')
            ->take(50)
            ->get();

        return view('questions.dashboard.detail', compact('question', 'stats', 'recentResponses'));
    }

    /**
     * Get real-time stats API
     */
    public function getRealTimeStats(Request $request)
    {
        $webinarId = $request->get('webinar_id');
        $questionId = $request->get('question_id');

        if ($questionId) {
            // Stats for specific question
            $question = Question::with('responses')->findOrFail($questionId);
            return response()->json([
                'total_responses' => $question->responses()->count(),
                'today_responses' => $question->responses()->today()->count(),
                'realtime_responses' => $question->responses()->realTime()->count(),
                'response_stats' => $question->response_stats,
                'updated_at' => now()->toISOString()
            ]);
        }

        if ($webinarId) {
            // Stats for webinar
            $stats = $this->getOverallStats($webinarId);
            return response()->json($stats);
        }

        return response()->json(['error' => 'Missing parameters'], 400);
    }

    /**
     * Get recent responses stream
     */
    public function getRecentResponses(Request $request)
    {
        $webinarId = $request->get('webinar_id');
        $questionId = $request->get('question_id');
        $since = $request->get('since'); // timestamp

        $query = QuestionResponse::with(['question', 'webinar']);

        if ($questionId) {
            $query->where('question_id', $questionId);
        } elseif ($webinarId) {
            $query->where('webinar_id', $webinarId);
        }

        if ($since) {
            $query->where('responded_at', '>', $since);
        }

        $responses = $query->orderBy('responded_at', 'desc')
            ->take(20)
            ->get();

        return response()->json([
            'responses' => $responses,
            'count' => $responses->count(),
            'latest_timestamp' => $responses->first()?->responded_at?->toISOString()
        ]);
    }

    /**
     * Submit response (for testing/demo)
     */
    public function submitResponse(Request $request)
    {
        $request->validate([
            'question_id' => 'required|exists:questions,id',
            'selected_answers' => 'array',
            'rating' => 'nullable|integer|between:1,5',
            'answer_text' => 'nullable|string|max:500'
        ]);

        $question = Question::findOrFail($request->question_id);

        $response = QuestionResponse::create([
            'question_id' => $question->id,
            'webinar_id' => $question->webinar_id,
            'user_name' => $request->user_name ?: 'Anonymous',
            'user_email' => $request->user_email,
            'user_phone' => $request->user_phone,
            'session_id' => session()->getId(),
            'selected_answers' => $request->selected_answers ?: [],
            'answer_text' => $request->answer_text,
            'rating' => $request->rating,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'responded_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Phản hồi đã được ghi nhận',
            'response' => $response
        ]);
    }

    /**
     * Get overall stats for webinar
     */
    private function getOverallStats($webinarId)
    {
        $totalQuestions = Question::where('webinar_id', $webinarId)->count();
        $activeQuestions = Question::where('webinar_id', $webinarId)->where('status', true)->count();
        $totalResponses = QuestionResponse::where('webinar_id', $webinarId)->count();
        $todayResponses = QuestionResponse::where('webinar_id', $webinarId)->today()->count();
        $realtimeResponses = QuestionResponse::where('webinar_id', $webinarId)->realTime()->count();
        
        // Unique participants (based on session_id)
        $uniqueParticipants = QuestionResponse::where('webinar_id', $webinarId)
            ->distinct('session_id')
            ->count();

        // Response rate
        $responseRate = $uniqueParticipants > 0 && $activeQuestions > 0 
            ? round(($totalResponses / ($uniqueParticipants * $activeQuestions)) * 100, 1)
            : 0;

        // Stats by question type
        $statsByType = Question::where('webinar_id', $webinarId)
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get()
            ->keyBy('type');

        return [
            'total_questions' => $totalQuestions,
            'active_questions' => $activeQuestions,
            'total_responses' => $totalResponses,
            'today_responses' => $todayResponses,
            'realtime_responses' => $realtimeResponses,
            'unique_participants' => $uniqueParticipants,
            'response_rate' => $responseRate,
            'stats_by_type' => $statsByType,
            'updated_at' => now()->toISOString()
        ];
    }
}
