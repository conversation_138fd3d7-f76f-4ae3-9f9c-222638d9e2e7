<?php

namespace App\Http\Controllers\Api;

use App\Events\OrderPaymentAutoSuccessEvent;
use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SepayController extends Controller
{
    //
    public function handleWebhook(Request $request)
    {
        $apiKey = setting('sepay_api_key');

        $authHeader = $request->header('Authorization');
        $headerApiKey = str_replace('Apikey ', '', $authHeader);
        if ($headerApiKey !== $apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid API key'
            ], 401);
        }

        $data = $request->all();
        $description = $data["content"] ?? null;
        $amount = $data['transferAmount'] ?? null;
        $phone = $data['accountNumber'] ?? null;

        $payment_note = $this->getPaymentNote($description);

        $order = Order::where('transaction_note', $payment_note)
//            ->where("phone", $phone)
            ->where("price", $amount)
            ->first();

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        }


        switch ($order->payment_status) {
            case 'paid':
                $message = "Order already paid";
                break;
            case 'pending':
                $order->payment_status = 'paid';
                $order->care_status = 'converted';
                $order->save();

                event(new OrderPaymentAutoSuccessEvent($order));
                $message = "Order paid successfully";
                break;
            default:
                $message = "Order cancelled";
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ], 200);
    }

    private function getPaymentNote($content)
    {
        return Str::after($content, "QR - ");
    }
}
