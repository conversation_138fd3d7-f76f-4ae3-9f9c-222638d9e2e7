<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\Webinar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use GuzzleHttp\Client;
use App\Services\TelegramApiService;

class SettingController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the settings page.
     */
    public function index(Request $request)
    {
        // Get active group from request or default to 'general'
        $activeGroup = $request->query('group', 'general');

        // Get all groups for navigation
        $groups = Setting::getGroups();

        // Get settings for the active group
        $settings = Setting::getByGroup($activeGroup);

        // Filter out recaptcha_enabled from settings when in security group
        // since it's already included in the recaptcha_setup partial
        if ($activeGroup === 'security') {
            $settings = $settings->filter(function($setting) {
                return $setting->key !== 'recaptcha_enabled';
            });
        }

        // Add telegram settings data for notifications tab
        $telegramSettings = [];
        if ($activeGroup === 'notifications') {
            $telegramSettings = [
                'telegram_enabled' => Setting::get('telegram_enabled', '0'),
                'telegram_bot_token' => Setting::get('telegram_bot_token'),
                'telegram_chat_ids' => Setting::get('telegram_chat_ids'),
                'telegram_notify_participants' => Setting::get('telegram_notify_participants', '0'),
                'telegram_notify_comments' => Setting::get('telegram_notify_comments', '0'),
                'telegram_bot_username' => Setting::get('telegram_bot_username'),
                'telegram_bot_name' => Setting::get('telegram_bot_name'),
                'telegram_chat_users' => Setting::get('telegram_chat_users', '{}'),
            ];
        }

        // Calculate storage stats for limits tab
        $storageStats = [];
        if ($activeGroup === 'limits') {
            $storageStats = $this->calculateStorageStats();
        }

        return view('settings.index', compact('settings', 'groups', 'activeGroup', 'telegramSettings', 'storageStats'));
    }

    /**
     * Update the settings.
     */
    public function update(Request $request)
    {
        $group = $request->input('group', 'general');

        // For appearance group, redirect to the dedicated handler
        if ($group === 'appearance') {
            return $this->updateAppearance($request);
        }

        $settings = Setting::getByGroup($group);

        $rules = [];
        $messages = [];

        // Build validation rules based on setting type
        foreach ($settings as $setting) {
            if ($setting->type === 'image' && $request->hasFile($setting->key)) {
                $rules[$setting->key] = 'nullable|image|max:2048';
                $messages[$setting->key.'.image'] = 'Tệp tải lên phải là hình ảnh';
                $messages[$setting->key.'.max'] = 'Hình ảnh không được vượt quá 2MB';
            } elseif ($setting->type === 'url') {
                $rules[$setting->key] = 'nullable|url';
                $messages[$setting->key.'.url'] = 'Vui lòng nhập một URL hợp lệ';
            } elseif ($setting->type === 'email') {
                $rules[$setting->key] = 'nullable|email';
                $messages[$setting->key.'.email'] = 'Vui lòng nhập một địa chỉ email hợp lệ';
            } elseif ($setting->type === 'number') {
                $rules[$setting->key] = 'nullable|numeric';
                $messages[$setting->key.'.numeric'] = 'Vui lòng nhập một số';
            }
        }

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return redirect()->route('settings.index', ['group' => $group])
                ->withErrors($validator)
                ->withInput();
        }

        // Update settings
        foreach ($settings as $setting) {
            if ($setting->type === 'image') {
                if ($request->hasFile($setting->key)) {
                    // Handle file upload
                    Setting::setFile($setting->key, $request->file($setting->key));
                }
                // Nếu không có file upload mới thì giữ nguyên giá trị cũ, không làm gì cả
            } elseif ($setting->type === 'boolean') {
                // Special handling for checkboxes: they are not sent in the request if unchecked
                $value = $request->has($setting->key) ? '1' : '0';
                Setting::set($setting->key, $value);
            } else {
                // Handle other types of settings
                $value = $request->input($setting->key);
                Setting::set($setting->key, $value);
            }
        }

        // Clear the settings cache
        Setting::clearCache();

        return redirect()->route('settings.index', ['group' => $group])
            ->with('success', 'Cài đặt đã được cập nhật thành công.');
    }

    /**
     * Delete an uploaded file setting.
     */
    public function deleteFile(Request $request, $key)
    {
        try {
            \Log::info('deleteFile method called', [
                'key' => $key,
                'is_ajax' => $request->ajax(),
                'is_json' => $request->wantsJson(),
                'content_type' => $request->header('Content-Type'),
                'all_inputs' => $request->all()
            ]);

            $setting = Setting::where('key', $key)->firstOrFail();

            if ($setting->value) {
                // Get the full path
                $fullPath = $setting->value;
                \Log::info('Deleting file', ['path' => $fullPath]);

                // Delete the file
                if (Storage::disk('public')->exists($fullPath)) {
                    Storage::disk('public')->delete($fullPath);
                    \Log::info('File deleted successfully');
                } else {
                    \Log::warning('File does not exist in storage', ['path' => $fullPath]);
                }

                // Update the setting value
                Setting::set($key, null);
                Setting::clearCache();

                // If this is an AJAX request, return JSON response
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Tệp đã được xóa thành công'
                    ]);
                }

                return redirect()->route('settings.index', ['group' => $setting->group])
                    ->with('success', 'Tệp đã được xóa.');
            }

            // If this is an AJAX request, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy tệp để xóa'
                ]);
            }

            return redirect()->route('settings.index', ['group' => $setting->group])
                ->with('error', 'Không tìm thấy tệp để xóa.');

        } catch (\Exception $e) {
            \Log::error('Error deleting file', [
                'key' => $key,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // If this is an AJAX request, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi khi xóa tệp: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('settings.index', ['group' => 'appearance'])
                ->with('error', 'Lỗi khi xóa tệp: ' . $e->getMessage());
        }
    }

    /**
     * Test upload functionality to debug issues.
     */
    public function testUpload(Request $request)
    {
        if ($request->hasFile('test_file')) {
            try {
                // 1. Lấy thông tin về file
                $file = $request->file('test_file');
                $originalName = $file->getClientOriginalName();
                $mimeType = $file->getMimeType();
                $size = $file->getSize();

                // 2. Thử lưu file trực tiếp không qua model Setting
                $path = $file->store('test_uploads', 'public');

                // 3. Log kết quả
                \Log::info('TEST UPLOAD SUCCESS', [
                    'originalName' => $originalName,
                    'mimeType' => $mimeType,
                    'size' => $size,
                    'path' => $path
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Upload thành công',
                    'path' => $path,
                    'url' => Storage::url($path),
                    'file_info' => [
                        'name' => $originalName,
                        'type' => $mimeType,
                        'size' => $size
                    ]
                ]);
            } catch (\Exception $e) {
                \Log::error('TEST UPLOAD ERROR', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi: ' . $e->getMessage(),
                    'file' => $request->hasFile('test_file') ? 'File đã được gửi' : 'Không tìm thấy file'
                ], 500);
            }
        }

        // Form để test upload
        return view('settings.test-upload');
    }

    /**
     * Test Telegram connection and send a test message.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function testTelegramConnection(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'bot_token' => 'required|string',
            'chat_ids' => 'required|array',
            'chat_ids.*' => 'required|string',
        ]);

        $botToken = $validated['bot_token'];
        $chatIds = $validated['chat_ids'];

        // Test message content
        $message = "🔔 *Tin nhắn kiểm tra*\n\n";
        $message .= "Đây là tin nhắn thử nghiệm từ hệ thống quản lý webinar.\n";
        $message .= "Nếu bạn nhận được tin nhắn này, cài đặt Telegram của bạn đã hoạt động.\n\n";
        $message .= "🕒 Thời gian: " . now()->format('d/m/Y H:i:s');

        // Track success count
        $successCount = 0;
        $errors = [];

        // Send test message to each chat ID
        foreach ($chatIds as $chatId) {
            try {
                $response = $this->sendTelegramMessage($botToken, $chatId, $message);

                if (isset($response['ok']) && $response['ok'] === true) {
                    $successCount++;
                } else {
                    $errorMessage = $response['description'] ?? 'Unknown error';
                    $errors[] = "Chat ID {$chatId}: {$errorMessage}";
                }
            } catch (\Exception $e) {
                $errors[] = "Chat ID {$chatId}: " . $e->getMessage();
            }
        }

        // Return response
        if ($successCount > 0) {
            return response()->json([
                'success' => true,
                'sent_count' => $successCount,
                'failed_count' => count($chatIds) - $successCount,
                'errors' => $errors
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Không thể gửi tin nhắn đến bất kỳ người dùng nào.',
                'errors' => $errors
            ], 400);
        }
    }

    /**
     * Send a message to Telegram using Bot API.
     *
     * @param string $botToken
     * @param string $chatId
     * @param string $message
     * @return array
     */
    private function sendTelegramMessage($botToken, $chatId, $message)
    {
        $url = TelegramApiService::getSendMessageUrl($botToken);

        $data = [
            'chat_id' => $chatId,
            'text' => $message,
            'parse_mode' => TelegramApiService::getDefaultParseMode()
        ];

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);

        if ($result === false) {
            return ['ok' => false, 'description' => 'Network error'];
        }

        return json_decode($result, true);
    }

    /**
     * Display the simplified Telegram setup page.
     *
     * @return \Illuminate\Http\Response
     */
    public function showTelegramSetup()
    {
        // Lấy cài đặt hiện tại của Telegram
        $telegramSettings = [
            'telegram_enabled' => Setting::get('telegram_enabled', '0'),
            'telegram_bot_token' => Setting::get('telegram_bot_token'),
            'telegram_chat_ids' => Setting::get('telegram_chat_ids'),
            'telegram_notify_participants' => Setting::get('telegram_notify_participants', '0'),
            'telegram_notify_comments' => Setting::get('telegram_notify_comments', '0'),
            'telegram_bot_username' => Setting::get('telegram_bot_username'),
            'telegram_bot_name' => Setting::get('telegram_bot_name'),
        ];

        return view('settings.telegram_setup_guide', compact('telegramSettings'));
    }

    /**
     * Generate a new API key for Sepay integration.
     *
     * @return \Illuminate\Http\Response
     */
    public function regenerateApiKey()
    {
        // Generate a new API key
        $apiKey = generateApiKey(32);

        // Save it to the settings
        Setting::set('sepay_api_key', $apiKey);

        // Clear cache
        Setting::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Khóa API đã được tạo mới thành công',
            'api_key' => $apiKey
        ]);
    }

    /**
     * Test Google reCAPTCHA configuration.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function testRecaptcha(Request $request)
    {
        // Get reCAPTCHA settings from database
        $siteKey = Setting::get('recaptcha_site_key');
        $secretKey = Setting::get('recaptcha_secret_key');
        $enabled = Setting::get('recaptcha_enabled') === '1';

        // Check if required settings exist
        if (empty($siteKey) || empty($secretKey)) {
            return response()->json([
                'success' => false,
                'message' => 'Cấu hình reCAPTCHA chưa được thiết lập. Vui lòng cập nhật Site Key và Secret Key trong cài đặt.'
            ], 400);
        }

        if (!$enabled) {
            return response()->json([
                'success' => false,
                'message' => 'reCAPTCHA hiện đang bị tắt. Hãy bật nó lên để sử dụng tính năng này.'
            ], 400);
        }

        // For test_only mode, just validate the credentials without an actual token
        if ($request->input('test_only', false)) {
            try {
                $client = new \GuzzleHttp\Client();
                $response = $client->post('https://www.google.com/recaptcha/api/siteverify', [
                    'form_params' => [
                        'secret' => $secretKey,
                        'response' => 'test_mode'
                    ]
                ]);

                $result = json_decode((string) $response->getBody(), true);

                // In test mode, we expect an error about the token,
                // but if the secret key is valid, it will give a specific error
                if (isset($result['error-codes']) &&
                    is_array($result['error-codes']) &&
                    in_array('invalid-input-response', $result['error-codes'])) {
                    // This means the secret key is valid but the token is not
                    return response()->json([
                        'success' => true,
                        'message' => 'Thông tin đăng nhập reCAPTCHA hợp lệ! Hệ thống đã sẵn sàng sử dụng reCAPTCHA.',
                        'details' => $result
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cấu hình reCAPTCHA không hợp lệ: ' . ($result['error-codes'][0] ?? 'Lỗi không xác định'),
                        'details' => $result
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('reCAPTCHA verification error', [
                    'error' => $e->getMessage(),
                    'site_key' => $siteKey
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
                ], 500);
            }
        } else {
            // Original behavior when testing with a token
            $token = $request->input('token');

            if (empty($token)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token không được cung cấp'
                ], 400);
            }

            try {
                $client = new \GuzzleHttp\Client();
                $response = $client->post('https://www.google.com/recaptcha/api/siteverify', [
                    'form_params' => [
                        'secret' => $secretKey,
                        'response' => $token
                    ]
                ]);

                $result = json_decode((string) $response->getBody(), true);

                if ($result['success']) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Cấu hình reCAPTCHA hoạt động tốt!',
                        'score' => $result['score'] ?? null,
                        'details' => $result
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cấu hình reCAPTCHA không hợp lệ: ' . ($result['error-codes'][0] ?? 'Lỗi không xác định'),
                        'details' => $result
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('reCAPTCHA verification error', [
                    'error' => $e->getMessage(),
                    'site_key' => $siteKey
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
                ], 500);
            }
        }
    }

    /**
     * Update appearance settings - Direct method
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function updateAppearance(Request $request)
    {
        // Debug log with all request information
        \Log::info('updateAppearance direct method called', [
            'method' => $request->method(),
            'has_files' => $request->hasFile('favicon') || $request->hasFile('admin_logo'),
            'files' => $request->allFiles()
        ]);

        try {
            // Handle favicon upload
            if ($request->hasFile('favicon')) {
                $file = $request->file('favicon');

                if ($file && $file->isValid()) {
                    // Store file with direct path manipulation
                    $directory = 'settings';
                    $extension = $file->getClientOriginalExtension() ?: 'png';
                    $fileName = 'favicon_' . time() . '.' . $extension;

                    // Make sure the directory exists
                    $storagePath = storage_path('app/public/' . $directory);
                    if (!file_exists($storagePath)) {
                        mkdir($storagePath, 0755, true);
                    }

                    // Store using move method to ensure the file gets saved
                    $filePath = $file->move($storagePath, $fileName);

                    if ($filePath) {
                        $relativePath = $directory . '/' . $fileName;

                        \Log::info('Favicon stored successfully', [
                            'absolutePath' => $filePath->getPathname(),
                            'relativePath' => $relativePath
                        ]);

                        // Update the setting with the correct relative path
                        Setting::set('favicon', $relativePath);
                    }
                }
            }

            // Handle admin_logo upload
            if ($request->hasFile('admin_logo')) {
                $file = $request->file('admin_logo');

                if ($file && $file->isValid()) {
                    // Store file with direct path manipulation
                    $directory = 'settings';
                    $extension = $file->getClientOriginalExtension() ?: 'png';
                    $fileName = 'admin_logo_' . time() . '.' . $extension;

                    // Make sure the directory exists
                    $storagePath = storage_path('app/public/' . $directory);
                    if (!file_exists($storagePath)) {
                        mkdir($storagePath, 0755, true);
                    }

                    // Store using move method to ensure the file gets saved
                    $filePath = $file->move($storagePath, $fileName);

                    if ($filePath) {
                        $relativePath = $directory . '/' . $fileName;

                        \Log::info('Admin logo stored successfully', [
                            'absolutePath' => $filePath->getPathname(),
                            'relativePath' => $relativePath
                        ]);

                        // Update the setting with the correct relative path
                        Setting::set('admin_logo', $relativePath);
                    }
                }
            }

            // Update footer text
            if ($request->has('footer_text')) {
                Setting::set('footer_text', $request->input('footer_text'));
            }

            // Clear the settings cache
            Setting::clearCache();

            \Log::info('Appearance settings updated successfully');

            return redirect()->route('settings.index', ['group' => 'appearance'])
                ->with('success', 'Cài đặt giao diện đã được cập nhật thành công.');

        } catch (\Exception $e) {
            \Log::error('Error updating appearance settings', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('settings.index', ['group' => 'appearance'])
                ->with('error', 'Lỗi cập nhật giao diện: ' . $e->getMessage());
        }
    }

    /**
     * Test file upload for appearance settings.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function testAppearanceUpload(Request $request)
    {
        // Log all request information
        \Log::info('testAppearanceUpload called', [
            'method' => $request->method(),
            'has_files' => $request->hasFile('favicon') || $request->hasFile('admin_logo'),
            'has_favicon' => $request->hasFile('favicon'),
            'has_logo' => $request->hasFile('admin_logo'),
            'all_inputs' => $request->all(),
            'is_ajax' => $request->ajax(),
            'content_type' => $request->header('Content-Type'),
        ]);

        // Check for file presence
        if ($request->hasFile('favicon')) {
            $file = $request->file('favicon');
            \Log::info('Favicon file details', [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType(),
                'is_valid' => $file->isValid(),
                'error' => $file->getError()
            ]);

            try {
                // Test direct storage
                $path = $file->store('test_uploads', 'public');
                \Log::info('Favicon storage success', ['path' => $path]);

                return redirect()->route('settings.index', ['group' => 'appearance'])
                    ->with('success', 'Test upload successful! File stored at: ' . $path);
            } catch (\Exception $e) {
                \Log::error('Favicon storage error', [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->route('settings.index', ['group' => 'appearance'])
                    ->with('error', 'Favicon upload test failed: ' . $e->getMessage());
            }
        }

        return redirect()->route('settings.index', ['group' => 'appearance'])
            ->with('error', 'No file was provided for testing');
    }

    /**
     * Calculate storage statistics.
     *
     * @return array
     */
    private function calculateStorageStats()
    {
        // Calculate total video storage from webinars
        $totalVideoStorage = Webinar::whereNotNull('video_file_size')->sum('video_file_size') ?? 0;

        // Calculate website storage (code + database)
        $websiteStorage = $this->calculateWebsiteStorage();

        return [
            'total_video_storage' => $totalVideoStorage,
            'website_storage' => $websiteStorage,
            'video_count' => Webinar::whereNotNull('video_file_size')->count()
        ];
    }

    /**
     * Calculate total website storage (code + database).
     *
     * @return int
     */
    private function calculateWebsiteStorage()
    {
        try {
            $totalSize = 0;

            // Calculate code size (excluding storage and vendor folders)
            $basePath = base_path();
            $excludePaths = [
                'storage/app/public',
                'storage/logs',
                'vendor',
                'node_modules',
                '.git'
            ];

            $totalSize += $this->getDirectorySize($basePath, $excludePaths);

            // Calculate database size
            $databaseSize = $this->getDatabaseSize();
            $totalSize += $databaseSize;

            return $totalSize;
        } catch (\Exception $e) {
            // Return 0 if calculation fails
            return 0;
        }
    }

    /**
     * Get directory size excluding specified paths.
     *
     * @param string $directory
     * @param array $excludePaths
     * @return int
     */
    private function getDirectorySize($directory, $excludePaths = [])
    {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $filePath = $file->getPathname();
                    $relativePath = str_replace($directory . DIRECTORY_SEPARATOR, '', $filePath);

                    // Check if file is in excluded paths
                    $isExcluded = false;
                    foreach ($excludePaths as $excludePath) {
                        if (strpos($relativePath, $excludePath) === 0) {
                            $isExcluded = true;
                            break;
                        }
                    }

                    if (!$isExcluded) {
                        $size += $file->getSize();
                    }
                }
            }
        } catch (\Exception $e) {
            // If directory scanning fails, return 0
            return 0;
        }

        return $size;
    }

    /**
     * Get database size.
     *
     * @return int
     */
    private function getDatabaseSize()
    {
        try {
            $databaseName = config('database.connections.mysql.database');

            $result = DB::select("
                SELECT
                    SUM(data_length + index_length) as size
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [$databaseName]);

            return $result[0]->size ?? 0;
        } catch (\Exception $e) {
            // If database size calculation fails, return 0
            return 0;
        }
    }
}
