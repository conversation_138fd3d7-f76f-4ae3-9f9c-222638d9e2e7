<?php

namespace App\Http\Controllers;

use App\Models\Question;
use App\Models\Webinar;
use Illuminate\Http\Request;
use RealRashid\SweetAlert\Facades\Alert;
use App\Http\Requests\StoreQuestionRequest;
use App\Http\Requests\UpdateQuestionRequest;

class QuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        if (auth()->user()->can('questions index')) {
            $query = Question::with('webinar');
        } elseif (auth()->user()->can('questions view only you')) {
            $query = Question::with('webinar')->where('user_id', auth()->id());
        } else {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        // Filter by search
        if (request('search')) {
            $query->where('title', 'like', '%' . request('search') . '%');
        }

        // Filter by webinar
        if (request('webinar_id')) {
            $query->where('webinar_id', request('webinar_id'));
        }

        // Filter by type
        if (request('type')) {
            $query->where('type', request('type'));
        }

        // Filter by status
        if (request('status') !== null && request('status') !== '') {
            $query->where('status', request('status'));
        }

        $questions = $query->orderBy('created_at', 'desc')->paginate(15);
        $webinars = Webinar::all();

        return view('questions.index', compact('questions', 'webinars'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->can('questions create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $webinars = Webinar::all();

        // Check if there are webinars available
        if ($webinars->isEmpty()) {
            Alert::error('Lỗi!', 'Vui lòng tạo webinar trước khi tạo câu hỏi!');
            return redirect()->route('webinars.index');
        }

        return view('questions.create', compact('webinars'));
    }

    /**
     * Store a newly created resource in storage.
     * @param StoreQuestionRequest $request
     * @return mixed
     */
    public function store(StoreQuestionRequest $request)
    {
        if (!auth()->user()->can('questions create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            // Get validated and processed data from the Form Request
            $validated = $request->getValidatedData();

            // Create question
            $question = Question::create($validated);

            Alert::success('Thành công!', 'Câu hỏi đã được tạo thành công.');
            return redirect()->route('question-sets.index');

        } catch (\Exception $e) {
            Alert::error('Lỗi!', 'Có lỗi xảy ra khi tạo câu hỏi. Vui lòng thử lại.');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Question $question)
    {
        $question->load('webinar', 'responses');
        return view('questions.show', compact('question'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Question $question)
    {
        if (!auth()->user()->can('questions edit')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $webinars = Webinar::all();
        return view('questions.edit', compact('question', 'webinars'));
    }

    /**
     * Update the specified resource in storage.
     * @param UpdateQuestionRequest $request
     * @param Question $question
     * @return mixed
     */
    public function update(UpdateQuestionRequest $request, Question $question)
    {
        if (!auth()->user()->can('questions edit')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            // Get validated and processed data from the Form Request
            $validated = $request->getValidatedData();

            // Update question
            $question->update($validated);

            Alert::success('Thành công!', 'Câu hỏi đã được cập nhật thành công.');
            return redirect()->route('question-sets.index');

        } catch (\Exception $e) {
            Alert::error('Lỗi!', 'Có lỗi xảy ra khi cập nhật câu hỏi. Vui lòng thử lại.');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Question $question)
    {
        if (!auth()->user()->can('questions delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            // Check if question has responses
            if ($question->responses()->exists()) {
                Alert::error('Không thể xóa!', 'Không thể xóa câu hỏi đã có người trả lời!');
                return redirect()->route('question-sets.index');
            }

            $question->delete();

            Alert::success('Thành công!', 'Câu hỏi đã được xóa thành công!');
            return redirect()->route('question-sets.index');

        } catch (\Exception $e) {
            Alert::error('Lỗi!', 'Có lỗi xảy ra khi xóa câu hỏi!');
            return redirect()->route('question-sets.index');
        }
    }

    /**
     * Toggle status of a question
     */
    public function toggleStatus(Question $question)
    {
        if (!auth()->user()->can('question edit')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            $question->update(['status' => !$question->status]);

            $message = $question->status ? 'Đã kích hoạt câu hỏi' : 'Đã tạm dừng câu hỏi';

            return response()->json([
                'success' => true,
                'status' => $question->status,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật trạng thái'
            ], 500);
        }
    }

    /**
     * Toggle status of all questions
     */
    public function toggleAllStatus(Request $request)
    {
        if (!auth()->user()->can('questions edit')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            $status = $request->input('status', 0);

            Question::query()->update(['status' => $status]);

            $message = $status ? 'Đã kích hoạt tất cả câu hỏi' : 'Đã tạm dừng tất cả câu hỏi';

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật trạng thái'
            ], 500);
        }
    }
}
