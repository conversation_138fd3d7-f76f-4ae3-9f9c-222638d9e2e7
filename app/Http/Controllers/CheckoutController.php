<?php

namespace App\Http\Controllers;

use App\Events\OrderCreatedEvent;
use App\Models\Advertisement;
use App\Models\Order;
use App\Models\Webinar;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CheckoutController extends Controller
{
    public function checkoutHandle(Request $request)
    {
        if (setting("recapcha_enable") === "1" && $this->check_recaptcha($request->input("g-recaptcha-response"))) {

            return $this->responseAjax('Recaptcha verification failed!', true, [], 422);
        }

        $rules = [
            "webinar_id" => "required|exists:webinars,id",
            "advertisement_id" => "required|exists:advertisements,id",
            "order_type" => "required|in:qr,form",
            "name" => "required|string|max:255",
            "phone" => "required|string",
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return $this->responseAjax($validator->errors()->first(), true, [], 422);
        }
        $webinar = Webinar::find($request->input("webinar_id"));
        if (!$webinar) {
            return $this->responseAjax("Webinar không tồn tại!", true, [], 404);
        }
        $ads = Advertisement::find($request->input("advertisement_id"));
        if (!$ads) {
            return $this->responseAjax("Sản phẩm không tồn tại!", true, [], 404);
        }
        $price = (int)$this->getPrice($ads);

        $order_type = $request->input("order_type");
        $note = $request->input("payment_note") . "\n";
        $note .= setting("sepay_merchant_name") . "\n";
        $note .= setting("sepay_bank_code") . "\n";
        $note .= setting("sepay_account_number");
        $is_live = $request->input("is_live") ? true : false;
        $order = Order::create([
            "name" => $request->input("name"),
            "phone" => $request->input("phone"),
            "email" => $request->input("email"),
            "product_name" => @$ads->product->name,
            "price" => $price,
            "is_live" => $is_live,
            "order_type" => $order_type,
            "payment_note" => $note,
            "purchase_time" => Carbon::now(),
            "webinar_id" => $webinar->id,
            "payment_status" => "pending",
            "care_status" => "new",
            "is_priority" => 0,
        ]);

        $data = ["order_type" => $order_type];
        if ($order_type == "qr") {
            $transaction_note = $this->generateUniqueHash();
            $order->update([
                "transaction_note" => $transaction_note,
            ]);
            $data = ["transaction_note" => $transaction_note, "order_type" => $order_type];
        }
        event(new OrderCreatedEvent($order));
        return $this->responseAjax("Tạo đơn thành công!", false, $data, 200);
    }

    private function generateUniqueHash()
    {
        $hash = strtoupper(Str::random(12));

        while (Order::where('transaction_note', $hash)->exists()) {
            $hash = strtoupper(Str::random(12));
        }

        return $hash;
    }

    private function check_recaptcha($GRecaptchaResponse = "")
    {
        if (isset($GRecaptchaResponse)) {
            $url = 'https://www.google.com/recaptcha/api/siteverify';
            $secret = setting('recaptcha_secret_key');
            $data = array(
                'secret' => $secret,
                'response' => $GRecaptchaResponse
            );
            $query = http_build_query($data);
            $options = array(
                'http' => array(
                    'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                        "Content-Length: " . strlen($query) . "\r\n" .
                        "User-Agent:MyAgent/1.0\r\n",
                    'method' => 'POST',
                    'content' => $query
                )
            );
            $context = stream_context_create($options);
            $verify = file_get_contents($url, false, $context);
            $captcha_success = json_decode($verify);
            if ($captcha_success->success == false) {
                return false;
            } else if ($captcha_success->success == true) {
                return true;
            }
        } else {
            return false;
        }
    }

    public function checkPayment(Request $request)
    {
        $transaction_note = $request->get("transaction_note");
        if (empty($transaction_note)) {
            return $this->responseAjax("Mã không tồn tại!");
        }
        $order = Order::where([
            "transaction_note" => $transaction_note,
            "webinar_id" => $request->get("webinar_id"),
            "order_type" => "qr"
        ])->first();
        if (!$order) {
            return $this->responseAjax("Không tìm thấy đơn hàng!");
        }
        if ($order->payment_status == "paid") {
            return $this->responseAjax("ok");
        }
        if ($order->payment_status == "pending") {
            return $this->responseAjax("Đơn hàng có mã $transaction_note đang chờ duyệt");
        }
        return $this->responseAjax("Đơn hàng có mã $transaction_note thanh toán không thành công!");
    }

    private function getPrice($ads)
    {
        $price = $ads->sale_price;
        if ($price) {
            return $price;
        }
        $price = $ads->original_price;
        if ($price) {
            return $price;
        }
        $product = $ads->product;
        if (!$product) {
            return 0;
        }
        $price = $product->sale_price;
        if ($price) {
            return $price;
        }
        $price = $ads->original_price;
        if ($price) {
            return $price;
        }
        return 0;
    }


    private
    function responseAjax(
        $mess,
        $error = true,
        $data = [],
        $status = 200
    )
    {
        return response()->json([
            "error" => $error,
            "message" => $mess,
            'data' => $data
        ], $status);
    }
}
