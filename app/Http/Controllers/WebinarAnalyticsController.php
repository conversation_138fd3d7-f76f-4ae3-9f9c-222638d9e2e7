<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use App\Models\WebinarComment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class WebinarAnalyticsController extends Controller
{
    /**
     * Tạo controller instance mới
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Hiển thị trang phân tích webinar
     *
     * @param  \App\Models\Webinar  $webinar
     * @return \Illuminate\Http\Response
     */
    public function show(Webinar $webinar)
    {
        // Kiểm tra quyền truy cập
        if (!Auth::user()->hasRole('administrator') && Auth::id() !== $webinar->user_id) {
            abort(403, 'Bạn không có quyền truy cập phân tích của webinar này.');
        }

        // Thống kê tham gia
        $totalParticipants = $webinar->participants()->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');

        // Kiểm tra xem cột view_duration đã tồn tại trong bảng chưa
        $hasViewDuration = Schema::hasColumn('webinar_participants', 'view_duration');
        $hasDeviceType = Schema::hasColumn('webinar_participants', 'device_type');

        // Tính thời gian xem trung bình (nếu có cột view_duration)
        $avgViewDuration = 0;
        if ($hasViewDuration) {
            $avgViewDuration = $webinar->participants()
                ->whereNotNull('view_duration')
                ->avg('view_duration') ?? 0;
        }

        // Phân tích theo thiết bị (nếu có cột device_type)
        $deviceStats = [];
        if ($hasDeviceType) {
            $deviceStats = $webinar->participants()
                ->select('device_type', DB::raw('count(*) as count'))
                ->whereNotNull('device_type')
                ->groupBy('device_type')
                ->get()
                ->pluck('count', 'device_type')
                ->toArray();
        } else {
            // Mô phỏng dữ liệu khi chưa có cột device_type
            $deviceStats = [
                'desktop' => $totalParticipants > 0 ? intval($totalParticipants * 0.6) : 60,
                'mobile' => $totalParticipants > 0 ? intval($totalParticipants * 0.3) : 30,
                'tablet' => $totalParticipants > 0 ? intval($totalParticipants * 0.1) : 10,
            ];
        }

        // Thời gian tham gia phổ biến
        $joinTimeDistribution = $webinar->participants()
            ->select(DB::raw('HOUR(joined_at) as hour'), DB::raw('COUNT(*) as count'))
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        // Thống kê bình luận
        $totalComments = $webinar->comments()->count();
        $commentTimes = $webinar->comments()
            ->select(DB::raw('COUNT(*) as count'), 'video_timestamp')
            ->groupBy('video_timestamp')
            ->orderBy('video_timestamp')
            ->get();

        // Lấy dữ liệu quảng cáo
        $adClicks = [];
        if ($webinar->advertisement_slots) {
            // Lấy danh sách quảng cáo thông qua accessor
            $advertisements = $webinar->getAdvertisementsAttribute();

            foreach ($webinar->advertisement_slots as $index => $slot) {
                $adName = 'Quảng cáo ' . ($index + 1);

                // Kiểm tra an toàn trước khi truy cập dữ liệu quảng cáo
                if (isset($slot['advertisement_id']) && !empty($slot['advertisement_id'])) {
                    $advertisement = $advertisements->where('id', $slot['advertisement_id'])->first();
                    if ($advertisement && !empty($advertisement->name)) {
                        $adName = $advertisement->name;
                    }
                }

                $adClicks[] = [
                    'time' => $slot['time'] ?? "00:00:00",
                    'clicks' => mt_rand(5, 50), // Mô phỏng số liệu
                    'views' => mt_rand(50, 200), // Mô phỏng số liệu
                    'name' => $adName
                ];
            }
        }

        // Dữ liệu tham gia theo thời gian
        $registrationTrend = $this->getRegistrationTrend($webinar);

        // Mô phỏng dữ liệu bỏ học theo thời gian (giả sử đã có dữ liệu thực tế)
        $retentionData = $this->generateRetentionData();

        // Thống kê dữ liệu UTM
        $utmSourceStats = $this->getUtmSourceStats($webinar);
        $utmMediumStats = $this->getUtmMediumStats($webinar);
        $utmCampaignStats = $this->getUtmCampaignStats($webinar);

        return view('webinars.analytics', compact(
            'webinar',
            'totalParticipants',
            'uniqueParticipants',
            'avgViewDuration',
            'deviceStats',
            'joinTimeDistribution',
            'totalComments',
            'commentTimes',
            'adClicks',
            'registrationTrend',
            'retentionData',
            'utmSourceStats',
            'utmMediumStats',
            'utmCampaignStats'
        ));
    }

    /**
     * Lấy dữ liệu xu hướng đăng ký tham gia
     */
    private function getRegistrationTrend(Webinar $webinar)
    {
        $createdAt = Carbon::parse($webinar->created_at);
        $now = Carbon::now();
        $diffInDays = $createdAt->diffInDays($now);

        // Nếu webinar được tạo dưới 7 ngày, lấy dữ liệu theo giờ
        if ($diffInDays < 7) {
            $participants = $webinar->participants()
                ->select(DB::raw('DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00") as date'), DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', $createdAt)
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return [
                'labels' => $participants->pluck('date')->map(function($date) {
                    return Carbon::parse($date)->format('H:00 d/m');
                }),
                'data' => $participants->pluck('count'),
            ];
        }

        // Nếu trên 7 ngày, lấy dữ liệu theo ngày
        $participants = $webinar->participants()
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', $createdAt)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $participants->pluck('date')->map(function($date) {
                return Carbon::parse($date)->format('d/m/Y');
            }),
            'data' => $participants->pluck('count'),
        ];
    }

    /**
     * Tạo dữ liệu giữ chân người xem (mô phỏng)
     */
    private function generateRetentionData()
    {
        // Mô phỏng dữ liệu: 5 phút đầu, 15 phút, 30 phút, 45 phút, 60 phút
        $timePoints = ['5 phút', '15 phút', '30 phút', '45 phút', '60 phút'];

        // Bắt đầu với 100%, giảm dần
        $percentages = [100, mt_rand(75, 95), mt_rand(50, 75), mt_rand(30, 50), mt_rand(20, 30)];

        return [
            'labels' => $timePoints,
            'data' => $percentages
        ];
    }

    /**
     * Lấy dữ liệu cho API
     */
    public function getAnalyticsData(Webinar $webinar)
    {
        // Kiểm tra quyền truy cập
        if (!Auth::user()->hasRole('administrator') && Auth::id() !== $webinar->user_id) {
            return response()->json(['error' => 'Không có quyền truy cập'], 403);
        }

        // Kiểm tra xem cột view_duration đã tồn tại trong bảng chưa
        $hasViewDuration = Schema::hasColumn('webinar_participants', 'view_duration');

        // Thống kê tham gia
        $participantStats = [
            'total' => $webinar->participants()->count(),
            'unique' => $webinar->participants()->distinct('email')->count('email'),
            'avgDuration' => $hasViewDuration ?
                round($webinar->participants()->whereNotNull('view_duration')->avg('view_duration') ?? 0) : 0,
        ];

        return response()->json([
            'participant_stats' => $participantStats,
            'registration_trend' => $this->getRegistrationTrend($webinar),
            'retention_data' => $this->generateRetentionData(),
            'utm_source_stats' => $this->getUtmSourceStats($webinar),
            'utm_medium_stats' => $this->getUtmMediumStats($webinar),
            'utm_campaign_stats' => $this->getUtmCampaignStats($webinar),
        ]);
    }

    /**
     * Lấy thống kê UTM Source
     */
    private function getUtmSourceStats(Webinar $webinar)
    {
        $stats = $webinar->participants()
            ->select('utm_source', DB::raw('COUNT(*) as count'))
            ->whereNotNull('utm_source')
            ->groupBy('utm_source')
            ->orderBy('count', 'desc')
            ->get();

        // Nếu không có dữ liệu thì thêm "-" làm giá trị mặc định
        if ($stats->isEmpty()) {
            $stats->push(['utm_source' => 'Không xác định', 'count' => $webinar->participants()->whereNull('utm_source')->count()]);
        }

        return [
            'labels' => $stats->pluck('utm_source'),
            'data' => $stats->pluck('count'),
        ];
    }

    /**
     * Lấy thống kê UTM Medium
     */
    private function getUtmMediumStats(Webinar $webinar)
    {
        $stats = $webinar->participants()
            ->select('utm_medium', DB::raw('COUNT(*) as count'))
            ->whereNotNull('utm_medium')
            ->groupBy('utm_medium')
            ->orderBy('count', 'desc')
            ->get();

        // Nếu không có dữ liệu thì thêm "-" làm giá trị mặc định
        if ($stats->isEmpty()) {
            $stats->push(['utm_medium' => 'Không xác định', 'count' => $webinar->participants()->whereNull('utm_medium')->count()]);
        }

        return [
            'labels' => $stats->pluck('utm_medium'),
            'data' => $stats->pluck('count'),
        ];
    }

    /**
     * Lấy thống kê UTM Campaign
     */
    private function getUtmCampaignStats(Webinar $webinar)
    {
        $stats = $webinar->participants()
            ->select('utm_campaign', DB::raw('COUNT(*) as count'))
            ->whereNotNull('utm_campaign')
            ->groupBy('utm_campaign')
            ->orderBy('count', 'desc')
            ->get();

        // Nếu không có dữ liệu thì thêm "-" làm giá trị mặc định
        if ($stats->isEmpty()) {
            $stats->push(['utm_campaign' => 'Không xác định', 'count' => $webinar->participants()->whereNull('utm_campaign')->count()]);
        }

        return [
            'labels' => $stats->pluck('utm_campaign'),
            'data' => $stats->pluck('count'),
        ];
    }
}
