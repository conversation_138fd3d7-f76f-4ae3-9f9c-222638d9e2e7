<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Webinar;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole(['administrator', 'admin'])) {
                abort(403, 'Bạn không có quyền truy cập chức năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();
        $isAdmin = $user->hasRole('administrator');

        $totalWebinars = $isAdmin
            ? Webinar::count()
            : $user->webinars()->count();

        $upcomingWebinars = $isAdmin
            ? $this->getUpcomingWebinars()
            : $this->getUpcomingWebinars($user->id);

        $recentWebinars = $isAdmin
            ? Webinar::with('user')->latest()->take(5)->get()
            : $user->webinars()->latest()->take(5)->get();

        $webinarLimit = $user->webinar_limit;
        $webinarCount = $user->webinars()->count();
        $canCreateMore = $isAdmin || $webinarCount < $webinarLimit;

        // Calculate storage statistics
        $storageStats = $this->calculateStorageStats($isAdmin, $user);

        // Check storage limit status
        $storageLimitExceeded = $this->checkStorageLimitExceeded($storageStats);

        $allWebinars = Webinar::all();
        return view('home', compact(
            'totalWebinars',
            'upcomingWebinars',
            'recentWebinars',
            'webinarLimit',
            'webinarCount',
            'canCreateMore',
            'isAdmin',
            'allWebinars',
            'storageStats',
            'storageLimitExceeded'
        ));
    }

    /**
     * Get upcoming webinars.
     *
     * @param int|null $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getUpcomingWebinars($userId = null)
    {
        $now = Carbon::now();
        $webinarQuery = $userId ? Webinar::where('user_id', $userId) : Webinar::query();

        // Get webinars that have future schedules
        $webinars = $webinarQuery->get();

        $upcomingWebinars = $webinars->filter(function ($webinar) use ($now) {

            if (!$webinar->schedules || count($webinar->schedules) === 0) {
                return false;
            }

            // Check if any schedule is in the future
            foreach ($webinar->schedules as $schedule) {
                $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                if ($scheduledTime->isAfter($now)) {
                    return true;
                }
            }

            return false;
        });

        return $upcomingWebinars->take(5);
    }

    /**
     * Calculate storage statistics.
     *
     * @param bool $isAdmin
     * @param \App\Models\User $user
     * @return array
     */
    private function calculateStorageStats($isAdmin, $user)
    {
        // Calculate total video storage from webinars
        $videoStorageQuery = $isAdmin
            ? Webinar::whereNotNull('video_file_size')
            : $user->webinars()->whereNotNull('video_file_size');

        $totalVideoStorage = $videoStorageQuery->sum('video_file_size') ?? 0;

        // Calculate website storage (code + database)
        $websiteStorage = $this->calculateWebsiteStorage();

        return [
            'total_video_storage' => $totalVideoStorage,
            'website_storage' => $websiteStorage,
            'video_count' => $videoStorageQuery->count()
        ];
    }

    /**
     * Calculate total website storage (code + database).
     *
     * @return int
     */
    private function calculateWebsiteStorage()
    {
        try {
            $totalSize = 0;

            // Calculate code size (excluding storage and vendor folders)
            $basePath = base_path();
            $excludePaths = [
                'storage/app/public',
                'storage/logs',
                'vendor',
                'node_modules',
                '.git'
            ];

            $totalSize += $this->getDirectorySize($basePath, $excludePaths);

            // Calculate database size
            $databaseSize = $this->getDatabaseSize();
            $totalSize += $databaseSize;

            return $totalSize;
        } catch (\Exception $e) {
            // Return 0 if calculation fails
            return 0;
        }
    }

    /**
     * Get directory size excluding specified paths.
     *
     * @param string $directory
     * @param array $excludePaths
     * @return int
     */
    private function getDirectorySize($directory, $excludePaths = [])
    {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $filePath = $file->getPathname();
                    $relativePath = str_replace($directory . DIRECTORY_SEPARATOR, '', $filePath);

                    // Check if file is in excluded paths
                    $isExcluded = false;
                    foreach ($excludePaths as $excludePath) {
                        if (strpos($relativePath, $excludePath) === 0) {
                            $isExcluded = true;
                            break;
                        }
                    }

                    if (!$isExcluded) {
                        $size += $file->getSize();
                    }
                }
            }
        } catch (\Exception $e) {
            // If directory scanning fails, return 0
            return 0;
        }

        return $size;
    }

    /**
     * Get database size.
     *
     * @return int
     */
    private function getDatabaseSize()
    {
        try {
            $databaseName = config('database.connections.mysql.database');

            $result = DB::select("
                SELECT
                    SUM(data_length + index_length) as size
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [$databaseName]);

            return $result[0]->size ?? 0;
        } catch (\Exception $e) {
            // If database size calculation fails, return 0
            return 0;
        }
    }

    /**
     * Check if storage limit is exceeded.
     *
     * @param array $storageStats
     * @return array
     */
    private function checkStorageLimitExceeded($storageStats)
    {
        try {
            // Check if storage limit checking is enabled
            $limitEnabled = \App\Models\Setting::get('storage_limit_enabled', '1') === '1';

            if (!$limitEnabled) {
                return [
                    'exceeded' => false,
                    'message' => null,
                    'video_exceeded' => false,
                    'system_exceeded' => false
                ];
            }

            // Get limits from settings (in GB)
            $videoLimitGB = (float) \App\Models\Setting::get('video_storage_limit_gb', 10);
            $systemLimitGB = (float) \App\Models\Setting::get('system_storage_limit_gb', 5);

            // Convert current usage to GB
            $currentVideoGB = $storageStats['total_video_storage'] / (1024 * 1024 * 1024);
            $currentSystemGB = $storageStats['website_storage'] / (1024 * 1024 * 1024);

            // Check if either limit is exceeded
            $videoExceeded = $currentVideoGB > $videoLimitGB;
            $systemExceeded = $currentSystemGB > $systemLimitGB;
            $anyExceeded = $videoExceeded || $systemExceeded;

            $message = null;
            if ($anyExceeded) {
                $message = \App\Models\Setting::get('storage_limit_message',
                    'Hệ thống đã vượt quá giới hạn dung lượng cho phép. Vui lòng liên hệ admin để nâng cấp dung lượng.');
            }

            return [
                'exceeded' => $anyExceeded,
                'message' => $message,
                'video_exceeded' => $videoExceeded,
                'system_exceeded' => $systemExceeded,
                'video_usage_gb' => round($currentVideoGB, 2),
                'system_usage_gb' => round($currentSystemGB, 2),
                'video_limit_gb' => $videoLimitGB,
                'system_limit_gb' => $systemLimitGB
            ];
        } catch (\Exception $e) {
            // If calculation fails, don't show error
            return [
                'exceeded' => false,
                'message' => null,
                'video_exceeded' => false,
                'system_exceeded' => false
            ];
        }
    }
}
