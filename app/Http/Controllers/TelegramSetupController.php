<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\SiteSetting;
use App\Jobs\SendTelegramNotification;
use App\Services\TelegramApiService;

class TelegramSetupController extends Controller
{
    /**
     * Verify the Telegram bot token and return bot information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyToken(Request $request)
    {
        $token = $request->input('token');

        if (empty($token)) {
            return response()->json([
                'success' => false,
                'message' => 'Token là bắt buộc'
            ], 400);
        }

        try {
            // Call the Telegram API to get the bot info
            $response = Http::get(TelegramApiService::getGetMeUrl($token));

            if ($response->successful()) {
                $botData = $response->json();

                if (isset($botData['ok']) && $botData['ok'] === true && isset($botData['result'])) {
                    $bot = $botData['result'];

                    // Store the token in the settings (encrypted)
                    $this->updateSetting('telegram_bot_token', $token);
                    $this->updateSetting('telegram_enabled', '1');

                    // Store bot username for later use
                    $this->updateSetting('telegram_bot_username', $bot['username'] ?? '');

                    // Store bot name
                    $botName = $bot['first_name'] ?? 'Telegram Bot';
                    $this->updateSetting('telegram_bot_name', $botName);

                    // Clear all pending updates to start fresh
                    $this->clearBotUpdates($token);

                    return response()->json([
                        'success' => true,
                        'botName' => $botName,
                        'botUsername' => $bot['username'] ?? ''
                    ]);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'Token không hợp lệ hoặc bot không hoạt động'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Telegram API Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get chat IDs from new messages sent to the bot
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChatIds()
    {
        $token = $this->getSetting('telegram_bot_token');

        if (empty($token)) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa thiết lập bot token'
            ], 400);
        }

        try {

            // Get update messages from the Telegram API - use a time-based approach
            // instead of the offset approach to get all messages from the last hour
            $response = Http::get(TelegramApiService::getGetUpdatesUrl($token), [
                'timeout' => 10,
                'limit' => 10 // Increased limit to get more messages
            ]);

            if ($response->successful()) {
                $updates = $response->json();

                $chatUsers = [];
                $oneHourAgo = time() - 3600; // One hour ago

                if (isset($updates['ok']) && $updates['ok'] === true && !empty($updates['result'])) {
                    foreach ($updates['result'] as $update) {
                        // Skip messages older than 1 hour
                        if (!isset($update['message']['date']) || $update['message']['date'] < $oneHourAgo) {
                            continue;
                        }

                        if (isset($update['message']['chat']['id'])) {
                            $chatId = (string)$update['message']['chat']['id'];
                            $username = $update['message']['chat']['username'] ?? null;
                            $firstName = $update['message']['chat']['first_name'] ?? '';
                            $lastName = $update['message']['chat']['last_name'] ?? '';
                            $fullName = trim("{$firstName} {$lastName}");

                            // Skip duplicates
                            if (!isset($chatUsers[$chatId])) {
                                $chatUsers[$chatId] = [
                                    'id' => $chatId,
                                    'username' => $username,
                                    'name' => $fullName ?: ($username ?: 'Người dùng không tên'),
                                    'time' => isset($update['message']['date']) ? date('d/m/Y H:i', $update['message']['date']) : null
                                ];
                            }
                        }
                    }
                }

                // Get existing chat IDs
                $existingChatIdsStr = $this->getSetting('telegram_chat_ids');
                $existingChatIds = !empty($existingChatIdsStr)
                    ? array_map('trim', explode("\n", $existingChatIdsStr))
                    : [];

                // Get stored chat user data
                $storedChatUsersJson = $this->getSetting('telegram_chat_users');
                $storedChatUsers = !empty($storedChatUsersJson)
                    ? json_decode($storedChatUsersJson, true)
                    : [];

                // Add existing IDs to the list
                foreach ($existingChatIds as $existingChatId) {
                    if (!empty($existingChatId)) {
                        $existingChatId = (string)$existingChatId;

                        if (!isset($chatUsers[$existingChatId])) {
                            // If we have stored user data, use that
                            if (isset($storedChatUsers[$existingChatId])) {
                                $userData = $storedChatUsers[$existingChatId];
                                $chatUsers[$existingChatId] = [
                                    'id' => $existingChatId,
                                    'username' => $userData['username'] ?? null,
                                    'name' => $userData['name'] ?? ('Chat ID ' . $existingChatId),
                                    'selected' => true,
                                    'time' => $userData['time'] ?? null
                                ];
                            } else {
                                // Fallback to basic info
                                $chatUsers[$existingChatId] = [
                                    'id' => $existingChatId,
                                    'username' => null,
                                    'name' => 'Chat ID ' . $existingChatId,
                                    'selected' => true,
                                    'time' => null
                                ];
                            }
                        } else if (isset($chatUsers[$existingChatId])) {
                            // Mark existing users as selected
                            $chatUsers[$existingChatId]['selected'] = true;
                        }
                    }
                }

                return response()->json([
                    'success' => true,
                    'chatIds' => $existingChatIds, // Compatibility with old code
                    'chatUsers' => array_values($chatUsers)
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy tin nhắn từ bot'
            ]);

        } catch (\Exception $e) {
            Log::error('Telegram API Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test sending a message to all configured chat IDs
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection(Request $request)
    {
        $token = $this->getSetting('telegram_bot_token');
        $chatIds = $this->getSetting('telegram_chat_ids');

        if (empty($token) || empty($chatIds)) {
            return response()->json([
                'success' => false,
                'message' => 'Thiếu thông tin bot token hoặc chat ID'
            ], 400);
        }

        $chatIdsArray = array_map('trim', explode("\n", $chatIds));
        $successCount = 0;
        $failedChatIds = [];

        // Get stored chat user data
        $storedChatUsersJson = $this->getSetting('telegram_chat_users');
        $storedChatUsers = !empty($storedChatUsersJson)
            ? json_decode($storedChatUsersJson, true)
            : [];

        foreach ($chatIdsArray as $chatId) {
            if (empty($chatId)) continue;

            try {
                // Get user data if available
                $userData = $storedChatUsers[$chatId] ?? null;
                $userName = $userData['name'] ?? null;

                // Create personalized message
                $message = '✅ Kết nối thành công với hệ thống quản lý webinar!';

                if ($userName) {
                    $message = "✅ Xin chào *{$userName}*!\n\n" . $message;
                }

                // Dispatch job to queue instead of sending directly
                \App\Jobs\SendTelegramNotification::dispatch($token, $chatId, $message);
                $successCount++;
            } catch (\Exception $e) {
                Log::error("Telegram send error for chat ID {$chatId}: " . $e->getMessage());
                $failedChatIds[] = $chatId;
            }
        }

        if ($successCount > 0) {
            return response()->json([
                'success' => true,
                'message' => "Đã gửi tin nhắn test đến {$successCount} người nhận",
                'failedChatIds' => $failedChatIds
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Không thể gửi tin nhắn đến bất kỳ người nhận nào',
                'failedChatIds' => $failedChatIds
            ], 400);
        }
    }

    /**
     * Helper to clear pending bot updates
     */
    private function clearBotUpdates($token)
    {
        try {
            // Get the current update ID
            $getUpdatesResponse = Http::get(TelegramApiService::getGetUpdatesUrl($token), [
                'offset' => 0,
                'limit' => 1
            ]);

            if ($getUpdatesResponse->successful() && $getUpdatesResponse['ok'] === true) {
                $updates = $getUpdatesResponse->json();

                if (!empty($updates['result'])) {
                    $lastUpdateId = $updates['result'][0]['update_id'];

                    // Clear updates by setting the offset to the last update ID + 1
                    Http::get(TelegramApiService::getGetUpdatesUrl($token), [
                        'offset' => $lastUpdateId + 1
                    ]);

                    // Store the offset for future updates
                    Cache::put('telegram_update_offset', $lastUpdateId + 1, 3600 * 24);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error clearing Telegram updates: ' . $e->getMessage());
        }
    }

    /**
     * Helper to get a setting value
     */
    private function getSetting($key)
    {
        $setting = SiteSetting::where('key', $key)->first();
        return $setting ? $setting->value : null;
    }

    /**
     * Helper to update a setting value
     */
    private function updateSetting($key, $value)
    {

        // Ensure boolean values are properly converted
        if ($value === 'true' || $value === true) {
            $value = '1';
        } elseif ($value === 'false' || $value === false) {
            $value = '0';
        }

        // Update in SiteSetting model
        SiteSetting::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'group' => 'notifications'
            ]
        );

        // Also store in cache to ensure it's available immediately
        Cache::put('site_settings.'.$key, $value, now()->addDay());
    }

    /**
     * Save selected chat IDs
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveChatIds(Request $request)
    {
        try {
            $validated = $request->validate([
                'chatIds' => 'present|array', // Present allows empty arrays
                'chatUsers' => 'array'
            ]);

            $chatIds = $validated['chatIds'] ?? [];

            // Store the selected chat IDs (for backwards compatibility)
            $this->updateSetting('telegram_chat_ids', empty($chatIds) ? '' : implode("\n", $chatIds));

            // Store detailed chat user information as JSON if provided
            if (isset($validated['chatUsers']) && !empty($validated['chatUsers'])) {
                // Convert to associative array with chat IDs as keys
                $chatUserData = [];
                foreach ($validated['chatUsers'] as $user) {
                    if (isset($user['id']) && (!empty($chatIds) || count($chatIds) === 0 || in_array($user['id'], $chatIds))) {
                        $chatUserData[$user['id']] = [
                            'id' => $user['id'],
                            'name' => $user['name'] ?? ('Chat ID ' . $user['id']),
                            'username' => $user['username'] ?? null,
                            'time' => $user['time'] ?? null
                        ];
                    }
                }

                $this->updateSetting('telegram_chat_users', json_encode($chatUserData, JSON_UNESCAPED_UNICODE));
            } else {
                // If no users provided, clear the data
                $this->updateSetting('telegram_chat_users', '{}');
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã lưu danh sách người nhận thông báo'
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving chat IDs: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a test message to a specific chat
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testSendToChatId(Request $request)
    {
        try {
            // Ensure chat_id is validated as a string
            $validated = $request->validate([
                'chat_id' => 'required',
            ]);

            $token = $this->getSetting('telegram_bot_token');
            $chatId = (string)$validated['chat_id']; // Force conversion to string

            if (empty($token)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chưa thiết lập bot token'
                ], 400);
            }

            // Get user data if available
            $storedChatUsersJson = $this->getSetting('telegram_chat_users');
            $storedChatUsers = !empty($storedChatUsersJson)
                ? json_decode($storedChatUsersJson, true)
                : [];

            $userData = $storedChatUsers[$chatId] ?? null;
            $userName = $userData['name'] ?? null;

            // Test message content
            $message = "🔔 *Tin nhắn kiểm tra*\n\n";

            if ($userName) {
                $message .= "Xin chào *{$userName}*!\n\n";
            }

            $message .= "Đây là tin nhắn thử nghiệm từ hệ thống quản lý webinar.\n";
            $message .= "Nếu bạn nhận được tin nhắn này, cài đặt Telegram của bạn đã hoạt động.\n\n";
            $message .= "🕒 Thời gian: " . now()->format('d/m/Y H:i:s');

            // Dispatch job to queue instead of sending directly
            \App\Jobs\SendTelegramNotification::dispatchSync($token, $chatId, $message);

            return response()->json([
                'success' => true,
                'message' => 'Đã gửi tin nhắn test thành công (đang trong hàng đợi)'
            ]);

        } catch (\Exception $e) {
            Log::error('Telegram API Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save Telegram notification settings
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveNotificationSettings(Request $request)
    {
        try {
            // Validate settings
            $validated = $request->validate([
                'telegram_notify_participants' => 'required|in:0,1',
                'telegram_notify_comments' => 'required|in:0,1',
                'telegram_enabled' => 'required|in:0,1',
            ]);

            // Update each setting
            foreach ($validated as $key => $value) {
                // Update in SiteSetting model (used by this controller)
                $this->updateSetting($key, $value);

                // Also update in the Setting model if it exists (used by main settings page)
                if (class_exists('\\App\\Models\\Setting')) {
                    \App\Models\Setting::set($key, $value);
                }
            }

            // Clear settings cache
            if (class_exists('\\App\\Models\\Setting')) {
                \App\Models\Setting::clearCache();
            }

            // Force settings to be visible in session for immediate feedback
            if (session()->has('settings')) {
                $settings = session()->get('settings', []);
                foreach ($validated as $key => $value) {
                    $settings[$key] = $value;
                }
                session()->put('settings', $settings);
            }

            return response()->json([
                'success' => true,
                'message' => 'Cài đặt thông báo đã được lưu'
            ]);

        } catch (\Exception $e) {
            Log::error('Error saving notification settings: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }
}
