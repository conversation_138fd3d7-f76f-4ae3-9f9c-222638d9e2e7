<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class ModuleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->can('settings')) {
                abort(403, 'Bạn không có quyền truy cập tính năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display modules management page
     */
    public function index()
    {
        $categories = Module::getCategories();
        $modules = Module::with([])->orderBy('category')->orderBy('sort_order')->get();
        
        return view('admin.modules.index', compact('modules', 'categories'));
    }

    /**
     * Toggle module status
     */
    public function toggle(Request $request, Module $module)
    {
        if ($module->is_core && $request->action === 'disable') {
            return response()->json([
                'success' => false,
                'message' => 'Không thể tắt module cốt lõi của hệ thống.'
            ]);
        }

        $module->is_enabled = !$module->is_enabled;
        $module->save();
        
        Module::clearCache();

        $status = $module->is_enabled ? 'kích hoạt' : 'tắt';
        
        return response()->json([
            'success' => true,
            'message' => "Module {$module->display_name} đã được {$status}.",
            'is_enabled' => $module->is_enabled
        ]);
    }

    /**
     * Update module configuration
     */
    public function updateConfig(Request $request, Module $module)
    {
        $config = $request->input('config', []);
        $module->config = $config;
        $module->save();
        
        Module::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Cấu hình module đã được cập nhật.'
        ]);
    }

    /**
     * Update module sort order
     */
    public function updateOrder(Request $request)
    {
        $orders = $request->input('orders', []);
        
        foreach ($orders as $order) {
            Module::where('id', $order['id'])->update(['sort_order' => $order['position']]);
        }
        
        Module::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Thứ tự module đã được cập nhật.'
        ]);
    }

    /**
     * Install default modules
     */
    public function installDefaults()
    {
        $defaultModules = [
            [
                'name' => 'analytics',
                'display_name' => 'Thống kê & Báo cáo',
                'description' => 'Module thống kê chi tiết về webinar, người dùng và doanh thu',
                'icon' => 'fas fa-chart-line',
                'route' => 'analytics.index',
                'permission' => 'analytics view',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 10,
                'category' => 'reporting',
                'config' => [
                    'show_revenue' => true,
                    'show_users' => true,
                    'show_webinars' => true
                ]
            ],
            [
                'name' => 'notifications',
                'display_name' => 'Thông báo nâng cao',
                'description' => 'Hệ thống thông báo email, SMS và push notification',
                'icon' => 'fas fa-bell',
                'route' => 'notifications.index',
                'permission' => 'notifications manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 20,
                'category' => 'communication',
                'config' => [
                    'email_enabled' => true,
                    'sms_enabled' => false,
                    'push_enabled' => false
                ]
            ],
            [
                'name' => 'backup',
                'display_name' => 'Sao lưu dữ liệu',
                'description' => 'Tự động sao lưu database và files quan trọng',
                'icon' => 'fas fa-database',
                'route' => 'backup.index',
                'permission' => 'backup manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 30,
                'category' => 'system',
                'config' => [
                    'auto_backup' => false,
                    'backup_frequency' => 'daily',
                    'keep_backups' => 7
                ]
            ],
            [
                'name' => 'social_login',
                'display_name' => 'Đăng nhập mạng xã hội',
                'description' => 'Cho phép đăng nhập bằng Facebook, Google, Twitter',
                'icon' => 'fas fa-share-alt',
                'route' => 'social.settings',
                'permission' => 'social login manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 40,
                'category' => 'authentication',
                'config' => [
                    'facebook_enabled' => false,
                    'google_enabled' => false,
                    'twitter_enabled' => false
                ]
            ],
            [
                'name' => 'live_chat',
                'display_name' => 'Chat trực tiếp',
                'description' => 'Hệ thống chat realtime trong webinar',
                'icon' => 'fas fa-comments',
                'route' => 'chat.settings',
                'permission' => 'chat manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 50,
                'category' => 'communication',
                'config' => [
                    'auto_moderation' => true,
                    'file_sharing' => false,
                    'emoji_enabled' => true
                ]
            ],
            [
                'name' => 'payment_gateway',
                'display_name' => 'Cổng thanh toán',
                'description' => 'Tích hợp các cổng thanh toán VNPay, MoMo, ZaloPay',
                'icon' => 'fas fa-credit-card',
                'route' => 'payments.settings',
                'permission' => 'payments manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 60,
                'category' => 'ecommerce',
                'config' => [
                    'vnpay_enabled' => false,
                    'momo_enabled' => false,
                    'zalopay_enabled' => false
                ]
            ],
            [
                'name' => 'seo_tools',
                'display_name' => 'Công cụ SEO',
                'description' => 'Tối ưu hóa SEO cho webinar và trang web',
                'icon' => 'fas fa-search',
                'route' => 'seo.settings',
                'permission' => 'seo manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 70,
                'category' => 'marketing',
                'config' => [
                    'auto_sitemap' => true,
                    'meta_optimization' => true,
                    'schema_markup' => true
                ]
            ],
            [
                'name' => 'api_management',
                'display_name' => 'Quản lý API',
                'description' => 'Quản lý API keys và webhook endpoints',
                'icon' => 'fas fa-code',
                'route' => 'api.settings',
                'permission' => 'api manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 80,
                'category' => 'development',
                'config' => [
                    'rate_limiting' => true,
                    'api_documentation' => true,
                    'webhook_enabled' => false
                ]
            ]
        ];

        foreach ($defaultModules as $moduleData) {
            Module::updateOrCreate(
                ['name' => $moduleData['name']],
                $moduleData
            );
        }

        Module::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Đã cài đặt thành công ' . count($defaultModules) . ' module mặc định.'
        ]);
    }
}
