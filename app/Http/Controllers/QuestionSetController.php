<?php

namespace App\Http\Controllers;

use App\Models\QuestionSet;
use App\Models\Webinar;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use RealRashid\SweetAlert\Facades\Alert;

class QuestionSetController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (auth()->user()->can('questions index')) {
            $query = QuestionSet::with(['webinar', 'questions']);
        } elseif (auth()->user()->can('questions view only you')) {
            $query = QuestionSet::with(['webinar', 'questions'])
                ->whereHas('webinar', function($q) {
                    $q->where('user_id', auth()->id());
                });
        } else {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        // Filter by search
        if (request('search')) {
            $query->where('name', 'like', '%' . request('search') . '%');
        }

        // Filter by webinar
        if (request('webinar_id')) {
            $query->where('webinar_id', request('webinar_id'));
        }

        // Filter by status
        if (request('status') !== null && request('status') !== '') {
            $query->where('status', request('status'));
        }

        $questionSets = $query->orderBy('created_at', 'desc')->paginate(15);
        $webinars = Webinar::all();

        return view('question-sets.index', compact('questionSets', 'webinars'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->can('questions create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $webinars = Webinar::all();

        if ($webinars->isEmpty()) {
            Alert::error('Lỗi!', 'Vui lòng tạo webinar trước khi tạo bộ câu hỏi!');
            return redirect()->route('webinars.index');
        }

        return view('question-sets.create', compact('webinars'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('questions create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'webinar_id' => 'required|exists:webinars,id',
            'status' => 'nullable|in:on,1,true,0,false',
            'description' => 'nullable|string',
            'questions' => 'required|array|min:1',
            'questions.*.title' => 'required|string',
            'questions.*.description' => 'nullable|string',
            'questions.*.type' => 'required|in:multiple-choice,single-choice,rating,open-survey',
            'questions.*.answers' => 'sometimes|array',
            'questions.*.answers.*' => 'sometimes|string',
        ], [
            'name.required' => 'Tên bộ câu hỏi là bắt buộc.',
            'webinar_id.required' => 'Vui lòng chọn webinar.',
            'webinar_id.exists' => 'Webinar không tồn tại.',
            'questions.required' => 'Vui lòng thêm ít nhất một câu hỏi.',
            'questions.min' => 'Bộ câu hỏi phải có ít nhất một câu hỏi.',
            'questions.*.title.required' => 'Tiêu đề câu hỏi là bắt buộc.',
            'questions.*.type.required' => 'Loại câu hỏi là bắt buộc.',
            'questions.*.type.in' => 'Loại câu hỏi không hợp lệ.',
            'questions.*.answers.required' => 'Đáp án là bắt buộc.',
            'questions.*.answers.min' => 'Mỗi câu hỏi phải có ít nhất một đáp án.',
            'questions.*.answers.*.required' => 'Nội dung đáp án không được để trống.',
        ]);

        // Custom validation for questions with answers
        foreach ($request->questions as $index => $questionData) {
            if ($questionData['type'] !== 'open-survey') {
                if (empty($questionData['answers']) || count($questionData['answers']) < 1) {
                    return redirect()->back()
                        ->withInput()
                        ->withErrors(["questions.{$index}.answers" => "Loại câu hỏi này cần có ít nhất một đáp án."]);
                }
            }
        }

        try {
            DB::transaction(function () use ($request) {
                // Create question set
                $questionSet = QuestionSet::create([
                    'name' => $request->name,
                    'webinar_id' => $request->webinar_id,
                    'status' => $request->boolean('status'),
                    'description' => $request->description,
                ]);

                // Create questions - all questions default to active
                foreach ($request->questions as $questionData) {
                    // Process answers based on question type
                    $answers = [];
                    if ($questionData['type'] !== 'open-survey' && isset($questionData['answers']) && !empty($questionData['answers'])) {
                        foreach ($questionData['answers'] as $index => $answerText) {
                            if (!empty(trim($answerText))) {
                                $answers[] = [
                                    'label' => trim($answerText),
                                    'value' => 'option_' . ($index + 1)
                                ];
                            }
                        }
                    }

                    Question::create([
                        'question_set_id' => $questionSet->id,
                        'webinar_id' => $questionSet->webinar_id,
                        'title' => $questionData['title'],
                        'description' => $questionData['description'] ?? null,
                        'type' => $questionData['type'],
                        'answers' => $answers,
                        'status' => true, // Default all questions to active
                    ]);
                }
            });

            Alert::success('Thành công!', 'Bộ câu hỏi đã được tạo thành công.');
            return redirect()->route('question-sets.index');

        } catch (\Exception $e) {
            \Log::error('QuestionSet Store Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            Alert::error('Lỗi!', 'Có lỗi xảy ra khi tạo bộ câu hỏi: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(QuestionSet $questionSet)
    {
        $questionSet->load(['webinar', 'questions.responses']);
        return view('question-sets.show', compact('questionSet'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(QuestionSet $questionSet)
    {
        if (!auth()->user()->can('questions update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $webinars = Webinar::all();
        $questionSet->load('questions');

        return view('question-sets.edit', compact('questionSet', 'webinars'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, QuestionSet $questionSet)
    {
        if (!auth()->user()->can('questions update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'webinar_id' => 'required|exists:webinars,id',
            'status' => 'nullable|in:on,1,true,0,false',
            'description' => 'nullable|string',
            'questions' => 'required|array|min:1',
            'questions.*.title' => 'required|string',
            'questions.*.description' => 'nullable|string',
            'questions.*.type' => 'required|in:multiple-choice,single-choice,rating,open-survey',
            'questions.*.answers' => 'sometimes|array',
            'questions.*.answers.*' => 'sometimes|string',
        ], [
            'name.required' => 'Tên bộ câu hỏi là bắt buộc.',
            'webinar_id.required' => 'Vui lòng chọn webinar.',
            'webinar_id.exists' => 'Webinar không tồn tại.',
            'questions.required' => 'Vui lòng thêm ít nhất một câu hỏi.',
            'questions.min' => 'Bộ câu hỏi phải có ít nhất một câu hỏi.',
            'questions.*.title.required' => 'Tiêu đề câu hỏi là bắt buộc.',
            'questions.*.type.required' => 'Loại câu hỏi là bắt buộc.',
            'questions.*.type.in' => 'Loại câu hỏi không hợp lệ.',
            'questions.*.answers.required' => 'Đáp án là bắt buộc.',
            'questions.*.answers.min' => 'Mỗi câu hỏi phải có ít nhất một đáp án.',
            'questions.*.answers.*.required' => 'Nội dung đáp án không được để trống.',
        ]);

        // Custom validation for questions with answers
        foreach ($request->questions as $index => $questionData) {
            if ($questionData['type'] !== 'open-survey') {
                if (empty($questionData['answers']) || count($questionData['answers']) < 1) {
                    return redirect()->back()
                        ->withInput()
                        ->withErrors(["questions.{$index}.answers" => "Loại câu hỏi này cần có ít nhất một đáp án."]);
                }
            }
        }

        try {
            DB::transaction(function () use ($request, $questionSet) {
                // Update question set
                $questionSet->update([
                    'name' => $request->name,
                    'webinar_id' => $request->webinar_id,
                    'status' => $request->boolean('status'),
                    'description' => $request->description,
                ]);

                // Delete existing questions
                $questionSet->questions()->delete();

                // Create new questions
                foreach ($request->questions as $questionData) {
                    // Process answers based on question type
                    $answers = [];
                    if ($questionData['type'] !== 'open-survey' && isset($questionData['answers']) && !empty($questionData['answers'])) {
                        foreach ($questionData['answers'] as $index => $answerText) {
                            if (!empty(trim($answerText))) {
                                $answers[] = [
                                    'label' => trim($answerText),
                                    'value' => 'option_' . ($index + 1)
                                ];
                            }
                        }
                    }

                    Question::create([
                        'question_set_id' => $questionSet->id,
                        'webinar_id' => $questionSet->webinar_id,
                        'title' => $questionData['title'],
                        'description' => $questionData['description'] ?? null,
                        'type' => $questionData['type'],
                        'answers' => $answers,
                        'status' => true, // Default all questions to active
                    ]);
                }
            });

            Alert::success('Thành công!', 'Bộ câu hỏi đã được cập nhật thành công.');
            return redirect()->route('question-sets.index');

        } catch (\Exception $e) {
            \Log::error('QuestionSet Update Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            Alert::error('Lỗi!', 'Có lỗi xảy ra khi cập nhật bộ câu hỏi: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(QuestionSet $questionSet)
    {
        if (!auth()->user()->can('questions delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        try {
            // Check if any questions have responses
            $hasResponses = $questionSet->questions()->whereHas('responses')->exists();

            if ($hasResponses) {
                Alert::error('Không thể xóa!', 'Không thể xóa bộ câu hỏi đã có người trả lời!');
                return redirect()->route('question-sets.index');
            }

            $questionSet->delete(); // This will cascade delete questions too

            Alert::success('Thành công!', 'Bộ câu hỏi đã được xóa thành công!');
            return redirect()->route('question-sets.index');

        } catch (\Exception $e) {
            Alert::error('Lỗi!', 'Có lỗi xảy ra khi xóa bộ câu hỏi!');
            return redirect()->route('question-sets.index');
        }
    }

    /**
     * Toggle status of a question set
     */
    public function toggleStatus(QuestionSet $questionSet)
    {
        if (!auth()->user()->can('questions update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        try {
            $questionSet->update(['status' => !$questionSet->status]);

            $message = $questionSet->status ? 'Đã kích hoạt bộ câu hỏi' : 'Đã tạm dừng bộ câu hỏi';

            return response()->json([
                'success' => true,
                'status' => $questionSet->status,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật trạng thái'
            ], 500);
        }
    }

    /**
     * Get questions by question set ID for AJAX calls
     */
    public function getQuestions(QuestionSet $questionSet)
    {
        try {
            $questions = $questionSet->questions()
                ->where('status', true)
                ->get();

            return response()->json([
                'success' => true,
                'questions' => $questions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy danh sách câu hỏi'
            ], 500);
        }
    }
}
