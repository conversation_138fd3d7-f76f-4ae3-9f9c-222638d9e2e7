<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Question;

class UpdateQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $question = $this->route('question');

        // Ensure we have a Question model instance
        if (!$question instanceof Question) {
            $question = Question::find($question);
        }

        return [
            'webinar_id' => [
                'required',
                'exists:webinars,id',
                function ($attribute, $value, $fail) use ($question) {
                    if ($question && $question->responses()->count() > 0 && $question->webinar_id != $value) {
                        $fail('Không thể chuyển câu hỏi sang webinar khác khi đã có phản hồi.');
                    }
                }
            ],
            'title' => 'required|string|max:1000|min:5',
            'type' => [
                'required',
                'in:multiple-choice,single-choice,rating,open-survey',
                function ($attribute, $value, $fail) use ($question) {
                    if ($question && $question->responses()->count() > 0 && $question->type !== $value) {
                        $fail("Không thể thay đổi loại câu hỏi khi đã có {$question->responses()->count()} phản hồi.");
                    }
                }
            ],
            'answers' => [
                'required',
                'array',
                'min:1',
                function ($attribute, $value, $fail) {
                    $this->validateAnswersForType($this->input('type'), $value, $fail);
                }
            ],
            'answers.*' => 'required|string|max:500|min:1',
            'status' => 'nullable|in:on,1,true,0,false'
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'webinar_id.required' => 'Vui lòng chọn webinar.',
            'webinar_id.exists' => 'Webinar được chọn không tồn tại.',
            'title.required' => 'Vui lòng nhập tiêu đề câu hỏi.',
            'title.min' => 'Tiêu đề câu hỏi phải có ít nhất 5 ký tự.',
            'title.max' => 'Tiêu đề câu hỏi không được vượt quá 1000 ký tự.',
            'type.required' => 'Vui lòng chọn loại câu hỏi.',
            'type.in' => 'Loại câu hỏi không hợp lệ.',
            'answers.required' => 'Vui lòng thêm ít nhất một lựa chọn.',
            'answers.array' => 'Dữ liệu lựa chọn không hợp lệ.',
            'answers.min' => 'Phải có ít nhất một lựa chọn.',
            'answers.*.required' => 'Tất cả lựa chọn phải có nội dung.',
            'answers.*.min' => 'Mỗi lựa chọn phải có ít nhất 1 ký tự.',
            'answers.*.max' => 'Mỗi lựa chọn không được vượt quá 500 ký tự.',
            'status.in' => 'Giá trị trạng thái không hợp lệ.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'webinar_id' => 'webinar',
            'title' => 'tiêu đề câu hỏi',
            'type' => 'loại câu hỏi',
            'answers' => 'lựa chọn',
            'answers.*' => 'lựa chọn',
            'status' => 'trạng thái'
        ];
    }

    /**
     * Get the validated data with processed answers
     */
    public function getValidatedData(): array
    {
        $validated = $this->validated();

        // Process answers based on question type
        $validated['answers'] = $this->processAnswers($validated['type'], $validated['answers']);

        // Clean title
        $validated['title'] = trim($validated['title']);

        // Handle status - convert checkbox values to boolean
        $status = $validated['status'] ?? null;
        $validated['status'] = in_array($status, ['on', '1', 'true', true, 1], true);

        return $validated;
    }

    /**
     * Process answers based on question type
     */
    private function processAnswers($type, $answers)
    {
        // Clean up answers
        $processedAnswers = array_map('trim', $answers);

        // Remove empty answers
        $processedAnswers = array_filter($processedAnswers, function($answer) {
            return !empty($answer);
        });

        // Reindex array
        $processedAnswers = array_values($processedAnswers);

        // Apply specific processing based on type
        switch ($type) {
            case 'rating':
                // Ensure consistent rating format
                return ['1 sao', '2 sao', '3 sao', '4 sao', '5 sao'];
        }

        return $processedAnswers;
    }

    /**
     * Validate answers based on question type
     */
    private function validateAnswersForType($type, $answers, $fail)
    {
        // Remove empty answers for validation
        $answers = array_filter($answers, function($answer) {
            return !empty(trim($answer));
        });
        // Check for duplicate answers
        $trimmedAnswers = array_map('trim', $answers);
        $uniqueAnswers = array_unique($trimmedAnswers);
        if (count($uniqueAnswers) !== count($trimmedAnswers)) {
            $fail('Các lựa chọn không được trùng lặp.');
        }
    }
}
