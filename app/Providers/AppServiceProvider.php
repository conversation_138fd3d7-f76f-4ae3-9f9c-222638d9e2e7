<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Blade;
use Illuminate\Pagination\Paginator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register FileSizeHelper
        $this->app->singleton('App\Helpers\FileSizeHelper', function ($app) {
            return new \App\Helpers\FileSizeHelper();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set default pagination views
        Paginator::useBootstrapFive();

        // Set application locale
        App::setLocale(config('app.locale', 'vi'));

        // Macro cho các thông báo flash với SweetAlert
        Redirect::macro('withSuccess', function ($message) {
            return $this->with('success', $message);
        });

        Redirect::macro('withError', function ($message) {
            return $this->with('error', $message);
        });

        Redirect::macro('withInfo', function ($message) {
            return $this->with('info', $message);
        });

        Redirect::macro('withWarning', function ($message) {
            return $this->with('warning', $message);
        });

        if (!config('app.debug')) {
            \Debugbar::disable();
        }

        // Register custom Blade directive for formatting file sizes
        Blade::directive('filesize', function ($expression) {
            return "<?php echo app('App\Helpers\FileSizeHelper')->format($expression); ?>";
        });
    }
}
