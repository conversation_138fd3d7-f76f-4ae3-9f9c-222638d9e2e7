<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use App\Models\Webinar;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        // Custom route model binding for Webinar to handle hash IDs
        Route::bind('webinar', function ($value) {
            $id = Webinar::decodeHashId($value);

            if (!$id) {
                abort(404);
            }

            return Webinar::where('id', $id)->firstOrFail();
        });

        // Custom route model binding for SmartLink to handle hash IDs
        Route::bind('smart_link', function ($value) {
            $id = \App\Models\SmartLink::decodeHashId($value);

            if (!$id) {
                abort(404);
            }

            return \App\Models\SmartLink::where('id', $id)->firstOrFail();
        });

        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
