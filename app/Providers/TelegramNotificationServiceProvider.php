<?php

namespace App\Providers;

use App\Events\OrderCreatedEvent;
use App\Events\OrderPaymentAutoSuccessEvent;
use Illuminate\Support\ServiceProvider;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;
use App\Events\WebinarParticipantRegistered;
use App\Events\WebinarCommentSubmitted;
use Illuminate\Support\Facades\Event;
use App\Jobs\SendTelegramNotification;

class TelegramNotificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Đăng ký listener cho sự kiện khi có người tham gia webinar
        Event::listen(WebinarParticipantRegistered::class, function ($event) {
            if ($this->isTelegramEnabled() && $this->isParticipantNotificationEnabled($event->webinar)) {
                $this->sendParticipantNotification($event->participant, $event->webinar);
            }
        });

        // Đăng ký listener cho sự kiện khi có người bình luận
        Event::listen(WebinarCommentSubmitted::class, function ($event) {
            if ($this->isTelegramEnabled() && $this->isCommentNotificationEnabled($event->webinar)) {
                $this->sendCommentNotification($event->comment, $event->webinar);
            }
        });
        // Đăng ký listener cho sự kiện khi có đơn hàng mới
        Event::listen(OrderCreatedEvent::class, function ($event) {
            if ($this->isTelegramEnabled() && $this->isOrderNewNotificationEnabled(@$event->order->webinar)) {
                $this->sendOrderNewNotification($event->order);
            }
        });
        // Đăng ký listener cho sự kiện khi có đơn hàng được thanh toán tự động thành công
        Event::listen(OrderPaymentAutoSuccessEvent::class, function ($event) {
            if ($this->isTelegramEnabled() && $this->isOrderPaymentAutoSuccessNotificationEnabled(@$event->order->webinar)) {
                $this->sendOrderPaymentAutoSuccessNotification($event->order);
            }
        });
    }

    /**
     * Kiểm tra xem thông báo Telegram có được bật không.
     *
     * @return bool
     */
    private function isTelegramEnabled(): bool
    {
        return (bool)Setting::get('telegram_enabled', false);
    }

    /**
     * Kiểm tra xem thông báo người tham gia mới có được bật không.
     *
     * @param \App\Models\Webinar $webinar
     * @return bool
     */
    private function isParticipantNotificationEnabled($webinar): bool
    {
        // Check webinar-specific setting first
        if (isset($webinar->notification_settings['telegram_notify_participants'])) {
            return $webinar->notification_settings['telegram_notify_participants'] === '1';
        }

        // Fall back to global setting
        return (bool)Setting::get('telegram_notify_new_participant', false);
    }

    /**
     * Kiểm tra xem thông báo bình luận mới có được bật không.
     *
     * @param \App\Models\Webinar $webinar
     * @return bool
     */
    private function isCommentNotificationEnabled($webinar): bool
    {
        // Check webinar-specific setting first
        if (isset($webinar->notification_settings['telegram_notify_comments'])) {
            return $webinar->notification_settings['telegram_notify_comments'] === '1';
        }

        // Fall back to global setting
        return (bool)Setting::get('telegram_notify_new_comment', false);
    }

    private function isOrderNewNotificationEnabled($webinar): bool
    {
        if (is_object($webinar)) {
            // Check webinar-specific setting first
            if (isset($webinar->notification_settings['telegram_notify_order_new'])) {
                return $webinar->notification_settings['telegram_notify_order_new'] === '1';
            }
        }

        // Fall back to global setting
        return (bool)Setting::get('telegram_notify_order_new', false);
    }

    private function isOrderPaymentAutoSuccessNotificationEnabled($webinar): bool
    {
        // Check webinar-specific setting first
        if (is_object($webinar)) {
            if (isset($webinar->notification_settings['telegram_notify_order_payment_success'])) {
                return $webinar->notification_settings['telegram_notify_order_payment_success'] === '1';
            }
        }


        // Fall back to global setting
        return (bool)Setting::get('telegram_notify_order_payment_success', false);
    }

    /**
     * Gửi thông báo khi có người tham gia webinar mới.
     *
     * @param \App\Models\Participant $participant
     * @param \App\Models\Webinar $webinar
     * @return void
     */
    private function sendParticipantNotification($participant, $webinar): void
    {
        $botToken = Setting::get('telegram_bot_token');
        $chatIdsString = Setting::get('telegram_chat_ids');

        if (!$botToken || !$chatIdsString) {
            Log::warning('Telegram notification failed: Missing bot token or chat IDs');
            return;
        }

        $chatIds = explode("\n", $chatIdsString);
        $chatIds = array_map('trim', $chatIds);
        $chatIds = array_filter($chatIds);

        $message = "🔔 *Có người vừa tham gia webinar*\n\n";
        $message .= "👤 *Tên*: " . ($participant->name ?? 'Không có tên') . "\n";
        $message .= "📧 *Email*: " . ($participant->email ?? 'Không có email') . "\n";
        $message .= "📱 *Điện thoại*: " . ($participant->phone ?? 'Không có SĐT') . "\n\n";
        $message .= "🎬 *Webinar*: " . ($webinar->title ?? 'Không xác định') . "\n";
        $message .= "👨‍🏫 *Người thuyết trình*: " . ($webinar->speaker ?? 'Không xác định') . "\n";
        $message .= "🕒 *Thời gian*: " . now()->format('d/m/Y H:i:s');

        foreach ($chatIds as $chatId) {
            try {
                $this->sendMessage($botToken, $chatId, $message);
            } catch (\Exception $e) {
                Log::error('Telegram participant notification failed', [
                    'error' => $e->getMessage(),
                    'chat_id' => $chatId
                ]);
            }
        }
    }

    /**
     * Gửi thông báo khi có bình luận mới.
     *
     * @param \App\Models\Comment $comment
     * @param \App\Models\Webinar $webinar
     * @return void
     */
    private function sendCommentNotification($comment, $webinar): void
    {
        $botToken = Setting::get('telegram_bot_token');
        $chatIdsString = Setting::get('telegram_chat_ids');

        if (!$botToken || !$chatIdsString) {
            Log::warning('Telegram notification failed: Missing bot token or chat IDs');
            return;
        }

        $chatIds = explode("\n", $chatIdsString);
        $chatIds = array_map('trim', $chatIds);
        $chatIds = array_filter($chatIds);

        $message = "🔔 *Bình luận webinar mới*\n\n";
        $message .= "👤 *Tên*: " . ($comment->name ?? 'Không có tên') . "\n";
        $message .= "💬 *Nội dung*: " . ($comment->content ?? 'Không có nội dung') . "\n\n";
        $message .= "🎬 *Webinar*: " . ($webinar->title ?? 'Không xác định') . "\n";
        $message .= "👨‍🏫 *Người thuyết trình*: " . ($webinar->speaker ?? 'Không xác định') . "\n";
        $message .= "🕒 *Thời gian*: " . now()->format('d/m/Y H:i:s');

        foreach ($chatIds as $chatId) {
            try {
                $this->sendMessage($botToken, $chatId, $message);
            } catch (\Exception $e) {
                Log::error('Telegram comment notification failed', [
                    'error' => $e->getMessage(),
                    'chat_id' => $chatId
                ]);
            }
        }
    }

    /**
     * Gửi thông báo khi có đơn hàng mới.
     *
     * @param $order
     * @return void
     */
    private function sendOrderNewNotification($order)
    {
        $botToken = Setting::get('telegram_bot_token');
        $chatIdsString = Setting::get('telegram_chat_ids');

        if (!$botToken || !$chatIdsString) {
            Log::warning('Telegram notification failed: Missing bot token or chat IDs');
            return;
        }

        $chatIds = explode("\n", $chatIdsString);
        $chatIds = array_map('trim', $chatIds);
        $chatIds = array_filter($chatIds);
        $webinar = $order->webinar;
        $message = "🔔 *Có đơn hàng mới*\n\n";
        $message .= "🎬 *Webinar*: " . (is_object($webinar) ? $webinar->title : 'Không xác định') . "\n";
        $message .= "👨‍🏫 *Người thuyết trình*: " . (is_object($webinar) ? $webinar->speaker : 'Không xác định') . "\n";
        $message .= "📦 *Sản phẩm*: " . ($order->product_name) . "\n";
        $message .= "💰 *Số tiền*: " . (number_format($order->price)) . "đ\n";
        $message .= "🧑 *Tên*: " . ($order->name) . "\n";
        $message .= "📞 *SĐT*: " . ($order->phone) . "\n";
        $message .= "🕒 *Thời gian*: " . now()->format('d/m/Y H:i:s');


        foreach ($chatIds as $chatId) {
            try {
                $this->sendMessage($botToken, $chatId, $message);
            } catch (\Exception $e) {
                Log::error('Telegram comment notification failed', [
                    'error' => $e->getMessage(),
                    'chat_id' => $chatId
                ]);
            }
        }
    }

    /**
     * Gửi thông báo khi có đơn hàng được thanh toán tự động thành công.
     *
     * @param $order
     * @return void
     */
    private function sendOrderPaymentAutoSuccessNotification($order)
    {
        $botToken = Setting::get('telegram_bot_token');
        $chatIdsString = Setting::get('telegram_chat_ids');

        if (!$botToken || !$chatIdsString) {
            Log::warning('Telegram notification failed: Missing bot token or chat IDs');
            return;
        }

        $chatIds = explode("\n", $chatIdsString);
        $chatIds = array_map('trim', $chatIds);
        $chatIds = array_filter($chatIds);
        $webinar = $order->webinar;
        $message = "🔔 *Có đơn hàng được thanh toán tự động thành công*\n\n";
        $message .= "🎬 *Webinar*: " . (is_object($webinar) ? $webinar->title : 'Không xác định') . "\n";
        $message .= "👨‍🏫 *Người thuyết trình*: " . (is_object($webinar) ? $webinar->speaker : 'Không xác định') . "\n";
        $message .= "📦 *Sản phẩm*: " . ($order->product_name) . "\n";
        $message .= "💰 *Số tiền*: " . (number_format($order->price)) . "đ\n";
        $message .= "🧾 *NDCK*: " . ($order->transaction_note) . "\n";
        $message .= "🧑 *Tên*: " . ($order->name) . "\n";
        $message .= "📞 *SĐT*: " . ($order->phone) . "\n";
        $message .= "🕒 *Thời gian*: " . now()->format('d/m/Y H:i:s');


        foreach ($chatIds as $chatId) {
            try {
                $this->sendMessage($botToken, $chatId, $message);
            } catch (\Exception $e) {
                Log::error('Telegram comment notification failed', [
                    'error' => $e->getMessage(),
                    'chat_id' => $chatId
                ]);
            }
        }
    }

    /**
     * Gửi tin nhắn đến Telegram.
     *
     * @param string $botToken
     * @param string $chatId
     * @param string $message
     * @return array|null
     */
    private function sendMessage($botToken, $chatId, $message)
    {
        // Dispatch job to queue instead of sending directly
        \App\Jobs\SendTelegramNotification::dispatch($botToken, $chatId, $message);

        // Return a placeholder response since the actual sending is now handled by the queue
        return ['queued' => true];
    }
}
