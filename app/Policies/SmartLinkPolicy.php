<?php

namespace App\Policies;

use App\Models\SmartLink;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SmartLinkPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('webinars index');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SmartLink $smartLink): bool
    {
        return $user->id === $smartLink->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('webinars index');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SmartLink $smartLink): bool
    {
        return $user->id === $smartLink->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SmartLink $smartLink): bool
    {
        return $user->id === $smartLink->user_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SmartLink $smartLink): bool
    {
        return $user->id === $smartLink->user_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SmartLink $smartLink): bool
    {
        return $user->id === $smartLink->user_id;
    }
}
