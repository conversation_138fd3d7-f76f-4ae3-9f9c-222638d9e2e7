<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionResponse extends Model
{
    use HasFactory;

    protected $fillable = [
        'question_id',
        'webinar_id', 
        'user_name',
        'user_email',
        'user_phone',
        'session_id',
        'selected_answers',
        'answer_text',
        'rating',
        'responded_at',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'selected_answers' => 'array',
        'responded_at' => 'datetime',
    ];

    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    // Scope for today's responses
    public function scopeToday($query)
    {
        return $query->whereDate('responded_at', today());
    }

    // Scope for real-time (last 5 minutes)
    public function scopeRealTime($query)
    {
        return $query->where('responded_at', '>=', now()->subMinutes(5));
    }
}
