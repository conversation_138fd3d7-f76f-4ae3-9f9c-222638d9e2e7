<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Module extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'icon',
        'route',
        'permission',
        'is_enabled',
        'is_core',
        'sort_order',
        'version',
        'price',
        'currency',
        'config',
        'category'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'is_core' => 'boolean',
        'sort_order' => 'integer',
        'config' => 'array'
    ];

    const CACHE_KEY = 'modules';

    /**
     * Check if a module is enabled
     */
    public static function isEnabled(string $moduleName): bool
    {
        $cacheKey = self::CACHE_KEY . '.enabled.' . $moduleName;
        
        return Cache::remember($cacheKey, now()->addHour(), function () use ($moduleName) {
            $module = self::where('name', $moduleName)->first();
            return $module ? $module->is_enabled : false;
        });
    }

    /**
     * Enable a module
     */
    public static function enable(string $moduleName): bool
    {
        $module = self::where('name', $moduleName)->first();
        if ($module) {
            $module->is_enabled = true;
            $module->save();
            self::clearCache();
            return true;
        }
        return false;
    }

    /**
     * Disable a module
     */
    public static function disable(string $moduleName): bool
    {
        $module = self::where('name', $moduleName)->first();
        if ($module && !$module->is_core) {
            $module->is_enabled = false;
            $module->save();
            self::clearCache();
            return true;
        }
        return false;
    }

    /**
     * Get all enabled modules
     */
    public static function getEnabled()
    {
        $cacheKey = self::CACHE_KEY . '.enabled_list';
        
        return Cache::remember($cacheKey, now()->addHour(), function () {
            return self::where('is_enabled', true)
                      ->orderBy('sort_order')
                      ->orderBy('display_name')
                      ->get();
        });
    }

    /**
     * Get modules by category
     */
    public static function getByCategory(string $category = null)
    {
        $query = self::orderBy('sort_order')->orderBy('display_name');
        
        if ($category) {
            $query->where('category', $category);
        }
        
        return $query->get();
    }

    /**
     * Get all categories
     */
    public static function getCategories()
    {
        return self::distinct('category')->pluck('category')->sort()->values();
    }

    /**
     * Clear module cache
     */
    public static function clearCache()
    {
        Cache::forget(self::CACHE_KEY . '.enabled_list');
        
        // Clear individual module caches
        $modules = self::pluck('name');
        foreach ($modules as $moduleName) {
            Cache::forget(self::CACHE_KEY . '.enabled.' . $moduleName);
        }
    }

    /**
     * Check if user has permission for this module
     */
    public function hasPermission($user = null): bool
    {
        if (!$this->permission) {
            return true;
        }

        $user = $user ?: auth()->user();
        
        if (!$user) {
            return false;
        }

        return $user->can($this->permission);
    }

    /**
     * Get module config value
     */
    public function getConfig(string $key, $default = null)
    {
        $config = $this->config ?? [];
        return data_get($config, $key, $default);
    }

    /**
     * Set module config value
     */
    public function setConfig(string $key, $value)
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->config = $config;
        $this->save();
        self::clearCache();
    }
}
