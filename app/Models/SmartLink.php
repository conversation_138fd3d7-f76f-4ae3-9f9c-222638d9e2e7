<?php

namespace App\Models;

use Hashids\Hashids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmartLink extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'code',
        'url',
        'is_active',
        'click_count',
        'last_accessed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'click_count' => 'integer',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Get hashids instance
     *
     * @return \Hashids\Hashids
     */
    protected function getHashids()
    {
        $salt = config('app.key', env('APP_KEY'));
        $minHashLength = 16;
        return new Hashids($salt, $minHashLength);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    /**
     * Get the route key value for the model.
     *
     * @return string
     */
    public function getRouteKey()
    {
        return $this->hashId();
    }

    /**
     * Retrieve the model for a bound value.
     *
     * @param mixed $value
     * @param string|null $field
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function resolveRouteBinding($value, $field = null)
    {
        $id = self::decodeHashId($value);

        if (!$id) {
            return null;
        }

        return $this->where('id', $id)->first();
    }

    /**
     * Convert the model's ID to a hashed ID.
     *
     * @return string
     */
    public function hashId()
    {
        return $this->getHashids()->encode($this->id);
    }

    /**
     * Decode a hashed ID back to the original ID.
     *
     * @param string $hashId
     * @return int|null
     */
    public static function decodeHashId($hashId)
    {
        try {
            // Skip if hashId is empty
            if (empty($hashId)) {
                return null;
            }

            $decoded = (new static)->getHashids()->decode($hashId);

            // Hashids returns an array of numbers, we need the first one
            if (!empty($decoded)) {
                return $decoded[0];
            }

            return null;
        } catch (\Exception $e) {
            // Log error for debugging if needed
            // \Log::error('Hash decode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the user that owns the smart link.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the schedules for the smart link.
     */
    public function schedules()
    {
        return $this->hasMany(SmartLinkSchedule::class)->orderBy('sort_order');
    }

    /**
     * Generate a unique code for smart link.
     */
    public static function generateCode()
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';

        do {
            $code = '';
            for ($i = 0; $i < 8; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get the current active webinar based on schedule.
     */
    public function getCurrentWebinar()
    {
        $now = now();

        foreach ($this->schedules as $schedule) {
            if ($schedule->isActive()) {
                return $schedule->webinar;
            }
        }

        return null;
    }

    /**
     * Get the next upcoming webinar.
     */
    public function getNextWebinar()
    {
        $upcomingSchedules = $this->schedules->filter(function ($schedule) {
            return $schedule->isUpcoming();
        })->sortBy(function ($schedule) {
            return $schedule->start_date_time;
        });

        $nextSchedule = $upcomingSchedules->first();
        return $nextSchedule ? $nextSchedule->webinar : null;
    }

    /**
     * Increment click count.
     */
    public function incrementClickCount()
    {
        $this->increment('click_count');
        $this->update(['last_accessed_at' => now()]);
    }

    /**
     * Get the full smart link URL.
     */
    public function getFullUrlAttribute()
    {
        return url("/s/{$this->code}");
    }
}
