<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WebinarParticipant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'webinar_id',
        'name',
        'email',
        'phone',
        'password',
        'is_student',
        'note',
        'ip_address',
        'joined_at',
        'join_count',
        'view_duration',
        'device_type',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'joined_at' => 'datetime',
        'join_count' => 'integer',
        'view_duration' => 'integer',
        'is_student' => 'boolean',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * Get the webinar that owns the participant.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Get the comments associated with the participant.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class, 'participant_id');
    }
} 