<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FileComment extends Model
{
    use HasFactory;
    public $table="file_comments";
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }
}
