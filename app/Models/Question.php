<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $table = "questions";
    protected $casts = [
        'answers' => 'array',
    ];

    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    public function questionSet()
    {
        return $this->belongsTo(QuestionSet::class);
    }

    public function responses()
    {
        return $this->hasMany(QuestionResponse::class);
    }

    // Get total responses count
    public function getTotalResponsesAttribute()
    {
        return $this->responses()->count();
    }

    // Get today's responses count
    public function getTodayResponsesAttribute()
    {
        return $this->responses()->today()->count();
    }

    // Get real-time responses count
    public function getRealTimeResponsesAttribute()
    {
        return $this->responses()->realTime()->count();
    }

    // Get response statistics
    public function getResponseStatsAttribute()
    {
        $responses = $this->responses;
        $stats = [];

        if ($this->type === 'multiple-choice' || $this->type === 'single-choice') {
            foreach ($this->answers as $index => $answer) {
                $count = $responses->filter(function ($response) use ($index) {
                    return in_array($index, $response->selected_answers ?? []);
                })->count();

                $stats[] = [
                    'answer' => $answer,
                    'count' => $count,
                    'percentage' => $responses->count() > 0 ? round(($count / $responses->count()) * 100, 1) : 0
                ];
            }
        } elseif ($this->type === 'rating') {
            for ($i = 1; $i <= 5; $i++) {
                $count = $responses->where('rating', $i)->count();
                $stats[] = [
                    'rating' => $i,
                    'count' => $count,
                    'percentage' => $responses->count() > 0 ? round(($count / $responses->count()) * 100, 1) : 0
                ];
            }
        }

        return $stats;
    }

}
