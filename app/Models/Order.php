<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone',
        'email',
        'product_name',
        'price',
        'transaction_note',
        'purchase_time',
        'order_type',
        'payment_status ',
        'transaction_id',
        'payment_note',
        'webinar_id',
        'payment_status',
        'care_status',
        'is_live',
        'next_follow_date',
        'assigned_to',
        'reminder_type',
        'is_priority',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'purchase_time' => 'datetime',
        'price' => 'decimal:2',
        'next_follow_date' => 'datetime',
        'is_priority' => 'boolean',
    ];

    /**
     * Get the webinar that the order belongs to.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Get the telesale user assigned to this order.
     */
    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get all care notes for this order.
     */
    public function careNotes()
    {
        return $this->hasMany(CustomerCareNote::class);
    }

    /**
     * Get all care history records for this order.
     */
    public function careHistory()
    {
        return $this->hasMany(CustomerCareHistory::class);
    }

    /**
     * Get the customer care status as text.
     */
    public function getCareStatusTextAttribute()
    {
        $statuses = [
            'new' => 'Mới',
            'contacted' => 'Đã liên hệ',
            'interested' => 'Quan tâm',
            'not_interested' => 'Không quan tâm',
            'callback' => 'Gọi lại sau',
            'converted' => 'Đã chuyển đổi',
            'closed' => 'Đã đóng',
        ];

        return $statuses[$this->care_status] ?? 'Không xác định';
    }

    /**
     * Get formatted price attribute.
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 0, ',', '.') . ' đ';
    }

    /**
     * Get time until next follow-up.
     */
    public function getTimeUntilFollowUpAttribute()
    {
        if (!$this->next_follow_date) {
            return null;
        }

        $now = Carbon::now();
        $followDate = Carbon::parse($this->next_follow_date);

        if ($followDate->isPast()) {
            return 'Quá hạn ' . $followDate->diffForHumans($now);
        }

        return $followDate->diffForHumans($now);
    }

    /**
     * Check if the order needs follow-up today.
     */
    public function getNeedsFollowUpTodayAttribute()
    {
        if (!$this->next_follow_date) {
            return false;
        }

        return Carbon::parse($this->next_follow_date)->isToday();
    }

    /**
     * Scope a query to only include orders assigned to a specific user.
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Scope a query to only include orders needing follow-up.
     */
    public function scopeNeedFollowUp($query)
    {
        return $query->whereDate('next_follow_date', '<=', now()->toDateString());
    }

    /**
     * Scope a query to only include orders with overdue follow-up.
     */
    public function scopeOverdueFollowUp($query)
    {
        return $query->whereDate('next_follow_date', '<', now()->toDateString());
    }

    /**
     * Scope a query to only include orders with follow-up scheduled today.
     */
    public function scopeFollowUpToday($query)
    {
        return $query->whereDate('next_follow_date', now()->toDateString());
    }

    /**
     * Scope a query to only include orders with follow-up scheduled this week.
     */
    public function scopeFollowUpThisWeek($query)
    {
        return $query->whereBetween('next_follow_date', [
            now()->startOfWeek()->toDateString(),
            now()->endOfWeek()->toDateString()
        ]);
    }

    /**
     * Scope a query to only include orders without assigned follow-up.
     */
    public function scopeWithoutFollowUp($query)
    {
        return $query->whereNull('next_follow_date');
    }

    /**
     * Scope a query to only include unassigned orders.
     */
    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_to');
    }

    /**
     * Scope a query to filter by webinar.
     */
    public function scopeFromWebinar($query, $webinarId)
    {
        return $query->where('webinar_id', $webinarId);
    }

    /**
     * Scope a query to filter by care status.
     */
    public function scopeWithCareStatus($query, $status)
    {
        return $query->where('care_status', $status);
    }

    /**
     * Scope a query to filter by payment status.
     */
    public function scopeWithPaymentStatus($query, $status)
    {
        return $query->where('payment_status', $status);
    }

    /**
     * Scope a query to filter by high value orders.
     */
    public function scopeHighValue($query, $threshold = 1000000)
    {
        return $query->where('price', '>=', $threshold);
    }

    /**
     * Scope a query to filter recent orders.
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to order by care status priority.
     * This is an optimized version of the complex CASE WHEN statement.
     */
    public function scopeOrderByCareStatusPriority($query)
    {
        return $query->selectRaw('*, CASE
            WHEN care_status IS NULL THEN 1
            WHEN care_status = "new" THEN 2
            WHEN care_status = "contacted" THEN 3
            WHEN care_status = "interested" THEN 4
            WHEN care_status = "callback" THEN 5
            WHEN care_status = "not_interested" THEN 6
            WHEN care_status = "closed" THEN 7
            WHEN care_status = "converted" THEN 8
            ELSE 9
        END as care_priority')
        ->orderBy('care_priority');
    }
}
