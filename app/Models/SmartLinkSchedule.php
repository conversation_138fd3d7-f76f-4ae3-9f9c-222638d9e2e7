<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmartLinkSchedule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'smart_link_id',
        'webinar_id',
        'schedule_index',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'schedule_index' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the smart link that owns the schedule.
     */
    public function smartLink()
    {
        return $this->belongsTo(SmartLink::class);
    }

    /**
     * Get the webinar for this schedule.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Get the schedule data from webinar.
     */
    public function getScheduleDataAttribute()
    {
        $webinarSchedules = $this->webinar->schedules ?? [];
        return $webinarSchedules[$this->schedule_index] ?? null;
    }

    /**
     * Get start datetime from webinar schedule.
     */
    public function getStartDateTimeAttribute()
    {
        $scheduleData = $this->schedule_data;
        if (!$scheduleData) return null;

        return \Carbon\Carbon::parse($scheduleData['date'] . ' ' . $scheduleData['time']);
    }

    /**
     * Get end datetime from webinar schedule.
     */
    public function getEndDateTimeAttribute()
    {
        $startDateTime = $this->start_date_time;
        if (!$startDateTime) return null;

        $duration = $this->webinar->video_duration_minutes ?? 120;
        return $startDateTime->copy()->addMinutes($duration);
    }

    /**
     * Check if this schedule is currently active.
     */
    public function isActive()
    {
        $now = now();
        $start = $this->start_date_time;
        $end = $this->end_date_time;

        if (!$start || !$end) return false;

        return $now->between($start, $end);
    }

    /**
     * Check if this schedule is upcoming.
     */
    public function isUpcoming()
    {
        $start = $this->start_date_time;
        if (!$start) return false;

        return $start->isFuture();
    }

    /**
     * Check if this schedule has ended.
     */
    public function hasEnded()
    {
        $end = $this->end_date_time;
        if (!$end) return true;

        return $end->isPast();
    }

    /**
     * Get status text.
     */
    public function getStatusAttribute()
    {
        if ($this->isActive()) {
            return 'Đang diễn ra';
        } elseif ($this->isUpcoming()) {
            return 'Sắp diễn ra';
        } else {
            return 'Đã kết thúc';
        }
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        if ($this->isActive()) {
            return 'badge-success';
        } elseif ($this->isUpcoming()) {
            return 'badge-warning';
        } else {
            return 'badge-secondary';
        }
    }
}
