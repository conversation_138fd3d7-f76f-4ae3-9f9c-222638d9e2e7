<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionSet extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'webinar_id',
        'status',
        'description',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the webinar that owns the question set.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Get the questions for the question set.
     */
    public function questions()
    {
        return $this->hasMany(Question::class)->orderBy('created_at');
    }

    /**
     * Get active questions for the question set.
     */
    public function activeQuestions()
    {
        return $this->hasMany(Question::class)->where('status', true);
    }

    /**
     * Get total questions count
     */
    public function getTotalQuestionsAttribute()
    {
        return $this->questions()->count();
    }

    /**
     * Get active questions count
     */
    public function getActiveQuestionsCountAttribute()
    {
        return $this->activeQuestions()->count();
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute()
    {
        return $this->status ? 'Kích hoạt' : 'Tạm dừng';
    }

    /**
     * Get status color class
     */
    public function getStatusColorAttribute()
    {
        return $this->status ? 'success' : 'secondary';
    }

    /**
     * Scope for active question sets
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for inactive question sets
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }
}
