<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Advertisement extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'image',
        'url',
        'product_id',
        'virtual_quantity',
        'order_method',
        'redirect_url',
        'require_name',
        'require_email',
        'require_phone',
        'original_price',
        'sale_price',
        'display_time',
        'user_id',
    ];

    protected $casts = [
        'virtual_quantity' => 'integer',
        'require_name' => 'boolean',
        'require_email' => 'boolean',
        'require_phone' => 'boolean',
        'display_time' => 'integer',
    ];

    /**
     * Get the user that owns the advertisement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product associated with the advertisement.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if the advertisement has a product.
     */
    public function hasProduct(): bool
    {
        return $this->type === 'product' && $this->product_id !== null;
    }
}
