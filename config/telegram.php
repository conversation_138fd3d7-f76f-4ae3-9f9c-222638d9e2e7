<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Telegram Bot API Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure settings for the Telegram Bot API integration.
    | The base URL can be changed if you need to use a different endpoint
    | or proxy for Telegram API requests.
    |
    */

    /**
     * Base URL for Telegram Bot API
     * 
     * Default: https://api.telegram.org
     * You can change this to use a different endpoint or proxy
     */
    'api_base_url' => env('TELEGRAM_API_BASE_URL', 'https://api.telegram.org'),

    /**
     * Timeout for API requests (in seconds)
     */
    'api_timeout' => env('TELEGRAM_API_TIMEOUT', 30),

    /**
     * Maximum number of updates to fetch in one request
     */
    'max_updates_limit' => env('TELEGRAM_MAX_UPDATES_LIMIT', 100),

    /**
     * Default parse mode for messages
     */
    'default_parse_mode' => env('TELEGRAM_DEFAULT_PARSE_MODE', 'Markdown'),

    /**
     * Cache key prefix for storing Telegram-related data
     */
    'cache_prefix' => 'telegram_',

    /**
     * Default cache TTL for Telegram data (in seconds)
     */
    'cache_ttl' => env('TELEGRAM_CACHE_TTL', 3600),
]; 