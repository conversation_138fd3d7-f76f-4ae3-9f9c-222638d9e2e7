@extends('layouts.app')

@section('content')


    <!-- Thống kê CRM -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Thống kê CRM</h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="btn-refresh-stats">
                        <i class="fas fa-sync-alt"></i> Làm mới
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['newCount'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Mới</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-phone-alt"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['contactedCount'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Đã liên hệ</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-thumbs-up"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['interestedCount'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Quan tâm</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['callbackCount'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Hẹn gọi lại</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-check-double"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['convertedCount'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Chuyển đổi thành công</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-dollar"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['total_money_converted'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Doanh thu</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card highlight">
                                <div class="stat-icon bg-danger">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['followTodayCount'] ?? 0, 0, ',', '.') }}</h3>
                                    <p class="stat-title">Cần gọi hôm nay</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="crm-stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format(($stats['convertedCount'] / max($stats['contactedCount'], 1)) * 100, 1, ',', '.') }}
                                        %</h3>
                                    <p class="stat-title">Tỷ lệ chuyển đổi</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bộ lọc tìm kiếm -->
    <div class="card mb-4">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <button class="btn btn-link text-decoration-none p-0 d-flex align-items-center" type="button"
                        data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false"
                        aria-controls="filterCollapse">
                    <i class="fas fa-filter me-2"></i> Bộ lọc
                    <span id="filter-badge" class="badge bg-primary filter-badge ms-2" style="display: none;">0</span>
                    <i class="fas fa-chevron-down ms-2 toggle-icon"></i>
                </button>
            </h5>
            <!-- Minified search always visible -->
            <div class="d-flex">
                <form class="d-flex align-items-center" action="{{ route('orders.index') }}" method="GET">
                    <input type="text" class="form-control form-control-sm me-2" id="quick-search" name="search"
                           placeholder="Tìm kiếm nhanh..." value="{{ request('search') }}" style="width: 200px;">
                    <button type="submit" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <!-- If we have filters, add them as hidden fields -->
                    @if(request('webinar_id'))
                        <input type="hidden" name="webinar_id" value="{{ request('webinar_id') }}">
                    @endif
                    @if(request('payment_status'))
                        <input type="hidden" name="payment_status" value="{{ request('payment_status') }}">
                    @endif
                    @if(request('care_status'))
                        <input type="hidden" name="care_status" value="{{ request('care_status') }}">
                    @endif
                    @if(request('assigned_to'))
                        <input type="hidden" name="assigned_to" value="{{ request('assigned_to') }}">
                    @endif
                    @if(request('follow_type'))
                        <input type="hidden" name="follow_type" value="{{ request('follow_type') }}">
                    @endif
                    @if(request('follow_date'))
                        <input type="hidden" name="follow_date" value="{{ request('follow_date') }}">
                    @endif
                    @if(request('sort_by'))
                        <input type="hidden" name="sort_by" value="{{ request('sort_by') }}">
                    @endif
                </form>
                <a href="{{ route('orders.index') }}" class="btn btn-sm btn-outline-secondary ms-2"
                   id="btn-clear-filters" title="Xóa tất cả bộ lọc">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body border-top">
                <form id="filter-form" class="row g-3" action="{{ route('orders.index') }}" method="GET">
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Tên/SĐT/Email</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Tìm kiếm..."
                               value="{{ request('search') }}">
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Mức ưu tiên</label>
                        <select class="form-select filter-select" id="priority" name="priority" data-filter="priority">
                            <option value="">Tất cả</option>
                            <option value="1" {{ request('priority') == '1' ? 'selected' : '' }}>Chỉ khách hàng ưu
                                tiên
                            </option>
                            <option value="0" {{ request('priority') == '0' ? 'selected' : '' }}>Chỉ khách hàng thường
                            </option>
                        </select>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Webinar</label>
                        <select class="form-select filter-select" id="webinar_id" name="webinar_id"
                                data-filter="webinar">
                            <option value="">Tất cả webinar</option>
                            @foreach($webinars as $webinar)
                                <option
                                    value="{{ $webinar->id }}" {{ request('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                    {{ $webinar->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Trạng thái thanh toán</label>
                        <select class="form-select filter-select" id="payment_status" name="payment_status"
                                data-filter="payment">
                            <option value="">Tất cả trạng thái</option>
                            <option value="pending" {{ request('payment_status') == 'pending' ? 'selected' : '' }}>Đang
                                xử lý
                            </option>
                            <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>Đã thanh
                                toán
                            </option>
                            <option value="failed" {{ request('payment_status') == 'failed' ? 'selected' : '' }}>Thất
                                bại
                            </option>
                        </select>
                    </div>
                    {{--                    <div class="col-md-4 col-lg-3">--}}
                    {{--                        <label class="form-label">Trạng thái chăm sóc</label>--}}
                    {{--                        <select class="form-select filter-select" id="care_status" name="care_status"--}}
                    {{--                                data-filter="care">--}}
                    {{--                            <option value="">Tất cả trạng thái</option>--}}
                    {{--                            <option value="new" {{ request('care_status') == 'new' ? 'selected' : '' }}>Mới</option>--}}
                    {{--                            <option value="contacted" {{ request('care_status') == 'contacted' ? 'selected' : '' }}>Đã--}}
                    {{--                                liên hệ--}}
                    {{--                            </option>--}}
                    {{--                            <option value="interested" {{ request('care_status') == 'interested' ? 'selected' : '' }}>--}}
                    {{--                                Quan tâm--}}
                    {{--                            </option>--}}
                    {{--                            <option--}}
                    {{--                                value="not_interested" {{ request('care_status') == 'not_interested' ? 'selected' : '' }}>--}}
                    {{--                                Không quan tâm--}}
                    {{--                            </option>--}}
                    {{--                            <option value="callback" {{ request('care_status') == 'callback' ? 'selected' : '' }}>Gọi--}}
                    {{--                                lại sau--}}
                    {{--                            </option>--}}
                    {{--                            <option value="converted" {{ request('care_status') == 'converted' ? 'selected' : '' }}>Đã--}}
                    {{--                                chuyển đổi--}}
                    {{--                            </option>--}}
                    {{--                            <option value="closed" {{ request('care_status') == 'closed' ? 'selected' : '' }}>Đã đóng--}}
                    {{--                            </option>--}}
                    {{--                        </select>--}}
                    {{--                    </div>--}}
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Nhân viên phụ trách</label>
                        <select class="form-select filter-select" id="assigned_to" name="assigned_to"
                                data-filter="assigned">
                            <option value="">Tất cả nhân viên</option>
                            @foreach($teleSales as $agent)
                                <option
                                    value="{{ $agent->id }}" {{ request('assigned_to') == $agent->id ? 'selected' : '' }}>
                                    {{ $agent->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Theo dõi</label>
                        <select class="form-select filter-select" id="follow_type" name="follow_type"
                                data-filter="follow">
                            <option value="">Tất cả</option>
                            <option value="today" {{ request('follow_type') == 'today' ? 'selected' : '' }}>Cần theo dõi
                                hôm nay
                            </option>
                            <option value="tomorrow" {{ request('follow_type') == 'tomorrow' ? 'selected' : '' }}>Cần
                                theo dõi ngày mai
                            </option>
                            <option value="week" {{ request('follow_type') == 'week' ? 'selected' : '' }}>Cần theo dõi
                                tuần này
                            </option>
                            <option value="overdue" {{ request('follow_type') == 'overdue' ? 'selected' : '' }}>Quá hạn
                                theo dõi
                            </option>
                            <option value="no_follow" {{ request('follow_type') == 'no_follow' ? 'selected' : '' }}>Chưa
                                có lịch theo dõi
                            </option>
                        </select>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Ngày theo dõi cụ thể</label>
                        <input type="date" class="form-control filter-select" id="follow_date" name="follow_date"
                               value="{{ request('follow_date') }}" data-filter="date">
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <label class="form-label">Sắp xếp theo</label>
                        <select class="form-select filter-select" id="sort_by" name="sort_by" data-filter="sort">
                            <option value="">Mặc định</option>
                            <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Ngày
                                tạo
                            </option>
                            <option value="purchase_time" {{ request('sort_by') == 'purchase_time' ? 'selected' : '' }}>
                                Ngày mua
                            </option>
                            <option
                                value="next_follow_date" {{ request('sort_by') == 'next_follow_date' ? 'selected' : '' }}>
                                Ngày theo dõi tiếp
                            </option>
                            <option value="price" {{ request('sort_by') == 'price' ? 'selected' : '' }}>Giá trị</option>
                        </select>
                    </div>
                    <div class="col-12 mt-4 text-end">
                        <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i> Làm mới
                        </a>
                        <button type="submit" class="btn btn-primary ms-2">
                            <i class="fas fa-search me-1"></i> Áp dụng bộ lọc
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Danh sách orders -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center bg-white">
            <div>
                <h5 class="mb-0">Danh sách Order</h5>
                <div class="order-workflow mt-2">
                    <div class="order-workflow-container">
                        <a href="{{route("orders.index",["care_status_filter"=>""])}}"
                           class="workflow-item {{ !request('care_status_filter')&&!request('care_status') ? 'active' : '' }}"
                           data-status=""
                           data-bs-toggle="tooltip" title="Tất cả trạng thái">

                            <div class="workflow-icon"><i class="fas fa-list"></i></div>
                            <span>Tất cả</span>
                            <span class="count-badge">{{ number_format($stats['total'], 0, ',', '.') }}</span>
                        </a>
                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"new"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'new' || request('care_status') == 'new' ? 'active' : '' }}"
                           data-status="new" data-bs-toggle="tooltip" title="Đơn hàng mới, chưa liên hệ">


                            <div class="workflow-icon"><i class="fas fa-file-alt"></i></div>
                            <span>Mới</span>
                            @if(isset($stats['newCount']) && $stats['newCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['newCount'], 0, ',', '.') }}</span>
                            @endif
                        </a>
                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"contacted"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'contacted' || request('care_status') == 'contacted' ? 'active' : '' }}"
                           data-status="contacted" data-bs-toggle="tooltip" title="Đã liên hệ với khách hàng">


                            <div class="workflow-icon"><i class="fas fa-phone-alt"></i></div>
                            <span>Đã liên hệ</span>
                            @if(isset($stats['contactedCount']) && $stats['contactedCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['contactedCount'], 0, ',', '.') }}</span>
                            @endif
                        </a>
                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"interested"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'interested' || request('care_status') == 'interested' ? 'active' : '' }}"
                           data-status="interested" data-bs-toggle="tooltip" title="Khách hàng quan tâm sản phẩm">

                            <div class="workflow-icon"><i class="fas fa-thumbs-up"></i></div>
                            <span>Quan tâm</span>
                            @if(isset($stats['interestedCount']) && $stats['interestedCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['interestedCount'], 0, ',', '.') }}</span>
                            @endif
                        </a>
                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"callback"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'callback' || request('care_status') == 'callback' ? 'active' : '' }}"
                           data-status="callback" data-bs-toggle="tooltip" title="Cần gọi lại sau">

                            <div class="workflow-icon"><i class="fas fa-phone-volume"></i></div>
                            <span>Gọi lại</span>
                            @if(isset($stats['callbackCount']) && $stats['callbackCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['callbackCount'], 0, ',', '.') }}</span>
                            @endif
                        </a>

                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"not_interested"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'not_interested' || request('care_status') == 'not_interested' ? 'active' : '' }}"
                           data-status="not_interested" data-bs-toggle="tooltip" title="Khách hàng không quan tâm">

                            <div class="workflow-icon"><i class="fas fa-thumbs-down"></i></div>
                            <span>Không quan tâm</span>
                            @if(isset($stats['not_interestedCount']) && $stats['not_interestedCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['not_interestedCount'], 0, ',', '.') }}</span>
                            @endif
                        </a>
                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"closed"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'closed' || request('care_status') == 'closed' ? 'active' : '' }}"
                           data-status="closed" data-bs-toggle="tooltip" title="Đơn hàng đã đóng">

                            <div class="workflow-icon"><i class="fas fa-window-close"></i></div>
                            <span>Đã đóng</span>
                            @if(isset($stats['closedCount']) && $stats['closedCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['closedCount'], 0, ',', '.') }}</span>
                            @endif

                        </a>
                        <div class="workflow-arrow"><i class="fas fa-chevron-right"></i></div>
                        <a href="{{route("orders.index",["care_status_filter"=>"converted"])}}"
                           class="workflow-item {{ request('care_status_filter') == 'converted' || request('care_status') == 'converted' ? 'active' : '' }}"
                           data-status="converted" data-bs-toggle="tooltip" title="Đã chuyển đổi thành công">

                            <div class="workflow-icon"><i class="fas fa-check-circle"></i></div>
                            <span>Đã chuyển đổi</span>
                            @if(isset($stats['convertedCount']) && $stats['convertedCount'] > 0)
                                <span
                                    class="count-badge">{{ number_format($stats['convertedCount'], 0, ',', '.') }}</span>
                            @endif

                        </a>
                    </div>
                    <div class="order-workflow-legend">
                        <i class="fas fa-info-circle me-1 text-primary"></i>
                        <span>Mặc định sắp xếp theo ưu tiên: Đơn chưa xử lý và mới lên đầu, đơn đã chuyển đổi xuống cuối</span>
                        <small class="d-block mt-1 text-muted">Nhấp vào các trạng thái để lọc danh sách</small>
                    </div>
                </div>
            </div>
            <div>
                <button type="button" class="btn btn-success me-2" id="btn-export">
                    <i class="fas fa-file-excel me-1"></i> Xuất Excel
                </button>
                <a href="{{ route('orders.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Tạo Order mới
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover" id="orders-table">
                    <thead class="table-light">
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th>Tên</th>
                        <th>Số điện thoại</th>
                        <th>Email</th>
                        <th>Tên sản phẩm</th>
                        <th>Giá</th>
                        <th>Thời gian mua</th>
                        <th>Loại đơn</th>
                        <th>Trạng thái CK</th>
                        <th>Ghi chú</th>
                        <th>Nội dung CK</th>
                        <th>Trạng thái CRM</th>
                        <th style="width: 150px;">Thao tác</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($orders as $order)
                        <tr id="order-row-{{ $order->id }}" class="{{ $order->is_priority ? 'priority-row' : '' }}">
                            <td>{{ $order->id }}
                                @if($order->is_priority)
                                    <span class="priority-badge" title="Khách hàng ưu tiên"><i
                                            class="fas fa-star"></i></span>
                                @endif
                            </td>
                            <td>{{ $order->name }}</td>
                            <td>
                                {{ $order->phone }}
                                <div class="mt-1">
                                    <a href="tel:{{ $order->phone }}" class="btn btn-sm btn-outline-secondary"
                                       title="Gọi điện">
                                        <i class="fas fa-phone-alt"></i>
                                    </a>
                                    <a href="https://zalo.me/{{ $order->phone }}" target="_blank"
                                       class="btn btn-sm btn-outline-info" title="Nhắn Zalo">
                                        <i class="fas fa-comment"></i>
                                    </a>
                                </div>
                            </td>
                            <td>{{ $order->email ?? 'N/A' }}</td>
                            <td>{{ $order->product_name }}</td>
                            <td>{{ number_format($order->price, 0, ',', '.') }} đ</td>
                            <td>{{ $order->purchase_time->format('d/m/Y H:i') }}</td>
                            <td>
                                @if($order->order_type == 'form')
                                    <span class="badge bg-primary">Form</span>
                                @else
                                    <span class="badge bg-info">Thanh toán QR</span>
                                @endif
                            </td>
                            <td>
                                <div class="payment-status-badges" data-id="{{ $order->id }}">
                                    @if($order->payment_status == 'pending')
                                        <span class="badge bg-warning status-badge">
                                        <i class="fas fa-clock me-1"></i> Đang xử lý
                                    </span>
                                    @elseif($order->payment_status == 'paid')
                                        <span class="badge bg-success status-badge">
                                        <i class="fas fa-check-circle me-1"></i> Đã thanh toán
                                    </span>
                                    @elseif($order->payment_status == 'failed')
                                        <span class="badge bg-danger status-badge">
                                        <i class="fas fa-times-circle me-1"></i> Thất bại
                                    </span>
                                    @endif
                                </div>
                            </td>
                            <td>{!! nl2br($order->payment_note ?? 'N/A')  !!}</td>
                            <td>{{ $order->transaction_note ?? 'N/A' }}</td>
                            <td>
                                <select class="form-select form-select-sm care-status-select"
                                        data-id="{{ $order->id }}">
                                    <option value="" {{ !$order->care_status ? 'selected' : '' }}>Chưa xử lý</option>
                                    <option value="new" {{ $order->care_status == 'new' ? 'selected' : '' }}>Mới
                                    </option>
                                    <option
                                        value="contacted" {{ $order->care_status == 'contacted' ? 'selected' : '' }}>Đã
                                        liên hệ
                                    </option>
                                    <option
                                        value="interested" {{ $order->care_status == 'interested' ? 'selected' : '' }}>
                                        Quan tâm
                                    </option>
                                    <option
                                        value="not_interested" {{ $order->care_status == 'not_interested' ? 'selected' : '' }}>
                                        Không quan tâm
                                    </option>
                                    <option value="callback" {{ $order->care_status == 'callback' ? 'selected' : '' }}>
                                        Gọi lại sau
                                    </option>
                                    <option
                                        value="converted" {{ $order->care_status == 'converted' ? 'selected' : '' }}>Đã
                                        chuyển đổi
                                    </option>
                                    <option value="closed" {{ $order->care_status == 'closed' ? 'selected' : '' }}>Đã
                                        đóng
                                    </option>
                                </select>
                                @if($order->next_follow_date)
                                    <div class="small mt-1">
                                    <span class="follow-tag {{ $order->needs_follow_up_today ? 'today' : '' }}">
                                        <i class="fas fa-calendar-alt me-1"></i> {{ $order->next_follow_date->format('d/m/Y') }}
                                    </span>
                                    </div>
                                @endif
                                @if(!$order->assigned_to)
                                    <div class="mt-1">
                                        <button type="button" class="btn btn-sm btn-outline-primary btn-assign-telesale"
                                                data-id="{{ $order->id }}" title="Gán cho telesale">
                                            <i class="fas fa-user-plus"></i> Gán Telesale
                                        </button>
                                    </div>
                                @elseif($order->assignedUser)
                                    <div class="small mt-1 text-muted">
                                        <i class="fas fa-user me-1"></i> {{ $order->assignedUser->name }}
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('orders.edit', $order) }}" class="btn btn-sm btn-primary me-1"
                                       title="Sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form id="delete-order" action="{{route("orders.destroy",$order->id)}}" method="post">
                                        @method("DELETE")
                                        @csrf
                                        <button type="button" class="btn btn-sm btn-danger btn-delete"
                                                data-id="{{ $order->id }}" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>

                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="12" class="text-center py-4">Không có dữ liệu</td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div>Hiển thị {{ number_format($orders->firstItem() ?? 0, 0, ',', '.') }}
                    đến {{ number_format($orders->lastItem() ?? 0, 0, ',', '.') }}
                    của {{ number_format($orders->total(), 0, ',', '.') }} order
                </div>
                <div class="d-flex justify-content-center">{{ $orders->links() }}</div>
            </div>
        </div>
    </div>

    <!-- Modal xem nhanh thông tin đơn hàng -->
    <div class="modal fade" id="order-detail-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chi tiết Order #<span id="order-id"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">Thông tin khách hàng</h6>
                            <table class="table table-sm table-bordered">
                                <tr>
                                    <th style="width: 35%">Tên</th>
                                    <td id="modal-name"></td>
                                </tr>
                                <tr>
                                    <th>Số điện thoại</th>
                                    <td id="modal-phone"></td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td id="modal-email"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">Thông tin đơn hàng</h6>
                            <table class="table table-sm table-bordered">
                                <tr>
                                    <th style="width: 35%">Sản phẩm</th>
                                    <td id="modal-product"></td>
                                </tr>
                                <tr>
                                    <th>Giá</th>
                                    <td id="modal-price"></td>
                                </tr>
                                <tr>
                                    <th>Thời gian mua</th>
                                    <td id="modal-time"></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">Thông tin thanh toán</h6>
                            <table class="table table-sm table-bordered">
                                <tr>
                                    <th style="width: 35%">Loại đơn</th>
                                    <td id="modal-type"></td>
                                </tr>
                                <tr>
                                    <th>Transaction ID</th>
                                    <td id="modal-transaction"></td>
                                </tr>
                                <tr>
                                    <th>Nội dung CK</th>
                                    <td id="modal-payment-note"></td>
                                </tr>
                                <tr>
                                    <th>Trạng thái</th>
                                    <td id="modal-status"></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">Thông tin CRM</h6>
                            <table class="table table-sm table-bordered mb-3">
                                <tr>
                                    <th style="width: 35%">Trạng thái chăm sóc</th>
                                    <td id="modal-care-status">
                                        <span class="badge bg-secondary">Chưa có thông tin</span>
                                    </td>
                                </tr>
                            </table>

                            <h6 class="text-muted mb-2">Ghi chú CRM</h6>
                            <div class="form-group">
                                <textarea id="crm-note" class="form-control" rows="4"
                                          placeholder="Nhập ghi chú về khách hàng..."></textarea>
                            </div>
                            <div class="mt-2 text-end">
                                <button type="button" id="btn-save-note" class="btn btn-sm btn-primary">
                                    <i class="fas fa-save me-1"></i> Lưu ghi chú
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <h6 class="text-muted mb-2">Lịch sử liên hệ</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                    <tr>
                                        <th>Thời gian</th>
                                        <th>Loại liên hệ</th>
                                        <th>Nội dung</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                    </thead>
                                    <tbody id="contact-history">
                                    <tr>
                                        <td colspan="4" class="text-center">Chưa có lịch sử liên hệ</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-2">
                                <button type="button" id="btn-add-contact" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus me-1"></i> Thêm liên hệ mới
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" id="btn-edit-order" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> Chỉnh sửa
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Form thêm liên hệ mới -->
    <div class="modal fade" id="add-contact-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Thêm liên hệ mới</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="contact-form">
                        <input type="hidden" id="contact-order-id">
                        <div class="mb-3">
                            <label for="contact-type" class="form-label">Loại liên hệ</label>
                            <select class="form-select" id="contact-type" required>
                                <option value="">-- Chọn loại --</option>
                                <option value="phone">Gọi điện</option>
                                <option value="sms">SMS</option>
                                <option value="email">Email</option>
                                <option value="zalo">Zalo</option>
                                <option value="other">Khác</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="contact-content" class="form-label">Nội dung</label>
                            <textarea class="form-control" id="contact-content" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="contact-status" class="form-label">Trạng thái</label>
                            <select class="form-select" id="contact-status" required>
                                <option value="">-- Chọn trạng thái --</option>
                                <option value="success">Thành công</option>
                                <option value="no_answer">Không trả lời</option>
                                <option value="callback">Hẹn gọi lại</option>
                                <option value="not_interested">Không có nhu cầu</option>
                                <option value="wrong_number">Sai số điện thoại</option>
                                <option value="other">Khác</option>
                            </select>
                        </div>
                        <div class="mb-3 callback-time-group d-none">
                            <label for="callback-time" class="form-label">Thời gian gọi lại</label>
                            <input type="datetime-local" class="form-control" id="callback-time">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" id="btn-save-contact" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Lưu
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal cập nhật trạng thái chăm sóc -->
    <div class="modal fade" id="care-status-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">Cập nhật trạng thái chăm sóc</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="care-status-form">
                        <input type="hidden" id="care-status-order-id">
                        <input type="hidden" id="care-status-type">

                        <div class="card mb-3 border-0 bg-light">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-circle fa-3x text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="mb-1" id="care-status-customer-name">Tên khách hàng</h5>
                                        <p class="mb-0">
                                            <i class="fas fa-phone-alt text-success me-1"></i>
                                            <span id="care-status-customer-phone">Số điện thoại</span>
                                        </p>
                                    </div>
                                    <div class="status-change-indicator">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-secondary me-2" id="old-status-badge">Chưa có</span>
                                            <i class="fas fa-arrow-right"></i>
                                            <span class="badge bg-primary ms-2" id="new-status-badge">Mới</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="care-contact-type" class="form-label">Phương thức liên hệ</label>
                                    <select class="form-select" id="care-contact-type" required>
                                        <option value="phone">Gọi điện</option>
                                        <option value="sms">SMS</option>
                                        <option value="email">Email</option>
                                        <option value="zalo">Zalo</option>
                                        <option value="other">Khác</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="care-contact-status" class="form-label">Kết quả liên hệ</label>
                                    <select class="form-select" id="care-contact-status" required>
                                        <option value="success">Thành công</option>
                                        <option value="no_answer">Không trả lời</option>
                                        <option value="callback">Hẹn gọi lại</option>
                                        <option value="not_interested">Không có nhu cầu</option>
                                        <option value="wrong_number">Sai số điện thoại</option>
                                        <option value="other">Khác</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="care-contact-content" class="form-label">Nội dung liên hệ <span
                                            class="text-danger">*</span></label>
                                    <div class="quick-msgs mb-2">
                                        <div class="d-flex flex-wrap gap-1 suggested-messages">
                                            <!-- Nút gợi ý sẽ được thêm từ javascript -->
                                        </div>
                                    </div>
                                    <textarea class="form-control" id="care-contact-content" rows="4"
                                              required></textarea>
                                    <div class="form-text mt-1">
                                        <i class="fas fa-lightbulb text-warning"></i> Gợi ý: Nhấp vào các tin nhắn mẫu
                                        phía trên để sử dụng nhanh
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row care-callback-time-group d-none">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="care-callback-time" class="form-label">Thời gian hẹn gọi lại <span
                                            class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="care-callback-time">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="care-reminder" class="form-label">Loại nhắc nhở</label>
                                    <select class="form-select" id="care-reminder">
                                        <option value="none">Không nhắc nhở</option>
                                        <option value="email" selected>Email</option>
                                        <option value="notification">Thông báo</option>
                                        <option value="both">Cả hai</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="add-to-priority" value="1">
                            <label class="form-check-label" for="add-to-priority">
                                <i class="fas fa-star text-warning me-1"></i> Đánh dấu là khách hàng ưu tiên
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" id="btn-save-care-status" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Lưu thông tin
                    </button>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('styles')
    <style>
        /* Payment Status Badges */
        .status-badge {
            padding: 6px 10px;
            font-size: 0.85rem;
            font-weight: 500;
            white-space: nowrap;
            display: inline-block;
        }

        /* Remove the payment-status-badge styles that are no longer needed */

        .pagination {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination .page-item {
            list-style: none;
        }

        .pagination .page-item .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 36px;
            min-width: 36px;
            padding: 0.375rem 0.75rem;
            border-radius: 4px;
            text-decoration: none;
            background-color: #fff;
            border: 1px solid #dee2e6;
            color: #0d6efd;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            color: #fff;
            border-color: #0d6efd;
        }

        .pagination .page-item.disabled .page-link {
            background-color: #f8f9fa;
            color: #6c757d;
            pointer-events: none;
            cursor: not-allowed;
        }

        .pagination .page-item .page-link:hover:not(.disabled) {
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        /* Toggle icon transition */
        .toggle-icon {
            transition: transform 0.3s;
        }

        .fa-rotate-180 {
            transform: rotate(180deg);
        }

        /* Filter header styling */
        .filter-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, .125);
        }

        /* Quick search styling */
        .quick-search-wrapper {
            max-width: 300px;
        }

        .btn-filter-toggle {
            color: #495057;
            background-color: transparent;
            border: none;
            box-shadow: none;
            transition: color 0.2s;
        }

        .btn-filter-toggle:hover {
            color: #0d6efd;
        }

        /* Active filters badge */
        .filter-badge {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            font-size: 10px;
            font-weight: 600;
            border-radius: 50%;
            background-color: #0d6efd;
            color: white;
            margin-left: 0.5rem;
        }

        .badge-pulse {
            animation: badgePulse 1.5s infinite;
        }

        @keyframes badgePulse {
            0% {
                box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
            }
            70% {
                box-shadow: 0 0 0 6px rgba(13, 110, 253, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
            }
        }

        /* Statistics Cards */
        .stats-card {
            border-radius: 10px;
            padding: 20px;
            color: white;
            position: relative;
            margin-bottom: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 2rem;
            opacity: 0.3;
        }

        .stats-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0 0;
        }

        /* CRM Statistics */
        .crm-stat-card {
            display: flex;
            align-items: center;
            background: #fff;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
            transition: all 0.3s;
        }

        .crm-stat-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-color: #dee2e6;
        }

        .crm-stat-card.highlight {
            border-left: 4px solid #dc3545;
            background-color: #fff8f8;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            margin-right: 15px;
            color: white;
        }

        .stat-icon i {
            font-size: 1.5rem;
        }

        .stat-details {
            flex: 1;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0;
            line-height: 1.2;
        }

        .stat-title {
            color: #6c757d;
            margin: 0;
            font-size: 0.9rem;
        }

        /* Table improvements */
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .order-action-buttons .btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Badge styles */
        .badge-pill {
            padding-right: .6em;
            padding-left: .6em;
            border-radius: 10rem;
        }

        /* Follow-up tags */
        .follow-tag {
            display: inline-block;
            padding: 0.25em 0.6em;
            font-size: 0.75em;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .follow-tag.today {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .follow-tag.tomorrow {
            background-color: #fd7e14;
            color: white;
            border-color: #fd7e14;
        }

        .follow-tag.week {
            background-color: #6f42c1;
            color: white;
            border-color: #6f42c1;
        }

        .follow-tag.overdue {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 5px rgba(220, 53, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }

        /* Care Status Popup */
        .status-change-indicator {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.5rem;
            border: 1px dashed #dee2e6;
        }

        .suggested-messages {
            max-height: 130px;
            overflow-y: auto;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
        }

        .btn-quick-message {
            transition: all 0.2s;
            text-align: left;
        }

        .btn-quick-message:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Hide scrollbar for Chrome, Safari and Opera */
        .suggested-messages::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        .suggested-messages {
            -ms-overflow-style: none; /* IE and Edge */
            scrollbar-width: none; /* Firefox */
        }

        /* Priority order styling */
        .priority-row {
            background-color: rgba(255, 193, 7, 0.05) !important;
            position: relative;
        }

        .priority-row:hover {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        .priority-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 5px;
            color: #ffc107;
            animation: star-pulse 1.5s infinite;
        }

        @keyframes star-pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Order Workflow Visualization - Enhanced */
        .order-workflow {
            margin-top: 10px;
        }

        .order-workflow-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 8px;
            padding: 8px;
            overflow-x: auto;
            scrollbar-width: thin;
        }

        .workflow-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
            position: relative;
            font-size: 0.8rem;
            font-weight: 600;
            color: #6c757d;
            min-width: 85px;
            cursor: pointer;
        }

        .workflow-item:hover {
            background-color: #f1f8ff;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-color: #bed4ff;
        }

        .workflow-icon {
            margin-bottom: 5px;
            font-size: 1rem;
        }

        .workflow-item.active {
            background-color: #e9f0ff;
            border-color: #bed4ff;
            color: #0d6efd;
            transform: translateY(-3px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .workflow-item.active .workflow-icon {
            color: #0d6efd;
        }

        .workflow-arrow {
            color: #adb5bd;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }

        .order-workflow-legend {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
        }

        /* Status-specific colors */
        .workflow-item[data-status=""] .workflow-icon {
            color: #6c757d;
        }

        .workflow-item[data-status="new"] .workflow-icon {
            color: #0d6efd;
        }

        .workflow-item[data-status="contacted"] .workflow-icon {
            color: #6610f2;
        }

        .workflow-item[data-status="interested"] .workflow-icon {
            color: #198754;
        }

        .workflow-item[data-status="callback"] .workflow-icon {
            color: #fd7e14;
        }

        .workflow-item[data-status="not_interested"] .workflow-icon {
            color: #dc3545;
        }

        .workflow-item[data-status="closed"] .workflow-icon {
            color: #6c757d;
        }

        .workflow-item[data-status="converted"] .workflow-icon {
            color: #20c997;
        }

        /* Count badges for workflow items */
        .count-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            background-color: #ff3e1d;
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 18px;
            height: 18px;
            border-radius: 9px;
            padding: 0 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .workflow-item.active .count-badge {
            background-color: #0d6efd;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .order-workflow-container {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 10px;
                -webkit-overflow-scrolling: touch;
            }

            .workflow-item {
                min-width: 70px;
                padding: 6px 8px;
                font-size: 0.75rem;
            }

            .workflow-icon {
                font-size: 0.9rem;
            }
        }

        /* Hide scrollbar for the workflow container on some browsers */
        .order-workflow-container::-webkit-scrollbar {
            height: 4px;
        }

        .order-workflow-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .order-workflow-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .order-workflow-container::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* Responsive styles for pagination */
        @media (max-width: 576px) {
            .d-flex.justify-content-between.align-items-center.p-3.border-top {
                flex-direction: column;
                gap: 1rem;
            }

            .pagination {
                justify-content: center;
                flex-wrap: wrap;
            }

            .pagination .page-item .page-link {
                height: 32px;
                min-width: 32px;
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        $(document).ready(function () {
            // Remove the payment status badge click handler since badges are now just display-only

            // Handle pagination link clicks to preserve filters
            $(document).on('click', '.pagination .page-link', function (e) {
                e.preventDefault();
                const url = new URL($(this).attr('href'));
                const currentUrl = new URL(window.location.href);

                // Preserve all current query parameters except page
                const searchParams = currentUrl.searchParams;
                searchParams.forEach((value, key) => {
                    if (key !== 'page') {
                        url.searchParams.set(key, value);
                    }
                });

                window.location.href = url.toString();
            });

            // Handle workflow item clicks for filtering
            // $('.workflow-item').click(function(e) {
            //     e.preventDefault();
            //     const status = $(this).data('status');
            //
            //     // Always show the filter section
            //     if (!$('#filterCollapse').hasClass('show')) {
            //         $('#filterCollapse').addClass('show');
            //         $('.toggle-icon').addClass('fa-rotate-180');
            //     }
            //
            //     // Update the care_status filter in the form
            //     $('#care_status').val(status);
            //
            //     // Update the UI to show which item is active
            //     $('.workflow-item').removeClass('active');
            //     $(this).addClass('active');
            //
            //     // Submit the form
            //     $('#filter-form').submit();
            // });

            // Count and display active filters
            function updateFilterCount() {
                const urlParams = new URLSearchParams(window.location.search);
                let activeFilters = 0;
                let hasFilters = false;

                // Count filters excluding the search parameter
                ['webinar_id', 'payment_status', 'care_status', 'assigned_to',
                    'follow_type', 'follow_date', 'sort_by', 'priority'].forEach(param => {
                    if (urlParams.has(param) && urlParams.get(param)) {
                        activeFilters++;
                        hasFilters = true;
                    }
                });

                // Include search parameter if it exists and is not empty
                if (urlParams.has('search') && urlParams.get('search')) {
                    activeFilters++;
                    hasFilters = true;
                }

                // Update the badge and clear filters button
                if (activeFilters > 0) {
                    $('#filter-badge').text(activeFilters).show();
                    $('#btn-clear-filters').show();

                    // If the filter collapse is not shown, add a pulse animation to the badge
                    if (!$('#filterCollapse').hasClass('show')) {
                        $('#filter-badge').addClass('badge-pulse');
                    } else {
                        $('#filter-badge').removeClass('badge-pulse');
                    }
                } else {
                    $('#filter-badge').hide();
                    $('#btn-clear-filters').hide();
                }

                return hasFilters;
            }

            // Clear filter button handler
            $('#btn-clear-filters').click(function (e) {
                e.preventDefault();
                window.location.href = "{{ route('orders.index') }}";
            });

            // Initialize the workflow status highlighting and tooltips
            function initializeWorkflow() {
                const urlParams = new URLSearchParams(window.location.search);
                const currentStatus = urlParams.get('care_status');

                if (currentStatus) {
                    $('.workflow-item').removeClass('active');
                    $(`.workflow-item[data-status="${currentStatus}"]`).addClass('active');
                }

                // Initialize tooltips
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }

            // Call initialization functions
            // initializeWorkflow();
            updateFilterCount();

            // Handle filter collapse toggle
            $('#filterCollapse').on('show.bs.collapse', function () {
                $('.toggle-icon').addClass('fa-rotate-180');
                // Sync search fields
                $('#search').val($('#quick-search').val());
                // Remove pulse animation when filters are shown
                $('#filter-badge').removeClass('badge-pulse');
            });

            $('#filterCollapse').on('hide.bs.collapse', function () {
                $('.toggle-icon').removeClass('fa-rotate-180');
                // Add pulse animation to badge if there are active filters
                if (parseInt($('#filter-badge').text()) > 0) {
                    $('#filter-badge').addClass('badge-pulse');
                }
            });

            // Sync main search field to quick search
            $('#quick-search').on('input', function () {
                $('#search').val($(this).val());
            });

            // Monitor changes to filter-select elements
            $('.filter-select').on('change', function () {
                const filterName = $(this).data('filter');
                const value = $(this).val();

                if (value) {
                    $(this).addClass('active-filter');
                } else {
                    $(this).removeClass('active-filter');
                }
            });

            // If filters are active, expand the filter panel
            if (updateFilterCount()) {
                $('#filterCollapse').collapse('show');
            }

            // Xử lý thay đổi trạng thái đơn hàng
            $('.status-select').change(function () {
                const orderId = $(this).data('id');
                const status = $(this).val();

                $.ajax({
                    url: `/orders/${orderId}/update-status`,
                    type: 'POST',
                    data: {
                        status: status,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        showAlert('Thành công', 'Trạng thái đơn hàng đã được cập nhật', 'success');

                        // Cập nhật thống kê
                        updateStats();
                    },
                    error: function (error) {
                        showAlert('Lỗi', 'Không thể cập nhật trạng thái đơn hàng', 'error');
                        console.error(error);
                    }
                });
            });

            // Xử lý thay đổi trạng thái chăm sóc khách hàng
            $('.care-status-select').change(function () {
                const orderId = $(this).data('id');
                const status = $(this).val();
                const $select = $(this);

                // Lưu trạng thái trước đó để có thể khôi phục nếu có lỗi
                const previousStatus = $select.data('previous-value') || '';

                // Statuses requiring contact notes
                const notesRequiredStatuses = ['contacted', 'interested', 'not_interested', 'callback', 'converted', 'closed'];

                if (notesRequiredStatuses.includes(status)) {
                    // Show the contact note modal with the appropriate info
                    const customerName = $select.closest('tr').find('td:nth-child(2)').text().trim();
                    const customerPhone = $select.closest('tr').find('td:nth-child(3)').text().trim().split('\n')[0].trim();

                    $('#care-status-customer-name').text(customerName);
                    $('#care-status-customer-phone').text(customerPhone);
                    $('#care-status-order-id').val(orderId);
                    $('#care-status-type').val(status);

                    // Show the status change indicator
                    const statusTexts = {
                        'new': 'Mới',
                        'contacted': 'Đã liên hệ',
                        'interested': 'Quan tâm',
                        'not_interested': 'Không quan tâm',
                        'callback': 'Gọi lại sau',
                        'converted': 'Đã chuyển đổi',
                        'closed': 'Đã đóng'
                    };

                    const statusColors = {
                        'new': 'bg-secondary',
                        'contacted': 'bg-primary',
                        'interested': 'bg-success',
                        'not_interested': 'bg-danger',
                        'callback': 'bg-warning',
                        'converted': 'bg-info',
                        'closed': 'bg-dark'
                    };

                    // Update the status change indicator
                    $('#old-status-badge')
                        .text(statusTexts[previousStatus] || 'Chưa có')
                        .removeClass()
                        .addClass(`badge ${statusColors[previousStatus] || 'bg-secondary'} me-2`);

                    $('#new-status-badge')
                        .text(statusTexts[status])
                        .removeClass()
                        .addClass(`badge ${statusColors[status]} ms-2`);

                    // Set default contact type based on status
                    if (status === 'callback') {
                        $('#care-contact-type').val('phone');
                        $('.care-callback-time-group').removeClass('d-none');

                        // Default callback time to tomorrow at business hours
                        const tomorrow = new Date();
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        tomorrow.setHours(9, 0, 0, 0);
                        $('#care-callback-time').val(tomorrow.toISOString().slice(0, 16));
                    } else {
                        $('#care-contact-type').val('phone');
                        $('.care-callback-time-group').addClass('d-none');
                    }

                    // Set default contact status based on CRM status
                    const statusMapping = {
                        'contacted': 'success',
                        'interested': 'success',
                        'not_interested': 'not_interested',
                        'callback': 'callback',
                        'converted': 'success',
                        'closed': 'success'
                    };

                    if (statusMapping[status]) {
                        $('#care-contact-status').val(statusMapping[status]);
                    }

                    // Show predefined messages based on the status
                    populateSuggestedMessages(status);

                    // Reset form
                    $('#care-contact-content').val('');
                    $('#add-to-priority').prop('checked', false);

                    // Show the modal
                    $('#care-status-modal').modal('show');

                    // Set this back if user cancels
                    $select.data('previous-value', previousStatus);

                    // If cancel modal, revert to previous status
                    $('#care-status-modal').one('hidden.bs.modal', function () {
                        $select.val(previousStatus);
                    });

                    return;
                }

                $.ajax({
                    url: `/orders/${orderId}/quick-update-care`,
                    type: 'POST',
                    data: {
                        care_status: status,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        showAlert('Thành công', 'Trạng thái chăm sóc đã được cập nhật', 'success');
                        updateStats();
                    },
                    error: function (error) {
                        showAlert('Lỗi', 'Không thể cập nhật trạng thái chăm sóc', 'error');
                        console.error(error);
                        // Reset the select to its previous value
                        $select.val(previousStatus);
                    }
                });
            });

            // Store the previous value of the care status select when focusing on it
            $('.care-status-select').focus(function () {
                $(this).data('previous-value', $(this).val());
            });

            // Populate suggested messages based on status
            function populateSuggestedMessages(status) {
                const $container = $('.suggested-messages');
                $container.empty();

                const messages = {
                    'contacted': [
                        'Đã gọi điện cho khách hàng để tư vấn sản phẩm.',
                        'Đã liên hệ với khách hàng để cung cấp thông tin chi tiết.',
                        'Đã tư vấn về sản phẩm/dịch vụ cho khách hàng.',
                        'Gọi điện giới thiệu sản phẩm, khách hàng lắng nghe và đặt một số câu hỏi.'
                    ],
                    'interested': [
                        'Khách hàng tỏ ra quan tâm đến sản phẩm/dịch vụ.',
                        'Khách hàng muốn biết thêm thông tin về giá cả và tính năng.',
                        'Khách đang cân nhắc, có khả năng sẽ mua sản phẩm.',
                        'Khách rất thích tính năng [X] và đang xem xét đầu tư.'
                    ],
                    'not_interested': [
                        'Khách hàng hiện không có nhu cầu sử dụng sản phẩm/dịch vụ.',
                        'Khách hàng cho biết đã mua sản phẩm tương tự từ đơn vị khác.',
                        'Khách không quan tâm đến sản phẩm vào lúc này.',
                        'Khách cho biết sản phẩm không phù hợp với nhu cầu hiện tại.'
                    ],
                    'callback': [
                        'Khách hàng đề nghị gọi lại sau khi đã cân nhắc.',
                        'Khách đang bận, hẹn gọi lại sau.',
                        'Khách cần thời gian để trao đổi với người khác trước khi quyết định.',
                        'Khách hẹn gọi lại vào [thời gian] sau khi đã tham khảo thêm thông tin.'
                    ],
                    'converted': [
                        'Khách hàng đã đồng ý mua sản phẩm/dịch vụ.',
                        'Đã hoàn tất thỏa thuận với khách hàng.',
                        'Khách hàng đã chuyển khoản thanh toán, đang xử lý đơn hàng.',
                        'Khách đã xác nhận mua hàng, đang hướng dẫn thủ tục thanh toán.'
                    ],
                    'closed': [
                        'Đã đóng hồ sơ khách hàng do không có nhu cầu.',
                        'Khách hàng đã từ chối dịch vụ nhiều lần, ngừng tiếp cận.',
                        'Đã hoàn tất quá trình chăm sóc và bàn giao cho phòng CSKH.',
                        'Khách hàng không còn phù hợp với nhóm đối tượng mục tiêu.'
                    ],
                    'new': [
                        'Xác nhận thông tin liên hệ của khách hàng mới.',
                        'Chào mừng khách hàng mới, giới thiệu sơ lược về doanh nghiệp.',
                        'Gọi lần đầu để giới thiệu dịch vụ cho khách hàng.',
                        'Gửi thông tin sản phẩm cho khách hàng mới đăng ký.'
                    ]
                };

                const statusMessages = messages[status] || [];

                statusMessages.forEach(message => {
                    const $button = $('<button>')
                        .attr('type', 'button')
                        .addClass('btn btn-sm btn-outline-secondary mb-1 me-1 btn-quick-message')
                        .text(message);

                    $button.click(function () {
                        $('#care-contact-content').val(message);
                    });

                    $container.append($button);
                });
            }

            // Xử lý khi thay đổi trạng thái liên hệ trong modal
            $('#care-contact-status').change(function () {
                const status = $(this).val();

                if (status === 'callback') {
                    $('.care-callback-time-group').removeClass('d-none');

                    // Default callback time to tomorrow at business hours if not already set
                    if (!$('#care-callback-time').val()) {
                        const tomorrow = new Date();
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        tomorrow.setHours(9, 0, 0, 0);
                        $('#care-callback-time').val(tomorrow.toISOString().slice(0, 16));
                    }
                } else {
                    $('.care-callback-time-group').addClass('d-none');
                }
            });

            // Lưu thông tin trạng thái chăm sóc
            $('#btn-save-care-status').click(function () {
                console.log(123)
                const orderId = $('#care-status-order-id').val();
                const careStatus = $('#care-status-type').val();
                const contactType = $('#care-contact-type').val();
                const contactContent = $('#care-contact-content').val();
                const contactStatus = $('#care-contact-status').val();
                const callbackTime = $('#care-callback-time').val();
                const isPriority = $('#add-to-priority').is(':checked');
                const reminderType = $('#care-reminder').val();

                if (!contactContent) {
                    showAlert('Lỗi', 'Vui lòng nhập nội dung liên hệ', 'error');
                    return;
                }

                if (contactStatus === 'callback' && !callbackTime) {
                    showAlert('Lỗi', 'Vui lòng chọn thời gian gọi lại', 'error');
                    return;
                }

                // Dữ liệu cập nhật
                const data = {
                    care_status: careStatus,
                    contact_type: contactType,
                    content: contactContent,
                    status: contactStatus,
                    is_priority: isPriority ? 1 : 0,
                    reminder_type: reminderType,
                    _token: '{{ csrf_token() }}'
                };

                if (contactStatus === 'callback') {
                    data.callback_time = callbackTime;
                }

                $.ajax({
                    url: `/orders/${orderId}/add-care-history-with-status`,
                    type: 'POST',
                    data: data,
                    success: function (response) {
                        $('#care-status-modal').modal('hide');
                        showAlert('Thành công', 'Đã cập nhật trạng thái chăm sóc và lưu lịch sử liên hệ', 'success');

                        // Reload trang để cập nhật thông tin hiển thị
                        setTimeout(function () {
                            window.location.reload();
                        }, 1000);
                    },
                    error: function (error) {
                        showAlert('Lỗi', 'Không thể cập nhật trạng thái', 'error');
                        console.error(error);

                        // Reset selects to previous values
                        const $select = $(`.care-status-select[data-id="${orderId}"]`);
                        $select.val($select.data('previous-value'));
                    }
                });
            });

            // Xử lý gán nhân viên telesale cho đơn hàng
            $('.btn-assign-telesale').click(function () {
                const orderId = $(this).data('id');

                // Hiển thị modal để chọn telesale
                Swal.fire({
                    title: 'Gán cho nhân viên telesale',
                    html: `
                    <select id="telesale-select" class="form-select mb-3">
                        <option value="">-- Chọn nhân viên --</option>
                        @foreach($teleSales as $agent)
                    <option value="{{ $agent->id }}">{{ $agent->name }}</option>
                        @endforeach
                    </select>
                `,
                    showCancelButton: true,
                    confirmButtonText: 'Gán',
                    cancelButtonText: 'Hủy',
                    preConfirm: () => {
                        const telesaleId = document.getElementById('telesale-select').value;
                        if (!telesaleId) {
                            Swal.showValidationMessage('Vui lòng chọn nhân viên telesale');
                            return false;
                        }
                        return telesaleId;
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        const telesaleId = result.value;

                        $.ajax({
                            url: `/orders/${orderId}/quick-assign`,
                            type: 'POST',
                            data: {
                                telesale_id: telesaleId,
                                _token: '{{ csrf_token() }}'
                            },
                            success: function (response) {
                                showAlert('Thành công', response.message, 'success');
                                // Reload trang để cập nhật thông tin
                                setTimeout(function () {
                                    window.location.reload();
                                }, 1500);
                            },
                            error: function (error) {
                                showAlert('Lỗi', 'Không thể gán đơn hàng', 'error');
                                console.error(error);
                            }
                        });
                    }
                });
            });

            // Xử lý xem nhanh thông tin đơn hàng
            $('.btn-quick-view').click(function () {
                const orderId = $(this).data('id');

                $.ajax({
                    url: `/orders/${orderId}/details`,
                    type: 'GET',
                    success: function (order) {
                        $('#order-id').text(order.id);
                        $('#modal-name').text(order.name);
                        $('#modal-phone').text(order.phone);
                        $('#modal-email').text(order.email || 'N/A');
                        $('#modal-product').text(order.product_name);
                        $('#modal-price').text(new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND'
                        }).format(order.price));
                        $('#modal-time').text(new Date(order.purchase_time).toLocaleString('vi-VN'));
                        $('#modal-type').text(order.order_type === 'form' ? 'Form' : 'Thanh toán QR');
                        $('#modal-transaction').text(order.transaction_id || 'N/A');
                        $('#modal-payment-note').text(order.payment_note || 'N/A');

                        let statusHtml = '';
                        if (order.payment_status === 'paid') {
                            statusHtml = '<span class="badge bg-success">Đã thanh toán</span>';
                        } else if (order.payment_status === 'pending') {
                            statusHtml = '<span class="badge bg-warning">Đang xử lý</span>';
                        } else {
                            statusHtml = '<span class="badge bg-danger">Thất bại</span>';
                        }
                        $('#modal-status').html(statusHtml);

                        // Hiển thị trạng thái chăm sóc
                        let careStatusHtml = '';
                        if (order.care_status) {
                            careStatusHtml = `<span class="badge bg-info">${order.care_status_text}</span>`;
                            if (order.assigned_to && order.assigned_user) {
                                careStatusHtml += `<div class="mt-1 small text-muted">Nhân viên: ${order.assigned_user.name}</div>`;
                            }
                            if (order.next_follow_date) {
                                careStatusHtml += `<div class="mt-1 small text-muted">Gọi lại: ${new Date(order.next_follow_date).toLocaleString('vi-VN')}</div>`;
                            }
                        } else {
                            careStatusHtml = '<span class="badge bg-secondary">Chưa xử lý</span>';
                        }
                        $('#modal-care-status').html(careStatusHtml);

                        // Cập nhật URL nút chỉnh sửa
                        $('#btn-edit-order').attr('href', `/orders/${order.id}/edit`);

                        // Hiển thị modal
                        $('#order-detail-modal').modal('show');

                        // Tải lịch sử liên hệ
                        loadContactHistory(orderId);

                        // Tải ghi chú
                        loadCareNotes(orderId);
                    },
                    error: function (error) {
                        showAlert('Lỗi', 'Không thể tải thông tin đơn hàng', 'error');
                        console.error(error);
                    }
                });
            });

            $('.btn-delete').click(function () {
                const orderId = $(this).data('id');

                Swal.fire({
                    title: 'Xác nhận xóa?',
                    text: "Bạn không thể hoàn tác hành động này!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Xóa',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $("#delete-order").submit();
                        // $.ajax({
                        //     url: `/orders/${orderId}`,
                        //     type: 'DELETE',
                        //     success: function () {
                        //         showAlert('Thành công', 'Đơn hàng đã được xóa', 'success');
                        //         $(`#order-row-${orderId}`).fadeOut();
                        //         updateStats();
                        //     },
                        //     error: function (error) {
                        //         showAlert('Lỗi', 'Không thể xóa đơn hàng', 'error');
                        //         console.error(error);
                        //     }
                        // });
                    }
                });
            });

            // Hàm cập nhật thống kê
            function updateStats() {
                $('.refresh-stats').html('<i class="fas fa-sync-alt fa-spin"></i>');

                $.ajax({
                    url: '/orders/stats',
                    type: 'GET',
                    success: function (stats) {
                        if (stats.total) $('#total-count').text(stats.total);
                        if (stats.pending) $('#pending-count').text(stats.pending);
                        if (stats.paid) $('#paid-count').text(stats.paid);
                        if (stats.new) $('#new-count').text(stats.new);
                        if (stats.contacted) $('#contacted-count').text(stats.contacted);
                        if (stats.callback) $('#callback-count').text(stats.callback);
                        if (stats.today_follow) $('#today-count').text(stats.today_follow);
                        if (stats.overdue) $('#overdue-count').text(stats.overdue);
                    },
                    error: function (error) {
                        console.error('Error updating stats:', error);
                    }
                });

                setTimeout(() => {
                    $(this).find('i').removeClass('fa-spin');
                }, 1000);
            }

            // Hàm tải lịch sử liên hệ
            function loadContactHistory(orderId) {
                $('#contact-history').html('<tr><td colspan="4" class="text-center">Đang tải...</td></tr>');

                $.ajax({
                    url: `/orders/${orderId}/care-history`,
                    type: 'GET',
                    success: function (history) {
                        if (history.length === 0) {
                            $('#contact-history').html('<tr><td colspan="4" class="text-center">Chưa có lịch sử liên hệ</td></tr>');
                            return;
                        }

                        let html = '';
                        history.forEach(item => {
                            let statusBadge = '';
                            switch (item.status) {
                                case 'success':
                                    statusBadge = '<span class="badge bg-success">Thành công</span>';
                                    break;
                                case 'no_answer':
                                    statusBadge = '<span class="badge bg-secondary">Không trả lời</span>';
                                    break;
                                case 'callback':
                                    statusBadge = '<span class="badge bg-info">Hẹn gọi lại</span>';
                                    break;
                                case 'not_interested':
                                    statusBadge = '<span class="badge bg-danger">Không quan tâm</span>';
                                    break;
                                case 'wrong_number':
                                    statusBadge = '<span class="badge bg-warning">Sai số</span>';
                                    break;
                                default:
                                    statusBadge = '<span class="badge bg-dark">Khác</span>';
                            }

                            const callbackInfo = item.callback_time
                                ? `<div class="mt-1"><span class="follow-tag today">Gọi lại: ${new Date(item.callback_time).toLocaleString('vi-VN')}</span></div>`
                                : '';

                            html += `
                            <tr>
                                <td style="width: 20%">
                                    ${new Date(item.created_at).toLocaleString('vi-VN')}
                                    <div class="small text-muted">${item.user ? item.user.name : 'N/A'}</div>
                                </td>
                                <td style="width: 15%">${item.contact_type_text}</td>
                                <td>${item.content}${callbackInfo}</td>
                                <td style="width: 15%">${statusBadge}</td>
                            </tr>
                        `;
                        });

                        $('#contact-history').html(html);
                    },
                    error: function (error) {
                        $('#contact-history').html('<tr><td colspan="4" class="text-center text-danger">Không thể tải lịch sử</td></tr>');
                        console.error(error);
                    }
                });
            }

            // Hàm tải ghi chú
            function loadCareNotes(orderId) {
                $.ajax({
                    url: `/orders/${orderId}/care-notes`,
                    type: 'GET',
                    success: function (notes) {
                        if (notes.length > 0) {
                            const latestNote = notes[0];
                            $('#crm-note').val(latestNote.content);
                        } else {
                            $('#crm-note').val('');
                        }
                    }
                });
            }

            // Nút thêm liên hệ mới
            $('#btn-add-contact').click(function () {
                $('#contact-order-id').val($('#order-id').text());
                $('#contact-form')[0].reset();
                $('#add-contact-modal').modal('show');
            });

            // Lưu liên hệ mới
            $('#btn-save-contact').click(function () {
                const orderId = $('#contact-order-id').val();
                const type = $('#contact-type').val();
                const content = $('#contact-content').val();
                const status = $('#contact-status').val();
                const callbackTime = $('#callback-time').val();

                if (!type || !content || !status) {
                    showAlert('Lỗi', 'Vui lòng điền đầy đủ thông tin', 'error');
                    return;
                }

                // Nếu status là callback nhưng không có thời gian
                if (status === 'callback' && !callbackTime) {
                    showAlert('Lỗi', 'Vui lòng chọn thời gian gọi lại', 'error');
                    return;
                }

                const data = {
                    contact_type: type,
                    content: content,
                    status: status,
                    _token: '{{ csrf_token() }}'
                };

                if (status === 'callback') {
                    data.callback_time = callbackTime;
                }

                $.ajax({
                    url: `/orders/${orderId}/add-care-history`,
                    type: 'POST',
                    data: data,
                    success: function (response) {
                        loadContactHistory(orderId);
                        $('#add-contact-modal').modal('hide');
                        showAlert('Thành công', 'Đã lưu thông tin liên hệ', 'success');

                        // Reload trang để cập nhật thông tin hiển thị
                        setTimeout(function () {
                            window.location.reload();
                        }, 1500);
                    },
                    error: function (error) {
                        showAlert('Lỗi', 'Không thể lưu thông tin liên hệ', 'error');
                        console.error(error);
                    }
                });
            });

            // Show callback time field when callback status is selected
            $('#contact-status').change(function () {
                if ($(this).val() === 'callback') {
                    $('.callback-time-group').removeClass('d-none');
                } else {
                    $('.callback-time-group').addClass('d-none');
                }
            });

            // Nút lưu ghi chú
            $('#btn-save-note').click(function () {
                const note = $('#crm-note').val();
                const orderId = $('#order-id').text();

                if (!note) {
                    showAlert('Lỗi', 'Vui lòng nhập nội dung ghi chú', 'error');
                    return;
                }

                $.ajax({
                    url: `/orders/${orderId}/add-care-note`,
                    type: 'POST',
                    data: {
                        content: note,
                        priority: 'medium',
                        is_pinned: false,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        showAlert('Thành công', 'Đã lưu ghi chú', 'success');
                    },
                    error: function (error) {
                        showAlert('Lỗi', 'Không thể lưu ghi chú', 'error');
                        console.error(error);
                    }
                });
            });

            // Xuất Excel
            $('#btn-export').click(function () {
                const queryParams = new URLSearchParams(window.location.search);
                queryParams.append('export', 'excel');

                window.location.href = `/orders/export?${queryParams.toString()}`;
            });

            // Hiển thị thông báo
            function showAlert(title, message, type) {
                Swal.fire({
                    title: title,
                    text: message,
                    icon: type,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true
                });
            }

            // Hiển thị tooltip
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
@endpush
