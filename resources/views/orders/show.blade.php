@extends('layouts.app')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Chi tiết Order #{{ $order->id }}</h5>
        <div>
            <a href="{{ route('orders.edit', $order) }}" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i> Chỉnh sửa
            </a>
            <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-4">
                <h6 class="text-muted mb-3">Thông tin khách hàng</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">Tên</th>
                        <td>{{ $order->name }}</td>
                    </tr>
                    <tr>
                        <th>Số điện thoại</th>
                        <td>{{ $order->phone }}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td>{{ $order->email }}</td>
                    </tr>
                </table>
            </div>

            <div class="col-md-6 mb-4">
                <h6 class="text-muted mb-3">Thông tin đơn hàng</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">Tên sản phẩm</th>
                        <td>{{ $order->product_name }}</td>
                    </tr>
                    <tr>
                        <th>Giá mua</th>
                        <td>{{ number_format($order->price, 0, ',', '.') }} đ</td>
                    </tr>
                    <tr>
                        <th>Thời gian mua</th>
                        <td>{{ $order->purchase_time->format('d/m/Y H:i:s') }}</td>
                    </tr>
                </table>
            </div>

            <div class="col-md-6 mb-4">
                <h6 class="text-muted mb-3">Thông tin thanh toán</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">Loại đơn</th>
                        <td>
                            @if($order->order_type == 'form')
                                <span class="badge bg-primary">Form</span>
                            @else
                                <span class="badge bg-info">Thanh toán QR</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Transaction ID</th>
                        <td>{{ $order->transaction_id ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <th>Nội dung CK</th>
                        <td>{{ $order->payment_note ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <th>Trạng thái</th>
                        <td>
                            @if($order->payment_status == 'paid')
                                <span class="badge bg-success">Đã thanh toán</span>
                            @elseif($order->payment_status == 'pending')
                                <span class="badge bg-warning">Đang xử lý</span>
                            @else
                                <span class="badge bg-danger">Thất bại</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>

            <div class="col-md-6 mb-4">
                <h6 class="text-muted mb-3">Thông tin khác</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">Webinar</th>
                        <td>
                            @if($order->webinar)
                                <a href="{{ route('webinars.show', $order->webinar) }}">
                                    {{ $order->webinar->title }}
                                </a>
                            @else
                                N/A
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>Ngày tạo</th>
                        <td>{{ $order->created_at->format('d/m/Y H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>Cập nhật lần cuối</th>
                        <td>{{ $order->updated_at->format('d/m/Y H:i:s') }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="mt-4 d-flex justify-content-between">
            <form action="{{ route('orders.destroy', $order) }}" method="POST" onsubmit="return confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> Xóa đơn hàng
                </button>
            </form>

            <a href="{{ route('orders.edit', $order) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i> Chỉnh sửa
            </a>
        </div>
    </div>
</div>
@endsection
