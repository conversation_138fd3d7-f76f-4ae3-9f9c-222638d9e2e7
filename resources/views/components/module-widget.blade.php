@php
    $enabledModules = getEnabledModules()->filter(function($module) {
        return $module->hasPermission();
    });
@endphp

@if($enabledModules->count() > 0)
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-tools me-2"></i>
            Tiện ích đã kích hoạt
            <span class="badge bg-primary ms-2">{{ $enabledModules->count() }}</span>
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            @foreach($enabledModules->take(8) as $module)
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="module-widget-item">
                        @if($module->route)
                            <a href="{{ route($module->route) }}" class="text-decoration-none">
                                <div class="module-widget-card">
                                    <div class="module-icon">
                                        <i class="{{ $module->icon }}"></i>
                                    </div>
                                    <div class="module-info">
                                        <h6 class="module-title">{{ $module->display_name }}</h6>
                                        <p class="module-desc">{{ Str::limit($module->description, 50) }}</p>
                                    </div>
                                </div>
                            </a>
                        @else
                            <div class="module-widget-card disabled">
                                <div class="module-icon">
                                    <i class="{{ $module->icon }}"></i>
                                </div>
                                <div class="module-info">
                                    <h6 class="module-title">{{ $module->display_name }}</h6>
                                    <p class="module-desc text-muted">Đang phát triển...</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        
        @if($enabledModules->count() > 8)
            <div class="text-center mt-3">
                <a href="{{ route('admin.modules.index') }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Xem thêm {{ $enabledModules->count() - 8 }} module khác
                </a>
            </div>
        @endif
        
        <div class="text-center mt-3">
            <a href="{{ route('admin.modules.index') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-cog me-1"></i>
                Quản lý Module
            </a>
        </div>
    </div>
</div>

<style>
.module-widget-item {
    height: 100%;
}

.module-widget-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.module-widget-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.module-widget-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.module-widget-card.disabled:hover {
    transform: none;
    box-shadow: none;
    border-color: var(--border-color);
}

.module-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    color: white;
    font-size: 1.5rem;
}

.module-widget-card:hover .module-icon {
    background: var(--primary-hover);
    transform: scale(1.1);
}

.module-info {
    flex: 1;
}

.module-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.module-desc {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0;
    line-height: 1.3;
}

.module-widget-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.module-widget-card:hover::before {
    left: 100%;
}

@media (max-width: 768px) {
    .module-widget-card {
        padding: 0.75rem;
    }
    
    .module-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
    
    .module-title {
        font-size: 0.8rem;
    }
    
    .module-desc {
        font-size: 0.7rem;
    }
}
</style>
@endif 