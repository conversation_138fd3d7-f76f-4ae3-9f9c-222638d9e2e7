<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Không có webinar - {{ $smartLink->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .no-webinar-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .icon-container {
            background: #f8f9fa;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
        }
    </style>
</head>
<body>
    <div class="no-webinar-card">
        <div class="icon-container">
            <i class="fas fa-calendar-times fa-3x text-muted"></i>
        </div>
        
        <h2 class="mb-4">Hiện tại không có webinar nào</h2>
        
        <div class="alert alert-info">
            <h5 class="alert-heading">{{ $smartLink->title }}</h5>
            @if($smartLink->description)
                <p class="mb-0">{{ $smartLink->description }}</p>
            @endif
        </div>
        
        <p class="text-muted mb-4">
            Tất cả các webinar trong lịch trình đã kết thúc hoặc chưa có lịch trình mới được thiết lập.
        </p>
        
        <div class="d-grid gap-2">
            <button class="btn btn-primary" onclick="checkAgain()">
                <i class="fas fa-refresh me-2"></i>Kiểm tra lại
            </button>
            <button class="btn btn-outline-secondary" onclick="goBack()">
                <i class="fas fa-arrow-left me-2"></i>Quay lại
            </button>
        </div>
        
        <hr class="my-4">
        
        <div class="text-center">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Nếu bạn cho rằng đây là lỗi, vui lòng liên hệ với người quản lý.
            </small>
        </div>
    </div>

    <script>
        function checkAgain() {
            window.location.reload();
        }
        
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }
        
        // Auto-refresh every 5 minutes to check for new webinars
        setInterval(() => {
            window.location.reload();
        }, 5 * 60 * 1000);
    </script>
</body>
</html>
