@extends('layouts.app')

@section('title', 'Link Smart')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>Quản lý Link Smart
                    </h5>
                    <a href="{{ route('smart-links.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Tạo Link Smart
                    </a>
                </div>
                <div class="card-body">
                    @if($smartLinks->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tiêu đề</th>
                                        <th>Link Smart</th>
                                        <th><PERSON><PERSON> lư<PERSON>t click</th>
                                        <th>Trạng thái</th>
                                        <th><PERSON><PERSON><PERSON> hiện tại</th>
                                        <th><PERSON><PERSON><PERSON> t<PERSON><PERSON> c<PERSON> cu<PERSON></th>
                                        <th style="width: 150px;"><PERSON><PERSON> tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($smartLinks as $smartLink)
                                        <tr>
                                            <td class="fw-medium">
                                                <a href="{{ route('smart-links.show', $smartLink) }}" class="text-decoration-none">
                                                    {{ $smartLink->title }}
                                                </a>
                                                @if($smartLink->description)
                                                    <small class="text-muted d-block">{{ Str::limit($smartLink->description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="text" class="form-control form-control-sm"
                                                           value="{{ $smartLink->full_url }}"
                                                           id="smart-url-{{ $smartLink->id }}" readonly>
                                                    <button class="btn btn-outline-secondary btn-sm copy-btn" type="button"
                                                            data-clipboard-target="#smart-url-{{ $smartLink->id }}" title="Sao chép">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info text-white">
                                                    <i class="fas fa-mouse-pointer me-1"></i>{{ number_format($smartLink->click_count) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($smartLink->is_active)
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>Hoạt động
                                                    </span>
                                                @else
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-pause-circle me-1"></i>Tạm dừng
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $currentWebinar = $smartLink->getCurrentWebinar();
                                                @endphp
                                                @if($currentWebinar)
                                                    <span class="badge badge-success">{{ $currentWebinar->title }}</span>
                                                @else
                                                    @php
                                                        $nextWebinar = $smartLink->getNextWebinar();
                                                    @endphp
                                                    @if($nextWebinar)
                                                        <span class="badge badge-warning">Sắp tới: {{ $nextWebinar->title }}</span>
                                                    @else
                                                        <span class="badge badge-secondary">Không có</span>
                                                    @endif
                                                @endif
                                            </td>
                                            <td>
                                                @if($smartLink->last_accessed_at)
                                                    <small>{{ $smartLink->last_accessed_at->format('d/m/Y H:i') }}</small>
                                                @else
                                                    <small class="text-muted">Chưa có</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('smart-links.show', $smartLink) }}"
                                                       class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('smart-links.edit', $smartLink) }}"
                                                       class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="confirmDelete('{{ $smartLink->hashId() }}')" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>

                                                <form id="delete-form-{{ $smartLink->hashId() }}"
                                                      action="{{ route('smart-links.destroy', $smartLink) }}"
                                                      method="POST" style="display: none;">
                                                    @csrf
                                                    @method('DELETE')
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-center">
                            {{ $smartLinks->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có Link Smart nào</h5>
                            <p class="text-muted">Tạo Link Smart đầu tiên để bắt đầu quản lý lịch trình webinar của bạn.</p>
                            <a href="{{ route('smart-links.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Tạo Link Smart
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
    // Initialize clipboard
    var clipboard = new ClipboardJS('.copy-btn');

    clipboard.on('success', function(e) {
        // Show success message
        const button = e.trigger;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';

        setTimeout(() => {
            button.innerHTML = originalIcon;
        }, 2000);

        e.clearSelection();
    });

    function confirmDelete(hashId) {
        if (confirm('Bạn có chắc chắn muốn xóa Link Smart này?')) {
            document.getElementById('delete-form-' + hashId).submit();
        }
    }
</script>
@endpush
@endsection
