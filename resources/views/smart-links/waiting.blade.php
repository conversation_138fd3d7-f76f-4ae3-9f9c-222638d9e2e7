<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webinar sắp bắt đầu - {{ $smartLink->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .waiting-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        .countdown {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 2rem 0;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .webinar-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="waiting-card">
        <div class="pulse">
            <i class="fas fa-clock fa-4x text-primary mb-4"></i>
        </div>

        <h2 class="mb-4">Webinar sắp bắt đầu!</h2>

        <div class="webinar-info">
            <h4 class="text-primary">{{ $nextWebinar->title }}</h4>
            <p class="mb-2"><i class="fas fa-user me-2"></i><strong>Diễn giả:</strong> {{ $nextWebinar->speaker }}</p>
            @php
                $nextSchedule = $smartLink->schedules->filter(function($schedule) use ($nextWebinar) {
                    return $schedule->webinar_id == $nextWebinar->id && $schedule->isUpcoming();
                })->first();
            @endphp
            @if($nextSchedule && $nextSchedule->start_date_time)
                <p class="mb-0"><i class="fas fa-calendar me-2"></i><strong>Thời gian:</strong> {{ $nextSchedule->start_date_time->format('d/m/Y H:i') }}</p>
            @endif
        </div>

        <div class="countdown" id="countdown">
            <i class="fas fa-spinner fa-spin"></i> Đang tính toán...
        </div>

        <p class="text-muted mb-4">
            Webinar sẽ tự động bắt đầu khi đến giờ. Vui lòng giữ trang này mở.
        </p>

        <div class="d-grid gap-2">
            <button class="btn btn-primary btn-lg" onclick="checkNow()">
                <i class="fas fa-refresh me-2"></i>Kiểm tra ngay
            </button>
            <a href="{{ $nextWebinar->join_url }}" class="btn btn-outline-primary" target="_blank">
                <i class="fas fa-external-link-alt me-2"></i>Truy cập trực tiếp
            </a>
        </div>
    </div>

    <script>
        @if($nextSchedule && $nextSchedule->start_date_time)
            const startTime = new Date('{{ $nextSchedule->start_date_time->toISOString() }}');
        @else
            const startTime = new Date();
        @endif
        const smartLinkUrl = '{{ $smartLink->full_url }}';

        function updateCountdown() {
            const now = new Date();
            const diff = startTime - now;

            if (diff <= 0) {
                // Time's up, redirect to webinar
                window.location.href = smartLinkUrl;
                return;
            }

            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);

            let countdownText = '';
            if (days > 0) {
                countdownText = `${days} ngày ${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                countdownText = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            document.getElementById('countdown').innerHTML = countdownText;
        }

        function checkNow() {
            window.location.href = smartLinkUrl;
        }

        // Auto-refresh every 30 seconds to check if webinar has started
        setInterval(() => {
            if (new Date() >= startTime) {
                window.location.href = smartLinkUrl;
            }
        }, 30000);

        // Update countdown every second
        updateCountdown();
        setInterval(updateCountdown, 1000);

        // Auto-redirect when time comes
        const timeUntilStart = startTime - new Date();
        if (timeUntilStart > 0 && timeUntilStart <= 24 * 60 * 60 * 1000) { // Within 24 hours
            setTimeout(() => {
                window.location.href = smartLinkUrl;
            }, timeUntilStart);
        }
    </script>
</body>
</html>
