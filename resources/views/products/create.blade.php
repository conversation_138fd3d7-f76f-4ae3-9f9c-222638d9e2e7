@extends('layouts.app')

@section('content')
<div class="container">
    <h1 class="main-title mb-4"><i class="fas fa-box-open"></i> Thê<PERSON> sản phẩm mới</h1>
    <form action="{{ route('products.store') }}" method="POST" enctype="multipart/form-data" id="product-form">
        @csrf
        <div class="card mb-4">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Tên sản phẩm <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" value="{{ old('name') }}" required>
                        @error('name')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold"><PERSON>i<PERSON> gốc <span class="text-danger">*</span></label>
                        <input type="number" name="original_price" class="form-control @error('original_price') is-invalid @enderror" value="{{ old('original_price') }}" min="0" required>
                        @error('original_price')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">Giá khuyến mãi</label>
                        <input type="number" name="sale_price" class="form-control @error('sale_price') is-invalid @enderror" value="{{ old('sale_price') }}" min="0">
                        @error('sale_price')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Ảnh sản phẩm</label>
                        <input type="file" name="image" class="form-control mb-2 @error('image') is-invalid @enderror">
                        <input type="url" name="image_url" class="form-control" placeholder="Hoặc nhập URL ảnh..." value="{{ old('image_url') }}">
                        @error('image')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">Số lượng trong kho <span class="text-danger">*</span></label>
                        <input type="number" name="stock_quantity" class="form-control @error('stock_quantity') is-invalid @enderror" value="{{ old('stock_quantity', 0) }}" min="0" required>
                        @error('stock_quantity')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold">Tags sản phẩm</label>
                        <select name="tags[]" id="tags" class="form-select" multiple="multiple">
                            @if(old('tags'))
                                @foreach(old('tags') as $tag)
                                    <option value="{{ $tag }}" selected>{{ $tag }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn btn-success mt-3"><i class="fas fa-save me-1"></i> Lưu sản phẩm</button>
                <a href="{{ route('products.index') }}" class="btn btn-secondary mt-3 ms-2">Quay lại</a>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<!-- Select2 & jQuery (nếu chưa có) -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(function() {
    $('#tags').select2({
        tags: true,
        tokenSeparators: [','],
        theme: 'bootstrap-5',
        placeholder: 'Nhập tags...'
    });
});
</script>
@endpush 