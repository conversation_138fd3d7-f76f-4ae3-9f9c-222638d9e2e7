@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-box-open"></i> <PERSON><PERSON> sách sản phẩm
    </h1>
    <a href="{{ route('products.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> Thêm sản phẩm
    </a>
</div>

@if(session('success'))
    <div class="alert alert-success">{{ session('success') }}</div>
@endif
@if(session('error'))
    <div class="alert alert-danger">{{ session('error') }}</div>
@endif

<div class="card">
    <div class="card-body p-0">
        @if($products->count())
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th><PERSON><PERSON><PERSON> sản phẩm</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Ảnh</th>
                            <th>Số l<PERSON>ợng</th>
                            <th>Tags</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($products as $product)
                            <tr>
                                <td>{{ $product->id }}</td>
                                <td>{{ $product->name }}</td>
                                <td>{{ number_format($product->original_price,0,',','.') }}đ</td>
                                <td>{{ $product->sale_price ? number_format($product->sale_price,0,',','.') . 'đ' : '-' }}</td>
                                <td>
                                    @if($product->image)
                                        <img src="{{ filter_var($product->image, FILTER_VALIDATE_URL) ? $product->image : Storage::url($product->image) }}" alt="Ảnh" width="40" height="40" class="rounded">
                                    @endif
                                </td>
                                <td>{{ $product->stock_quantity }}</td>
                                <td>
                                    @if($product->tags)
                                        @foreach($product->tags as $tag)
                                            <span class="badge bg-primary">{{ $tag }}</span>
                                        @endforeach
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{{ route('products.show', $product) }}" class="btn btn-sm btn-info me-1"><i class="fas fa-eye"></i></a>
                                        <a href="{{ route('products.edit', $product) }}" class="btn btn-sm btn-warning me-1"><i class="fas fa-edit"></i></a>
                                        <button type="button" class="btn btn-sm btn-danger delete-btn" 
                                                data-id="{{ $product->id }}" 
                                                data-name="{{ $product->name }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <form id="delete-form-{{ $product->id }}" action="{{ route('products.destroy', $product) }}" method="POST" class="d-none">
                                            @csrf
                                            @method('DELETE')
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="mt-3 ms-2">
                {{ $products->links() }}
            </div>
        @else
            <div class="p-4 text-center text-muted">Chưa có sản phẩm nào.</div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    .delete-form {
        display: inline-block;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-btn');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-id');
                const productName = this.getAttribute('data-name');
                
                Swal.fire({
                    title: 'webinar.test says',
                    text: `Bạn có chắc chắn muốn xoá sản phẩm này?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'OK',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        document.getElementById(`delete-form-${productId}`).submit();
                    }
                });
            });
        });
    });
</script>
@endpush 