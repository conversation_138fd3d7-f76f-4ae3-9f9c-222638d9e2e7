@extends('layouts.app')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
<style>
    .current-time {
        font-size: 0.875rem;
        padding: 3px 8px;
        background-color: rgba(59, 122, 243, 0.1);
        color: #3A7AF3;
        border-radius: 5px;
        font-weight: 600;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .current-time:hover {
        background-color: rgba(59, 122, 243, 0.15);
    }

    .current-time i {
        margin-right: 5px;
        color: #3A7AF3;
    }

    .live-indicator {
        display: inline-flex;
        align-items: center;
        background-color: rgba(255, 0, 0, 0.1);
        color: #ff0000;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 0.7rem;
        font-weight: bold;
        margin-left: 8px;
        animation: pulse 1.5s infinite;
    }

    .live-indicator i {
        font-size: 0.6rem;
        margin-right: 3px;
        animation: blink 1.5s infinite;
    }

    @keyframes blink {
        0% { opacity: 1; }
        50% { opacity: 0.4; }
        100% { opacity: 1; }
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.4); }
        70% { box-shadow: 0 0 0 6px rgba(255, 0, 0, 0); }
        100% { box-shadow: 0 0 0 0 rgba(255, 0, 0, 0); }
    }
</style>
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-chart-pie"></i> TỔNG QUAN
    </h1>
    <div class="date-display">
        <i class="far fa-calendar-alt"></i> {{ \Carbon\Carbon::now()->format('d/m/Y') }}
    </div>
</div>

<!-- Thông báo vượt quá giới hạn dung lượng -->
@if(session('storage_limit_exceeded') || (isset($storageLimitExceeded) && $storageLimitExceeded['exceeded']))
<div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle me-3"></i>
        <div class="flex-grow-1">
            <strong>Cảnh báo: Vượt quá giới hạn dung lượng!</strong>
            <div class="mt-1">
                {{ $storageLimitExceeded['message'] ?? \App\Models\Setting::get('storage_limit_message', 'Hệ thống đã vượt quá giới hạn dung lượng cho phép. Vui lòng liên hệ admin để nâng cấp dung lượng.') }}
            </div>

            @if(isset($storageLimitExceeded))
            <div class="mt-2 d-flex flex-wrap gap-3">
                @if($storageLimitExceeded['video_exceeded'])
                <div class="d-flex align-items-center">
                    <i class="fas fa-play-circle text-white me-2"></i>
                    <span class="fw-bold">Video:</span>
                    <span class="ms-1">{{ $storageLimitExceeded['video_usage_gb'] }}GB / {{ $storageLimitExceeded['video_limit_gb'] }}GB</span>
                    <span class="badge bg-white text-danger ms-2 fw-bold">Vượt quá</span>
                </div>
                @endif

                @if($storageLimitExceeded['system_exceeded'])
                <div class="d-flex align-items-center">
                    <i class="fas fa-server text-white me-2"></i>
                    <span class="fw-bold">Hệ thống:</span>
                    <span class="ms-1">{{ $storageLimitExceeded['system_usage_gb'] }}GB / {{ $storageLimitExceeded['system_limit_gb'] }}GB</span>
                    <span class="badge bg-white text-danger ms-2 fw-bold">Vượt quá</span>
                </div>
                @endif
            </div>
            @endif
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<!-- Thẻ thống kê chính -->
<div class="row g-4">
    <div class="col-lg-3 col-md-6">
        <div class="stats-card bg-gradient-primary">
            <div class="stats-content">
                <div class="stats-icon">
                    <i class="fas fa-video"></i>
                </div>
                <div class="stats-info">
                    <h6 class="card-title">TỔNG SỐ WEBINAR</h6>
                    <h3 class="stats-value">{{ $totalWebinars }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="stats-card bg-gradient-success">
            <div class="stats-content">
                <div class="stats-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stats-info">
                    <h6 class="card-title">BUỔI SẮP TỚI</h6>
                    <h3 class="stats-value">{{ $upcomingWebinars->count() }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="stats-card bg-gradient-info">
            <div class="stats-content">
                <div class="stats-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="stats-info">
                    <h6 class="card-title">Dung lượng hệ thống</h6>
                    <h3 class="stats-value">@filesize($storageStats['website_storage'])</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="stats-card bg-gradient-warning">
            <div class="stats-content">
                <div class="stats-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="stats-info">
                    <h6 class="card-title">DUNG LƯỢNG VIDEO</h6>
                    <h3 class="stats-value">@filesize($storageStats['total_video_storage'])</h3>
                    <small class="text-muted">{{ $storageStats['video_count'] }} video</small>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Module Widget -->
<div class="row mt-4">
    <div class="col-12">
        <x-module-widget />
    </div>
</div>

<!-- Webinar gần đây -->
<div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-history text-primary me-2"></i>
                    <h5 class="m-0">Webinar đang phát <span class="live-indicator"><i class="fas fa-circle"></i> LIVE</span></h5>
                </div>
                <a href="{{ route('webinars.index') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-list-ul me-1"></i> Xem Tất Cả
                </a>
            </div>
            <div class="card-body">
                @if($recentWebinars->isEmpty())
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> Bạn chưa có webinar nào.
                        @if($canCreateMore)
                            <a href="{{ route('webinars.create') }}" class="alert-link">Tạo webinar đầu tiên của bạn</a>.
                        @endif
                    </div>
                @else
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tiêu Đề</th>
                                    @if($isAdmin)
                                        <th>Chủ Sở Hữu</th>
                                    @endif
                                    <th>Người Thuyết Trình</th>
                                    <th>Thời Gian Bắt Đầu</th>
                                    <th>Thao Tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentWebinars as $webinar)
                                    @php
                                        $now = \Carbon\Carbon::now();
                                        $isCurrentlyLive = false;

                                        if($webinar->schedules && count($webinar->schedules) > 0) {
                                            foreach($webinar->schedules as $schedule) {
                                                $scheduledTime = \Carbon\Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                                                $endTime = $scheduledTime->copy()->addMinutes($webinar->duration ?? 60);

                                                // Check if current time is between start and end time
                                                if ($now->between($scheduledTime, $endTime)) {
                                                    $isCurrentlyLive = true;
                                                    break;
                                                }
                                            }
                                        }
                                    @endphp

                                    @if($isCurrentlyLive)
                                    <tr>
                                        <td>{{ $webinar->title }}</td>
                                        @if($isAdmin)
                                            <td>{{ $webinar->user->name }}</td>
                                        @endif
                                        <td>{{ $webinar->speaker }}</td>
                                        <td>
                                            @php
                                                $scheduledTime = \Carbon\Carbon::parse($webinar->schedules[0]['date'] . ' ' . $webinar->schedules[0]['time']);
                                            @endphp
                                            {{$scheduledTime->format('H:i d/m/Y')}}
                                        </td>
                                        <td>
                                            <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-4">
        @php
            $now = \Carbon\Carbon::now();
            $nearestSchedule = null;
            $nearestWebinar = null;
            foreach($allWebinars as $webinar) {
                if($webinar->schedules && count($webinar->schedules) > 0) {
                    foreach($webinar->schedules as $schedule) {
                        $scheduledTime = \Carbon\Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                        if ($scheduledTime->isFuture()) {
                            if (!$nearestSchedule || $scheduledTime->lessThan(\Carbon\Carbon::parse($nearestSchedule['date'] . ' ' . $nearestSchedule['time']))) {
                                $nearestSchedule = $schedule;
                                $nearestWebinar = $webinar;
                            }
                        }
                    }
                }
            }
            $nearestDateTime = $nearestSchedule ? \Carbon\Carbon::parse($nearestSchedule['date'] . ' ' . $nearestSchedule['time']) : null;
        @endphp
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-calendar-day text-primary me-2"></i>
                    <h5 class="m-0">Buổi Sắp Tới</h5>
                </div>
                <div class="current-time">
                    <i class="far fa-clock me-1"></i>
                    @if($nearestDateTime)
                        <span class="badge bg-light text-primary fw-bold">
                            {{ $nearestDateTime->format('H:i d/m/Y') }}
                        </span>
                    @else
                        <span id="current-time">--:--</span>
                    @endif
                </div>
            </div>
            <div class="card-body">
                @if($nearestSchedule && $nearestWebinar)
                    <div class="d-flex align-items-center mb-2">
                        <i class="far fa-clock me-2"></i>
                        <span class="fw-bold">{{ \Carbon\Carbon::parse($nearestSchedule['date'] . ' ' . $nearestSchedule['time'])->format('H:i d/m/Y') }}</span>
                    </div>
                    <div class="mb-1">
                        <strong>{{ $nearestWebinar->title }}</strong> - {{ $nearestWebinar->speaker }}
                    </div>
                    <a href="{{ route('webinars.show', $nearestWebinar) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i> Chi tiết
                    </a>
                @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> Không có buổi webinar sắp tới.
                    </div>
                @endif
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex align-items-center">
                <i class="fas fa-bolt text-primary me-2"></i>
                <h5 class="m-0">Thao Tác Nhanh</h5>
                        </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($canCreateMore)
                        <a href="{{ route('webinars.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Tạo Webinar Mới
                        </a>
                    @endif
                    <a href="{{ route('webinars.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i> Quản Lý Webinar
                    </a>
                    @if($isAdmin)
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-dark">
                            <i class="fas fa-users me-2"></i> Quản Lý Người Dùng
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Real-time clock update
        function updateClock() {
            const now = new Date();

            // Format time: HH:MM:SS
            let hours = now.getHours().toString().padStart(2, '0');
            let minutes = now.getMinutes().toString().padStart(2, '0');
            let seconds = now.getSeconds().toString().padStart(2, '0');

            // Format date: DD/MM/YYYY
            let day = now.getDate().toString().padStart(2, '0');
            let month = (now.getMonth() + 1).toString().padStart(2, '0');
            let year = now.getFullYear();

            // Update the time display
            document.getElementById('current-time').textContent = `${hours}:${minutes}:${seconds} ${day}/${month}/${year}`;

            // Update every second
            setTimeout(updateClock, 1000);
        }

        // Start the clock
        // updateClock();

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Cấu hình biểu đồ đầu tư và lợi nhuận
        const ctx = document.getElementById('investmentChart').getContext('2d');

        const investmentChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Dự Án Webinar', 'Dự án elearning'],
                datasets: [
                    {
                        label: 'Đầu tư (VND)',
                        data: [8112000, 40587971],
                        backgroundColor: 'rgba(65, 118, 249, 0.7)',
                        borderColor: 'rgba(65, 118, 249, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Lợi nhuận (VND)',
                        data: [-8112000, -25987971],
                        backgroundColor: 'rgba(250, 89, 89, 0.7)',
                        borderColor: 'rgba(250, 89, 89, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let value = context.raw;
                                return context.dataset.label + ': ' + value.toLocaleString() + ' VND';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
