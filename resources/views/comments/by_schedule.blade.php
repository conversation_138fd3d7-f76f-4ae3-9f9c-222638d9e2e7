@extends('layouts.app')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="main-title m-0">
            <i class="fas fa-comments"></i> <PERSON><PERSON><PERSON> luận
        </h1>
        <div class="d-flex gap-2">
            <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0 d-flex align-items-center">
                <i class="fas fa-calendar-alt me-2"></i> Danh sách comment của {{ $webinar->title }}
            </h5>
        </div>
        <div class="card-body">
            @if($comments->isEmpty())
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> Webinar này chưa có bình luận nào.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                        <tr>
                            <th width="5%">STT</th>
                            <th width="20%">Ngày</th>
                            <th width="30%">Thao tác</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($comments as $index => $comment)
                            <tr>
                                <td>{{ (int)$index + 1 }}</td>
                                <td>{{ \Carbon\Carbon::parse($comment->date)->format("d/m/Y H:i")  }}</td>
                                <td class="d-flex gap-2">
                                    <form action="{{ route('comments.by_schedule.destroy', [$webinar, $comment]) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <input type="hidden" name="id" value="{{ $comment->id }}">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa bình luận này không?')">
                                            <i class="fas fa-trash me-1"></i>
                                            <span class="button-text">Xóa</span>
                                        </button>
                                    </form>
                                    <button type="button" class="btn btn-sm btn-outline-primary toggle-comments"
                                            data-id="{{$comment->id}}"
                                    >
                                        <i class="fas fa-eye me-1"></i>
                                        <span class="button-text">
                                             Xem bình luận
                                            </span>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>

    <!-- Comments Table Section (Initially Hidden) -->
    <div id="comments-container" class="d-none">
    </div>

@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <style>
        .comment-bot {
            background-color: rgba(108, 117, 125, 0.1);
        }

        .comment-human {
            background-color: rgba(13, 110, 253, 0.05);
        }
    </style>
@endpush

@push('scripts')
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(function () {
            // Handle toggle comments button
            $('.toggle-comments').on('click', function () {
                var button = $(this);
                var id = button.data('id');

                // Reset other buttons
                $('.toggle-comments').not(button).each(function () {
                    var otherButton = $(this);
                    otherButton.data('action', 'show');
                    otherButton.find('.button-text').text('Xem bình luận');
                    otherButton.closest('tr').removeClass('table-primary');
                    otherButton.addClass('btn-outline-primary').removeClass('btn-success');
                });

                // Update this button
                button.data('action', 'hide');
                button.find('.button-text').text('Đang xem bình luận');
                button.addClass('btn-success').removeClass('btn-outline-primary');
                button.closest('tr').addClass('table-primary');

                loadCommentsForDate(id, button);
            });

            // Function to load comments for a date
            function loadCommentsForDate(id, button) {
                $.ajax({
                    url: "{{ route('comments.by_schedule', $webinar) }}",
                    data: {
                        id: id,
                    },
                    dataType: 'html',
                    success: function (response) {

                        // Extract only the comments section from the response
                        var commentsSection = $(response).find('#comments-container').html();

                        // Update the comments container
                        $('#comments-container').html(commentsSection);
                        $('#comments-container').removeClass('d-none');

                        // Scroll to comments
                        $('html, body').animate({
                            scrollTop: $('#comments-container').offset().top - 20
                        }, 500);
                        $("#comments-container").html(response);
                    },
                    error: function () {
                        alert('Đã xảy ra lỗi khi tải bình luận');
                    }
                });
            }

            // If a date is already selected on page load, scroll to the comments
            if ($('#comments-container').is(':visible')) {
                $('html, body').animate({
                    scrollTop: $('#comments-container').offset().top - 20
                }, 500);
            }

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
        });
    </script>
@endpush
