@if($comments->isNotEmpty())
    <div class="card" id="comments-list">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-comments me-2"></i> <PERSON><PERSON><PERSON> luận ngày <span
                    id="comments-date">{{ \Carbon\Carbon::parse($selectedDate)->setTimezone('Asia/Ho_Chi_Minh')->format('d/m/Y') }}</span>
                <span class="badge bg-light text-dark ms-2"><span
                        id="comments-count">{{ $comments->count() }}</span> bình luận</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="comments-table">
                    <thead>
                    <tr>
                        <th width="5%">STT</th>
                        <th width="10%">Th<PERSON><PERSON> gian</th>
                        <th width="15%">Người bình luận</th>
                        <th width="10%">Loại</th>
                        <th width="50%">Nội dung</th>
                        <th width="10%">Thời điểm video</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($comments as $index => $comment)
                        @php
                            $isBot = empty($comment["participant_id"]) || $comment["participant_id"] === null;
                            $rowClass = $isBot ? 'comment-bot' : 'comment-human';
                        @endphp
                        <tr class="{{ $rowClass }}">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                @php
                                    // Format time correctly, ensuring we only display H:i:s for UTC+7
                                    try {
                                        // Ensure we're using Asia/Ho_Chi_Minh timezone (UTC+7)
                                        $timeInVietnam = \Carbon\Carbon::parse($comment["created_at"])->setTimezone('Asia/Ho_Chi_Minh');

                                        // Get hours, minutes, seconds and normalize if needed
                                        $hours = (int)$timeInVietnam->format('H');
                                        $minutes = (int)$timeInVietnam->format('i');
                                        $seconds = (int)$timeInVietnam->format('s');

                                        // Ensure hours are within 0-23 range
                                        $hours = $hours % 24;

                                        // Format the time as H:i:s
                                        $formattedTime = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
                                        echo $formattedTime;
                                    } catch (Exception $e) {
                                        echo '--:--:--';
                                    }
                                @endphp
                            </td>
                            <td>{{ $comment["name"] }}</td>
                            <td>
                                @if($isBot)
                                    <span class="badge bg-secondary" data-bs-toggle="tooltip"
                                          title="Comment được tạo sẵn">
                                                <i class="fas fa-robot me-1"></i> Bot
                                            </span>
                                @else
                                    <span class="badge bg-primary" data-bs-toggle="tooltip"
                                          title="Comment từ người dùng thực">
                                                <i class="fas fa-user me-1"></i> Người dùng
                                            </span>
                                @endif
                            </td>
                            <td>{{ $comment["content"] }}</td>
                            <td>
                                @if(isset($comment["video_timestamp"]) && $comment["video_timestamp"])
                                    @php
                                        // Convert video timestamp to proper format (mm:ss or hh:mm:ss)
                                        $timestamp = $comment["video_timestamp"];

                                        // If it's a number (seconds), convert to mm:ss
                                        if (is_numeric($timestamp)) {
                                            $seconds = (int)$timestamp;
                                            $minutes = floor($seconds / 60);
                                            $remainingSeconds = $seconds % 60;

                                            // Format as mm:ss
                                            $formattedTimestamp = sprintf('%02d:%02d', $minutes, $remainingSeconds);

                                            // If more than 60 minutes, format as hh:mm:ss
                                            if ($minutes >= 60) {
                                                $hours = floor($minutes / 60);
                                                $remainingMinutes = $minutes % 60;
                                                $formattedTimestamp = sprintf('%02d:%02d:%02d', $hours, $remainingMinutes, $remainingSeconds);
                                            }
                                        } else {
                                            $formattedTimestamp = $timestamp;
                                        }
                                    @endphp
                                    <span class="badge bg-info">{{ $formattedTimestamp }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@elseif($selectedDate)
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Không có bình luận nào cho ngày <span
            id="no-comments-date">{{ \Carbon\Carbon::parse($selectedDate)->setTimezone('Asia/Ho_Chi_Minh')->format('d/m/Y') }}</span>.
    </div>
@endif
