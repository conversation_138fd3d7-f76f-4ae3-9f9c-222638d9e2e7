@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-comments"></i> DANH SÁCH BÌNH LUẬN
    </h1>
    <div class="d-flex gap-2">
        <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Quay lại
        </a>
        <div class="dropdown">
            <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-export me-1"></i> Xuất dữ liệu
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                <li>
                    <a class="dropdown-item" href="{{ route('comments.export', ['webinar' => $webinar->getRouteKey(), 'format' => 'xlsx']) }}">
                        <i class="fas fa-file-excel me-2 text-success"></i> Excel (.xlsx)
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{{ route('comments.export', ['webinar' => $webinar->getRouteKey(), 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2 text-primary"></i> CSV (.csv)
                    </a>
                </li>
            </ul>
        </div>
        <a href="{{ route('comments.by_schedule', $webinar) }}" class="btn btn-info">
            <i class="fas fa-calendar-check me-1"></i> Xem theo lịch
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            {{ $webinar->title }} <span class="badge bg-primary">{{ $webinar->comments->count() }} bình luận</span>
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="comments-table" style="width:100%">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Người bình luận</th>
                        <th>Nội dung</th>
                        <th>Thời điểm video</th>
                        <th>Ngày bình luận</th>
                        <th>Giờ bình luận</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(function() {
        var table = $('#comments-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: "{{ route('comments.index', $webinar->getRouteKey()) }}",
            columns: [
                { data: 'id', name: 'id' },
                { data: 'name', name: 'name' },
                { data: 'content', name: 'content' },
                {
                    data: 'video_timestamp',
                    name: 'video_timestamp',
                    render: function(data) {
                        return data === '-' ? data : `<span class="badge bg-info">${data}</span>`;
                    }
                },
                { data: 'comment_date', name: 'created_at' },
                { data: 'comment_time', name: 'created_at' },
                {
                    data: 'id',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    render: function(data) {
                        return `
                            <button class="btn btn-sm btn-danger"
                                    onclick="deleteComment(${data})"
                                    data-id="${data}">
                                <i class="fas fa-trash"></i>
                            </button>
                        `;
                    }
                }
            ],
            order: [[4, 'desc'], [5, 'desc']], // Sắp xếp theo ngày và giờ bình luận mới nhất
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json'
            }
        });

        // Xử lý xóa bình luận
        window.deleteComment = function(id) {
            if (confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
                $.ajax({
                    url: `{{ url('webinars/' . $webinar->getRouteKey() . '/comments') }}/${id}`,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function() {
                        table.ajax.reload();

                        // Hiển thị thông báo thành công
                        Swal.fire({
                            title: 'Thành công!',
                            text: 'Đã xóa bình luận',
                            icon: 'success',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    },
                    error: function(err) {
                        console.error(err);
                        // Hiển thị thông báo lỗi
                        Swal.fire({
                            title: 'Lỗi!',
                            text: 'Không thể xóa bình luận',
                            icon: 'error'
                        });
                    }
                });
            }
        }
    });
</script>
@endpush
