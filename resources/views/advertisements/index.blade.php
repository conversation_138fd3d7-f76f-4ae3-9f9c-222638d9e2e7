@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-bullhorn"></i> Danh sách quảng cáo
    </h1>
    <a href="{{ route('advertisements.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> Thêm quảng cáo
    </a>
</div>

@if(session('success'))
    <div class="alert alert-success">{{ session('success') }}</div>
@endif
@if(session('error'))
    <div class="alert alert-danger">{{ session('error') }}</div>
@endif

<div class="card">
    <div class="card-body p-0">
        @if($advertisements->count())
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Tên</th>
                            <th>Loại</th>
                            <th>Ảnh</th>
                            <th>Sản phẩm</th>
                            <th>Gi<PERSON> gốc</th>
                            <th>Giá khuyến mãi</th>
                            <th>Số lượng ảo</th>
                            <th>Phương thức</th>
                            <th>Bắt buộc</th>
                            <th>Thời gian hết hạn</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($advertisements as $ad)
                            <tr>
                                <td>{{ $ad->id }}</td>
                                <td>{{ $ad->name ?? 'Chưa đặt tên' }}</td>
                                <td>
                                    @if($ad->type === 'image')
                                        <span class="badge bg-info">Hình ảnh</span>
                                    @else
                                        <span class="badge bg-primary">Sản phẩm</span>
                                    @endif
                                </td>
                                <td>
                                    @if($ad->image)
                                        <img src="{{ filter_var($ad->image, FILTER_VALIDATE_URL) ? $ad->image : Storage::url($ad->image) }}" alt="Ảnh" width="40" height="40" class="rounded">
                                    @endif
                                </td>
                                <td>
                                    @if($ad->product)
                                        {{ $ad->product->name }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>{{ number_format($ad->original_price) }}đ</td>
                                <td>{{ number_format($ad->sale_price) }}đ</td>
                                <td>{{ $ad->virtual_quantity ?? '-' }}</td>
                                <td>
                                    @if($ad->order_method === 'form')
                                        <span class="badge bg-success">Form</span>
                                    @elseif($ad->order_method === 'url')
                                        <span class="badge bg-secondary">Url</span>
                                    @else
                                        <span class="badge bg-warning text-dark">QR</span>
                                    @endif
                                </td>
                                <td>
                                    @if($ad->order_method === 'form')
                                        @if($ad->require_name)<span class="badge bg-primary">Tên</span> @endif
                                        @if($ad->require_email)<span class="badge bg-primary">Email</span> @endif
                                        @if($ad->require_phone)<span class="badge bg-primary">SĐT</span> @endif
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>{{ $ad->display_time }} giây</td>
                                <td>
                                    <a href="{{ route('advertisements.edit', $ad) }}" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i></a>
                                    <form action="{{ route('advertisements.destroy', $ad) }}" method="POST" class="d-inline delete-form">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa quảng cáo này?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="mt-3 ms-2">
                {{ $advertisements->links() }}
            </div>
        @else
            <div class="p-4 text-center text-muted">Chưa có quảng cáo nào.</div>
        @endif
    </div>
</div>
@endsection
