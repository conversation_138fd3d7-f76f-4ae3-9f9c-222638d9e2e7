@extends('layouts.app')

@section('content')
    <div class="container">
        <h1 class="main-title mb-4"><i class="fas fa-bullhorn"></i> Chỉnh sửa quảng cáo</h1>
        <form action="{{ route('advertisements.update', $advertisement) }}" method="POST" enctype="multipart/form-data"
              id="ad-form">
            @csrf
            @method('PUT')
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label fw-bold">Tên quảng cáo <span
                                    class="text-danger">*</span></label>
                            <input type="text" name="name" id="name"
                                   class="form-control @error('name') is-invalid @enderror"
                                   value="{{ old('name', $advertisement->name) }}" required>
                            @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>@enderror
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="type" class="form-label fw-bold">Loại quảng cáo <span
                                    class="text-danger">*</span></label>
                            <select name="type" id="ad-type" class="form-select @error('type') is-invalid @enderror"
                                    required>
                                <option
                                    value="image" {{ (old('type', $advertisement->type) == 'image') ? 'selected' : '' }}>
                                    Hình ảnh
                                </option>
                                <option
                                    value="product" {{ (old('type', $advertisement->type) == 'product') ? 'selected' : '' }}>
                                    Sản phẩm
                                </option>
                            </select>
                            @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>@enderror
                        </div>
                    </div>
                    <div id="ad-image-fields" class="mb-3">
                        <label class="form-label fw-bold">Hình ảnh quảng cáo</label>
                        @if($advertisement->image)
                            <div class="mb-2">
                                <img
                                    src="{{ filter_var($advertisement->image, FILTER_VALIDATE_URL) ? $advertisement->image : Storage::url($advertisement->image) }}"
                                    alt="Hình ảnh hiện tại" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="remove_image" id="remove_image"
                                       value="1">
                                <label class="form-check-label" for="remove_image">Xóa hình ảnh hiện tại</label>
                            </div>
                        @endif
                        <input type="file" name="image" class="form-control mb-2 @error('image') is-invalid @enderror">
                        <input type="url" name="url" class="form-control mb-2" placeholder="Hoặc nhập URL hình ảnh..."
                               value="{{ old('url', $advertisement->url) }}">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>@enderror
                    </div>
                    <div id="ad-product-fields" class="mb-3" style="display:none;">
                        <div class="row">
                            <div class="col-md-8 mb-2 mb-md-0">
                                <label class="form-label fw-bold">Chọn sản phẩm <span
                                        class="text-danger">*</span></label>
                                <select name="product_id" id="product-select"
                                        class="form-select @error('product_id') is-invalid @enderror">
                                    @if($advertisement->product)
                                        <option value="{{ $advertisement->product_id }}"
                                                selected>{{ $advertisement->product->name }}</option>
                                    @endif
                                </select>
                                @error('product_id')
                                <div class="invalid-feedback">{{ $message }}</div>@enderror
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold">Số lượng ảo muốn bán</label>
                                <input type="number" name="virtual_quantity" class="form-control" min="0"
                                       value="{{ old('virtual_quantity', $advertisement->virtual_quantity) }}">
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="original_price" class="form-label fw-bold">Giá gốc <span
                                    class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" name="original_price" id="original_price"
                                       class="form-control @error('original_price') is-invalid @enderror"
                                       value="{{ old('original_price', $advertisement->original_price) }}" required
                                       min="0">
                                <span class="input-group-text">đ</span>
                            </div>
                            @error('original_price')
                            <div class="invalid-feedback">{{ $message }}</div>@enderror
                        </div>
                        <div class="col-md-6">
                            <label for="sale_price" class="form-label fw-bold">Giá khuyến mãi <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" name="sale_price" id="sale_price"
                                       class="form-control @error('sale_price') is-invalid @enderror"
                                       value="{{ old('sale_price', $advertisement->sale_price) }}" required min="0">
                                <span class="input-group-text">đ</span>
                            </div>
                            @error('sale_price')
                            <div class="invalid-feedback">{{ $message }}</div>@enderror
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Phương thức đặt hàng <span
                                    class="text-danger">*</span></label>
                            <select name="order_method" id="order-method"
                                    class="form-select @error('order_method') is-invalid @enderror" required>
                                <option
                                    value="form" {{ (old('order_method', $advertisement->order_method) == 'form') ? 'selected' : '' }}>
                                    Form
                                </option>
                                <option
                                    value="url" {{ (old('order_method',$advertisement->order_method)=='url') ? 'selected' : '' }}>
                                    Url
                                </option>
                                <option
                                    value="qr" {{ (old('order_method', $advertisement->order_method) == 'qr') ? 'selected' : '' }}>
                                    QR thanh toán
                                </option>
                            </select>
                            @error('order_method')
                            <div class="invalid-feedback">{{ $message }}</div>@enderror
                        </div>
                    </div>

                    <div id="order-url-fields" class="mb-3">
                        <label class="form-label fw-bold">Url quảng cáo</label>
                        <input class="form-control" type="text" name="redirect_url" id="redirect_url"
                               value="{{old('redirect_url', $advertisement->redirect_url)}}">

                    </div>
                    <div id="order-form-fields" class="mb-3">
                        <label class="form-label fw-bold">Trường bắt buộc khi đặt hàng</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="require_name" id="require_name"
                                   value="1" {{ (old('require_name', $advertisement->require_name)) ? 'checked' : '' }}>
                            <label class="form-check-label" for="require_name">Tên</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="require_email" id="require_email"
                                   value="1" {{ (old('require_email', $advertisement->require_email)) ? 'checked' : '' }}>
                            <label class="form-check-label" for="require_email">Email</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="require_phone" id="require_phone"
                                   value="1" {{ (old('require_phone', $advertisement->require_phone)) ? 'checked' : '' }}>
                            <label class="form-check-label" for="require_phone">Số điện thoại</label>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="display_time" class="form-label fw-bold">Thời gian hết hạn (giây) <span
                                    class="text-danger">*</span></label>
                            <input type="number" name="display_time" id="display_time"
                                   class="form-control @error('display_time') is-invalid @enderror"
                                   value="{{ old('display_time', $advertisement->display_time) }}" required min="1">
                            @error('display_time')
                            <div class="invalid-feedback">{{ $message }}</div>@enderror
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success mt-3"><i class="fas fa-save me-1"></i> Cập nhật quảng
                        cáo
                    </button>
                    <a href="{{ route('advertisements.index') }}" class="btn btn-secondary mt-3 ms-2">Quay lại</a>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <!-- Select2 & jQuery (nếu chưa có) -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(function () {
            function toggleFields() {
                if ($('#ad-type').val() === 'image') {
                    $('#ad-image-fields').show();
                    $('#ad-product-fields').hide();
                } else {
                    $('#ad-image-fields').hide();
                    $('#ad-product-fields').show();
                }
            }

            $('#ad-type').on('change', toggleFields);
            toggleFields();

            // Select2 cho sản phẩm
            $('#product-select').select2({
                theme: 'bootstrap-5',
                placeholder: 'Tìm kiếm sản phẩm...',
                width: '100%',
                ajax: {
                    url: '{{ route('products.search') }}',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {q: params.term};
                    },
                    processResults: function (data) {
                        return {
                            results: data.results
                        };
                    },
                    cache: true
                },
                minimumInputLength: 1
            });

            function toggleOrderFields() {
                if ($('#order-method').val() === 'form') {
                    $('#order-url-fields').hide();
                    $('#order-form-fields').show();
                } else if ($('#order-method').val() === 'url') {
                    $('#order-form-fields').hide();
                    $('#order-url-fields').show();
                } else {
                    $('#order-form-fields').hide();
                    $('#order-url-fields').hide();
                }
            }

            $('#order-method').on('change', toggleOrderFields);
            toggleOrderFields();
        });
    </script>
@endpush
