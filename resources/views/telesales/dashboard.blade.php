@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Thống kê telesale -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Bảng điều khiển Telesale</h5>

                    @hasanyrole('administrator|admin')
                    <div>
                        <a href="{{ route('orders.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-list-ul me-1"></i> Quản lý Order
                        </a>
                    </div>
                    @endhasanyrole
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="telesale-stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['total_assigned'], 0, ',', '.') }}</h3>
                                    <p class="stat-title">Tổng số lead được gán</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="telesale-stat-card highlight">
                                <div class="stat-icon bg-danger">
                                    <i class="fas fa-phone-alt"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['follow_today'], 0, ',', '.') }}</h3>
                                    <p class="stat-title">Cần gọi hôm nay</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="telesale-stat-card">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['overdue'], 0, ',', '.') }}</h3>
                                    <p class="stat-title">Quá hạn theo dõi</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="telesale-stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ $stats['success_rate'] }}%</h3>
                                    <p class="stat-title">Tỷ lệ thành công</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6 mb-3">
                            <div class="telesale-stat-card">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-phone-volume"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ number_format($stats['contacted_today'], 0, ',', '.') }}</h3>
                                    <p class="stat-title">Cuộc gọi hôm nay</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="telesale-stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="stat-details">
                                    <h3 class="stat-number">{{ $stats['conversion_rate'] }}%</h3>
                                    <p class="stat-title">Tỷ lệ chuyển đổi</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Danh sách công việc cần làm hôm nay -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-day text-danger me-2"></i> Cần theo dõi hôm nay ({{ number_format($stats['follow_today'], 0, ',', '.') }})</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="followup-today-table" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>Tên</th>
                                    <th>Điện thoại</th>
                                    <th>Thời gian</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Danh sách công việc quá hạn -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-exclamation-circle text-warning me-2"></i> Quá hạn theo dõi ({{ number_format($stats['overdue'], 0, ',', '.') }})</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="overdue-followup-table" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>Tên</th>
                                    <th>Điện thoại</th>
                                    <th>Quá hạn</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Prospects giá trị cao -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-star text-warning me-2"></i> Prospects tiềm năng</h5>
                </div>
                <div class="card-body p-0">
                    @if($highValueProspects->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($highValueProspects as $order)
                                <div class="list-group-item p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ $order->name }}</h6>
                                            <div class="d-flex gap-2 mb-2">
                                                <a href="tel:{{ $order->phone }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-phone-alt"></i> {{ $order->phone }}
                                                </a>
                                                <a href="https://zalo.me/{{ $order->phone }}" target="_blank" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-comment"></i> Zalo
                                                </a>
                                            </div>
                                            <div>
                                                <span class="badge bg-success me-1">{{ number_format($order->price, 0, ',', '.') }} đ</span>
                                                @if($order->webinar)
                                                    <span class="badge bg-info"><i class="fas fa-video me-1"></i> {{ $order->webinar->title }}</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-success add-contact-btn" data-id="{{ $order->id }}" data-name="{{ $order->name }}">
                                                <i class="fas fa-plus me-1"></i> Thêm liên hệ
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="p-4 text-center text-muted">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <p>Chưa có prospects tiềm năng nào.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        @hasanyrole('administrator|admin')
        <!-- Công cụ tự động phân công -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-random text-primary me-2"></i> Tự động phân công lead</h5>
                </div>
                <div class="card-body">
                    <form id="auto-assign-form">
                        <div class="mb-3">
                            <label for="webinar_id" class="form-label">Webinar</label>
                            <select class="form-select" id="webinar_id" name="webinar_id">
                                <option value="">-- Tất cả webinar --</option>
                                @foreach($webinars as $webinar)
                                    <option value="{{ $webinar->id }}">{{ $webinar->title }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="limit" class="form-label">Số lượng lead cần lấy</label>
                            <input type="number" class="form-control" id="limit" name="limit" min="1" max="50" value="10">
                        </div>
                        <div class="mb-3">
                            <p class="mb-2">Hệ thống sẽ gán các lead chưa được phân công cho bạn theo thứ tự ưu tiên:</p>
                            <ol class="small text-muted">
                                <li>Lead từ webinar đã chọn (nếu có)</li>
                                <li>Lead đã thanh toán được ưu tiên cao nhất</li>
                                <li>Lead mới và chưa được liên hệ</li>
                            </ol>
                        </div>
                        <div class="d-grid">
                            <button type="button" id="btn-auto-assign" class="btn btn-primary">
                                <i class="fas fa-tasks me-1"></i> Tự động phân công cho tôi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        @endhasanyrole
    </div>
</div>

<!-- Modal thêm liên hệ mới -->
<div class="modal fade" id="add-contact-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm liên hệ mới - <span id="contact-customer-name"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="contact-form">
                    <input type="hidden" id="contact-order-id">
                    <div class="mb-3">
                        <label for="contact-type" class="form-label">Loại liên hệ</label>
                        <select class="form-select" id="contact-type" required>
                            <option value="">-- Chọn loại --</option>
                            <option value="phone">Gọi điện</option>
                            <option value="sms">SMS</option>
                            <option value="email">Email</option>
                            <option value="zalo">Zalo</option>
                            <option value="other">Khác</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="contact-content" class="form-label">Nội dung</label>
                        <textarea class="form-control" id="contact-content" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="contact-status" class="form-label">Kết quả</label>
                        <select class="form-select" id="contact-status" required>
                            <option value="">-- Chọn kết quả --</option>
                            <option value="success">Thành công</option>
                            <option value="no_answer">Không trả lời</option>
                            <option value="callback">Hẹn gọi lại</option>
                            <option value="not_interested">Không quan tâm</option>
                            <option value="wrong_number">Sai số điện thoại</option>
                            <option value="other">Khác</option>
                        </select>
                    </div>
                    <div class="mb-3 callback-time-group d-none">
                        <label for="callback-time" class="form-label">Thời gian gọi lại</label>
                        <input type="datetime-local" class="form-control" id="callback-time">
                    </div>
                    <div class="mb-3">
                        <label for="care-status" class="form-label">Cập nhật trạng thái chăm sóc</label>
                        <select class="form-select" id="care-status">
                            <option value="">-- Giữ nguyên --</option>
                            <option value="new">Mới</option>
                            <option value="contacted">Đã liên hệ</option>
                            <option value="interested">Quan tâm</option>
                            <option value="not_interested">Không quan tâm</option>
                            <option value="callback">Gọi lại sau</option>
                            <option value="converted">Đã chuyển đổi</option>
                            <option value="closed">Đã đóng</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" id="btn-save-contact" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Lưu
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    /* Telesale dashboard styles */
    .telesale-stat-card {
        display: flex;
        align-items: center;
        background: #fff;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border: 1px solid #e9ecef;
        transition: all 0.3s;
        height: 100%;
    }

    .telesale-stat-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-color: #dee2e6;
    }

    .telesale-stat-card.highlight {
        border-left: 4px solid #dc3545;
        background-color: #fff8f8;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        margin-right: 15px;
        color: white;
    }

    .stat-icon i {
        font-size: 1.5rem;
    }

    .stat-details {
        flex: 1;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0;
        line-height: 1.2;
    }

    .stat-title {
        color: #6c757d;
        margin: 0;
        font-size: 0.9rem;
    }

    /* List styles */
    .list-group-item {
        transition: all 0.2s;
    }

    .list-group-item:hover {
        background-color: rgba(0,123,255,0.05);
    }

    /* Badge styles */
    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // DataTables initialization
        const followupTodayTable = $('#followup-today-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: "{{ route('telesales.dashboard') }}",
                data: function(d) {
                    d.section = 'followupToday';
                }
            },
            columns: [
                {
                    data: 'name',
                    name: 'name',
                    render: function(data, type, row) {
                        let html = '<h6 class="mb-0">' + data + '</h6>';
                        if (row.webinar && row.webinar.title) {
                            html += '<small class="text-muted"><i class="fas fa-video me-1"></i>' + row.webinar.title + '</small>';
                        }
                        return html;
                    }
                },
                {
                    data: 'phone',
                    name: 'phone',
                    render: function(data, type, row) {
                        return '<div class="d-flex gap-2">' +
                               '<a href="tel:' + data + '" class="btn btn-sm btn-outline-primary">' +
                               '<i class="fas fa-phone-alt"></i> ' + data + '</a>' +
                               '<a href="https://zalo.me/' + data + '" target="_blank" class="btn btn-sm btn-outline-info">' +
                               '<i class="fas fa-comment"></i> Zalo</a>' +
                               '</div>';
                    }
                },
                {
                    data: 'next_follow_date',
                    name: 'next_follow_date',
                    render: function(data, type, row) {
                        if (!data) return 'N/A';
                        let date = new Date(data);
                        return '<i class="fas fa-clock me-1"></i> ' +
                               date.toLocaleDateString('vi-VN') + ' ' +
                               date.toLocaleTimeString('vi-VN', {hour: '2-digit', minute:'2-digit'});
                    }
                },
                {
                    data: 'actions',
                    name: 'actions',
                    orderable: false,
                    searchable: false
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/vi.json'
            },
            pageLength: 5,
            lengthMenu: [5, 10, 25, 50],
            order: [[2, 'asc']]
        });

        const overdueFollowupTable = $('#overdue-followup-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: "{{ route('telesales.dashboard') }}",
                data: function(d) {
                    d.section = 'overdueFollowup';
                }
            },
            columns: [
                {
                    data: 'name',
                    name: 'name',
                    render: function(data, type, row) {
                        let html = '<h6 class="mb-0">' + data + '</h6>';
                        if (row.webinar && row.webinar.title) {
                            html += '<small class="text-muted"><i class="fas fa-video me-1"></i>' + row.webinar.title + '</small>';
                        }
                        return html;
                    }
                },
                {
                    data: 'phone',
                    name: 'phone',
                    render: function(data, type, row) {
                        return '<div class="d-flex gap-2">' +
                               '<a href="tel:' + data + '" class="btn btn-sm btn-outline-primary">' +
                               '<i class="fas fa-phone-alt"></i> ' + data + '</a>' +
                               '<a href="https://zalo.me/' + data + '" target="_blank" class="btn btn-sm btn-outline-info">' +
                               '<i class="fas fa-comment"></i> Zalo</a>' +
                               '</div>';
                    }
                },
                {
                    data: 'next_follow_date',
                    name: 'next_follow_date',
                    render: function(data, type, row) {
                        if (!data) return 'N/A';
                        let date = new Date(data);
                        let now = new Date();
                        let diffMs = now - date;
                        let diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                        let diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

                        let overdue = '';
                        if (diffDays > 0) {
                            overdue = diffDays + ' ngày';
                        } else {
                            overdue = diffHours + ' giờ';
                        }

                        return '<small class="text-danger"><i class="fas fa-exclamation-circle me-1"></i> Quá hạn ' +
                               overdue + '</small>';
                    }
                },
                {
                    data: 'actions',
                    name: 'actions',
                    orderable: false,
                    searchable: false
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/vi.json'
            },
            pageLength: 5,
            lengthMenu: [5, 10, 25, 50],
            order: [[2, 'asc']]
        });

        // Xử lý hiện/ẩn trường thời gian gọi lại
        $('#contact-status').change(function() {
            if ($(this).val() === 'callback') {
                $('.callback-time-group').removeClass('d-none');
            } else {
                $('.callback-time-group').addClass('d-none');
            }
        });

        // Xử lý nút thêm liên hệ (delegation for dynamically loaded content)
        $(document).on('click', '.add-contact-btn', function() {
            const orderId = $(this).data('id');
            const customerName = $(this).data('name');

            $('#contact-order-id').val(orderId);
            $('#contact-customer-name').text(customerName);
            $('#contact-form')[0].reset();
            $('.callback-time-group').addClass('d-none');

            $('#add-contact-modal').modal('show');
        });

        // Xử lý lưu liên hệ mới
        $('#btn-save-contact').click(function() {
            const orderId = $('#contact-order-id').val();
            const type = $('#contact-type').val();
            const content = $('#contact-content').val();
            const status = $('#contact-status').val();
            const callbackTime = $('#callback-time').val();
            const careStatus = $('#care-status').val();

            if (!type || !content || !status) {
                showAlert('Lỗi', 'Vui lòng điền đầy đủ thông tin', 'error');
                return;
            }

            // Nếu status là callback nhưng không có thời gian
            if (status === 'callback' && !callbackTime) {
                showAlert('Lỗi', 'Vui lòng chọn thời gian gọi lại', 'error');
                return;
            }

            const data = {
                contact_type: type,
                content: content,
                status: status,
                _token: '{{ csrf_token() }}'
            };

            if (status === 'callback') {
                data.callback_time = callbackTime;
            }

            // Gửi request API
            $.ajax({
                url: `/orders/${orderId}/add-care-history`,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Nếu cần cập nhật trạng thái chăm sóc
                    if (careStatus) {
                        $.ajax({
                            url: `/orders/${orderId}/update-care-status`,
                            type: 'POST',
                            data: {
                                care_status: careStatus,
                                _token: '{{ csrf_token() }}'
                            },
                            success: function() {
                                $('#add-contact-modal').modal('hide');
                                showAlert('Thành công', 'Đã lưu thông tin liên hệ và cập nhật trạng thái', 'success');
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1500);
                            }
                        });
                    } else {
                        $('#add-contact-modal').modal('hide');
                        showAlert('Thành công', 'Đã lưu thông tin liên hệ', 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                },
                error: function(error) {
                    showAlert('Lỗi', 'Không thể lưu thông tin liên hệ', 'error');
                    console.error(error);
                }
            });
        });

        // Xử lý nút tự động phân công
        $('#btn-auto-assign').click(function() {
            const button = $(this);
            const originalText = button.html();

            button.html('<i class="fas fa-spinner fa-spin me-1"></i> Đang phân công...');
            button.prop('disabled', true);

            $.ajax({
                url: '{{ route("telesales.auto-assign") }}',
                type: 'POST',
                data: {
                    webinar_id: $('#webinar_id').val(),
                    limit: $('#limit').val(),
                    assigned_to: '{{ auth()->id() }}',
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    showAlert('Thành công', response.message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                },
                error: function(error) {
                    button.html(originalText);
                    button.prop('disabled', false);
                    showAlert('Lỗi', 'Không thể phân công lead', 'error');
                    console.error(error);
                }
            });
        });

        // Hiển thị thông báo
        function showAlert(title, message, type) {
            Swal.fire({
                title: title,
                text: message,
                icon: type,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }
    });
</script>
@endpush
