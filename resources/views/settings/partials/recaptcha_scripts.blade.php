<script>
    document.addEventListener('DOMContentLoaded', function() {
        const testRecaptchaBtn = document.getElementById('testRecaptchaConnection');
        if (testRecaptchaBtn) {
            testRecaptchaBtn.addEventListener('click', function() {
                // Check if reCAPTCHA is enabled
                const recaptchaEnabled = document.getElementById('recaptcha_enabled').checked;
                
                if (!recaptchaEnabled) {
                    Swal.fire({
                        title: 'Chú ý',
                        text: 'reCAPTCHA đang bị tắt. Vui lòng bật lên để sử dụng tính năng này.',
                        icon: 'warning'
                    });
                    return;
                }
                
                // Hiển thị loading
                Swal.fire({
                    title: 'Đang kiểm tra cấu hình...',
                    text: 'Vui lòng đợi trong giây lát',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // <PERSON><PERSON><PERSON> yêu cầu kiểm tra đến server
                fetch('/settings/test-recaptcha', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        test_only: true
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'Thành công',
                            text: 'Cấu hình reCAPTCHA hoạt động tốt!',
                            icon: 'success'
                        });
                    } else {
                        Swal.fire({
                            title: 'Lỗi',
                            text: data.message || 'Cấu hình reCAPTCHA không hợp lệ',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('Lỗi:', error);
                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Đã xảy ra lỗi khi kiểm tra cấu hình reCAPTCHA',
                        icon: 'error'
                    });
                });
            });
        }
    });
</script> 