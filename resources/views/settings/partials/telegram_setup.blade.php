<!-- Telegram Notification Setup -->
<div class="telegram-setup mt-4">
    <h3 class="card-subtitle mb-3"><i class="fab fa-telegram text-primary me-2"></i>Thiế<PERSON> lập thông báo Telegram</h3>

    <div class="form-check form-switch mb-3">
        <input type="checkbox" class="form-check-input" id="telegram_enabled"
               name="telegram_enabled" value="1" {{ isset($telegramSettings['telegram_enabled']) && $telegramSettings['telegram_enabled'] == '1' ? 'checked' : '' }}>
        <label class="form-check-label" for="telegram_enabled">Bật thông báo Telegram</label>
    </div>


    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Thông báo Telegram sẽ được x<PERSON> lý bằng hàng đợi (queue) để tránh gây chậm trễ cho người dùng. Vui lòng đảm bảo đã thiết lập <code>php artisan queue:work</code> trên máy chủ để thông báo được gửi đi.
    </div>

    <!-- Bot Token Configuration -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-key me-2"></i>Cấu hình Bot</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="telegram_token" class="form-label">Bot Token</label>
                <div class="input-group">
                    <input type="password" id="telegram_token" name="telegram_bot_token"
                           class="form-control" value="{{ $telegramSettings['telegram_bot_token'] ?? '' }}">
                    <button type="button" id="connect-bot" class="btn btn-primary">
                        <i class="fas fa-plug me-1"></i> <span class="button-text">Kết nối</span>
                        <span class="spinner-border spinner-border-sm ms-1 d-none" role="status" aria-hidden="true"></span>
                    </button>
                </div>
                <div class="form-text">
                    <a href="https://t.me/BotFather" target="_blank">
                        <i class="fab fa-telegram me-1"></i>Tạo bot mới trên @BotFather
                    </a>
                </div>
            </div>

            <div id="bot-connected" class="{{ isset($telegramSettings['telegram_bot_token']) && $telegramSettings['telegram_bot_token'] ? '' : 'd-none' }}">
                <div class="alert alert-success d-flex align-items-center">
                    <i class="fas fa-check-circle me-2 fs-5"></i>
                    <span>Kết nối thành công với Bot: <span id="bot-name" class="fw-bold">{{ $telegramSettings['telegram_bot_name'] ?? ($telegramSettings['telegram_bot_username'] ?? 'Telegram Bot') }}</span></span>
                </div>

                <div class="mb-0">
                    <a href="{{ 'https://t.me/'.$telegramSettings['telegram_bot_username'] }}"
                       id="bot-chat-link" target="_blank" class="btn btn-sm btn-info">
                        <i class="fab fa-telegram me-1"></i> Chat với Bot
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recipients Setup -->
    <div class="card mb-4" id="chat-id-card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>Người nhận thông báo</h5>
        </div>
        <div class="card-body">
            <div class="d-flex flex-wrap gap-2 mb-3">
                <button type="button" id="check-chatid" class="btn btn-success">
                    <i class="fas fa-sync-alt me-1"></i> <span class="button-text">Tự động lấy Chat ID</span>
                    <span class="spinner-border spinner-border-sm ms-1 d-none" role="status" aria-hidden="true"></span>
                </button>
                <button type="button" id="test-connection" class="btn btn-info">
                    <i class="fas fa-paper-plane me-1"></i> <span class="button-text">Kiểm tra kết nối</span>
                    <span class="spinner-border spinner-border-sm ms-1 d-none" role="status" aria-hidden="true"></span>
                </button>
            </div>

            <div class="d-none">
                <textarea id="telegram_chat_ids" name="telegram_chat_ids" rows="3"
                       class="form-control">{{ $telegramSettings['telegram_chat_ids'] ?? '' }}</textarea>
                <input type="hidden" id="telegram_chat_users_data" name="telegram_chat_users_data"
                       value="{{ $telegramSettings['telegram_chat_users'] ?? '[]' }}">
            </div>

            <div id="chat-users-container">
                <div class="alert alert-info" id="no-chat-users-alert">
                    <i class="fas fa-info-circle me-1"></i> Chưa có người nhận thông báo nào được cấu hình.
                    Vui lòng nhấn nút "Tự động lấy Chat ID" hoặc chat với bot để thêm người nhận.
                </div>

                <div class="table-responsive d-none" id="chat-users-table-container">
                    <table class="table table-bordered table-hover" id="chat-users-table">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 45px" class="text-center">Chọn</th>
                                <th>Người dùng</th>
                                <th style="width: 100px">Chat ID</th>
                                <th style="width: 150px">Thời gian</th>
                                <th style="width: 120px" class="text-center">Hành động</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Chat users will be populated via JavaScript -->
                        </tbody>
                    </table>

                    <div class="d-flex justify-content-end mt-2">
                        <button type="button" id="save-chat-ids" class="btn btn-sm save-btn">
                            <i class="fas fa-save me-1"></i> <span class="button-text">Lưu danh sách người nhận</span>
                            <span class="spinner-border spinner-border-sm ms-1 d-none" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Save all settings button -->
    <div class="d-flex justify-content-end mt-4">
        <button type="button" id="save-all-settings" class="btn save-btn">
            <i class="fas fa-save me-1"></i> <span class="button-text">Lưu cài đặt</span>
            <span class="spinner-border spinner-border-sm ms-1 d-none" role="status" aria-hidden="true"></span>
        </button>
    </div>
</div>
<style>
    #save-settings-btn {
        display: none !important;
    }

    .btn .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Add loading styles */
    .btn.is-loading .button-text {
        opacity: 0.6;
    }

    .btn.is-loading .spinner-border {
        display: inline-block !important;
    }

    /* Loading states for other buttons */
    #check-chatid.is-loading,
    #test-connection.is-loading,
    #connect-bot.is-loading {
        position: relative;
        pointer-events: none;
    }

    /* Orange save button style */
    .save-btn {
        background-color: #fa8128;
        border-color: #fa8128;
        color: white;
        padding: 8px 16px;
        font-size: 0.9rem;
        border-radius: 6px;
        font-weight: 500;
        box-shadow: 0 3px 5px rgba(250, 129, 40, 0.2);
        transition: all 0.25s ease;
    }

    .save-btn:hover:not(:disabled) {
        background-color: #e67420;
        border-color: #e67420;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(250, 129, 40, 0.25);
    }

    .save-btn:disabled {
        background-color: #faa26a;
        border-color: #faa26a;
        opacity: 0.6;
    }

    /* Improve layout for the main action buttons */
    #check-chatid, #test-connection {
        font-weight: 500;
        min-width: 150px;
    }
    #save-all-settings {
        display:none;
    }
</style>
