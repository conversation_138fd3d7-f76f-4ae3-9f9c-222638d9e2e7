<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Cài đặt giới hạn dung lượng:</strong> Thi<PERSON><PERSON> lập giới hạn dung lượng cho video và hệ thống. 
            Khi vượt quá giới hạn, hệ thống sẽ hiển thị thông báo và hạn chế truy cập.
        </div>
    </div>
</div>

@php
    $currentVideoStorage = $storageStats['total_video_storage'] ?? 0;
    $currentSystemStorage = $storageStats['website_storage'] ?? 0;
    
    $videoStorageGB = round($currentVideoStorage / (1024 * 1024 * 1024), 2);
    $systemStorageGB = round($currentSystemStorage / (1024 * 1024 * 1024), 2);
    
    $videoLimit = (float) \App\Models\Setting::get('video_storage_limit_gb', 10);
    $systemLimit = (float) \App\Models\Setting::get('system_storage_limit_gb', 5);
    
    $videoUsagePercent = $videoLimit > 0 ? min(($videoStorageGB / $videoLimit) * 100, 100) : 0;
    $systemUsagePercent = $systemLimit > 0 ? min(($systemStorageGB / $systemLimit) * 100, 100) : 0;
@endphp

<!-- Current Usage Display -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="fas fa-play-circle me-2"></i>Dung lượng Video hiện tại</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-bold">{{ $videoStorageGB }} GB / {{ $videoLimit }} GB</span>
                    <span class="badge {{ $videoUsagePercent > 90 ? 'bg-danger' : ($videoUsagePercent > 70 ? 'bg-warning' : 'bg-success') }}">
                        {{ number_format($videoUsagePercent, 1) }}%
                    </span>
                </div>
                <div class="progress">
                    <div class="progress-bar {{ $videoUsagePercent > 90 ? 'bg-danger' : ($videoUsagePercent > 70 ? 'bg-warning' : 'bg-success') }}" 
                         style="width: {{ $videoUsagePercent }}%"></div>
                </div>
                <small class="text-muted">{{ $storageStats['video_count'] ?? 0 }} video đã upload</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-code me-2"></i>Dung lượng Hệ thống hiện tại</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-bold">{{ $systemStorageGB }} GB / {{ $systemLimit }} GB</span>
                    <span class="badge {{ $systemUsagePercent > 90 ? 'bg-danger' : ($systemUsagePercent > 70 ? 'bg-warning' : 'bg-success') }}">
                        {{ number_format($systemUsagePercent, 1) }}%
                    </span>
                </div>
                <div class="progress">
                    <div class="progress-bar {{ $systemUsagePercent > 90 ? 'bg-danger' : ($systemUsagePercent > 70 ? 'bg-warning' : 'bg-success') }}" 
                         style="width: {{ $systemUsagePercent }}%"></div>
                </div>
                <small class="text-muted">Code + Database</small>
            </div>
        </div>
    </div>
</div>

<!-- Settings Form -->
@foreach($settings as $setting)
    <div class="mb-4">
        <label for="{{ $setting->key }}" class="form-label fw-bold">
            {{ $setting->label }}
        </label>

        @if($setting->type === 'text')
            <div class="input-group">
                <input type="text" name="{{ $setting->key }}" id="{{ $setting->key }}"
                       class="form-control @error($setting->key) is-invalid @enderror"
                       value="{{ old($setting->key, $setting->value) }}"
                       @if(str_contains($setting->key, 'limit_gb')) step="0.1" min="0.1" @endif>
                @if(str_contains($setting->key, 'limit_gb'))
                    <span class="input-group-text">GB</span>
                @endif
            </div>

        @elseif($setting->type === 'textarea')
            <textarea name="{{ $setting->key }}" id="{{ $setting->key }}"
                      class="form-control @error($setting->key) is-invalid @enderror"
                      rows="3">{{ old($setting->key, $setting->value) }}</textarea>

        @elseif($setting->type === 'boolean')
            <div class="form-check form-switch">
                <input type="checkbox" name="{{ $setting->key }}" id="{{ $setting->key }}"
                       class="form-check-input @error($setting->key) is-invalid @enderror"
                       value="1" {{ old($setting->key, $setting->value) ? 'checked' : '' }}>
            </div>
        @endif

        @error($setting->key)
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror

        @if($setting->description)
            <div class="form-text text-muted">{{ $setting->description }}</div>
        @endif
    </div>
@endforeach

@if($videoUsagePercent > 90 || $systemUsagePercent > 90)
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Cảnh báo:</strong> Dung lượng đã sử dụng vượt quá 90%. Hệ thống có thể bị hạn chế truy cập.
</div>
@endif
