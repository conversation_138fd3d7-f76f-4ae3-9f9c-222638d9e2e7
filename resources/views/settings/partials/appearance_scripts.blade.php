<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Appearance scripts loaded');
        
        // Find the appearance form by ID
        const appearanceForm = document.getElementById('appearance-form');
        console.log('Appearance form found by ID:', appearanceForm !== null);
        
        if (appearanceForm) {
            // Add form submission handler with validation
            appearanceForm.addEventListener('submit', function(e) {
                console.log('Appearance form submit triggered');
                
                const submitButton = document.getElementById('appearance-save-btn');
                if (submitButton) {
                    console.log('Setting button loading state');
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Đang lưu...';
                    submitButton.disabled = true;
                }
            });
        }
        
        // Handle file input triggers
        const adminLogoBtn = document.getElementById('admin-logo-btn');
        const faviconBtn = document.getElementById('favicon-btn');
        
        if (adminLogoBtn) {
            adminLogoBtn.addEventListener('click', function() {
                document.getElementById('admin_logo').click();
            });
        }
        
        if (faviconBtn) {
            faviconBtn.addEventListener('click', function() {
                document.getElementById('favicon').click();
            });
        }
        
        // Image preview functionality
        const imageInputs = document.querySelectorAll('input[type="file"]');
        imageInputs.forEach(input => {
            input.addEventListener('change', function() {
                console.log(`File input changed: ${this.id}`, this.files);
                
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    const preview = document.createElement('img');
                    preview.className = 'img-thumbnail preview-image mt-2';
                    preview.style.maxHeight = '100px';
                    preview.style.maxWidth = '100%';
                    
                    // Get the preview container
                    let previewContainer;
                    if (this.id === 'admin_logo') {
                        previewContainer = document.querySelector('.logo-preview');
                    } else if (this.id === 'favicon') {
                        previewContainer = document.querySelector('.favicon-preview');
                    }
                    
                    if (previewContainer) {
                        console.log(`Found preview container for ${this.id}`);
                        // Clear the preview container
                        previewContainer.innerHTML = '';
                        
                        // Create preview message
                        const previewMessage = document.createElement('div');
                        previewMessage.className = 'mt-2 text-success';
                        previewMessage.innerHTML = '<small><i class="fas fa-check-circle"></i> File đã được chọn (chưa lưu)</small>';
                        
                        // Add preview elements
                        previewContainer.appendChild(preview);
                        previewContainer.appendChild(previewMessage);
                        
                        // Load the image
                        reader.onload = function(e) {
                            preview.src = e.target.result;
                        }
                        
                        reader.readAsDataURL(this.files[0]);
                        console.log(`Preview created for ${this.files[0].name}`);
                    } else {
                        console.error(`Preview container not found for ${this.id}`);
                    }
                }
            });
        });
        
        // Handle delete buttons with AJAX
        const deleteButtons = document.querySelectorAll('.delete-logo-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('Bạn có chắc chắn muốn xóa file này?')) {
                    const key = this.getAttribute('data-key');
                    const action = this.getAttribute('data-action');
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    
                    console.log('Deleting file', {key, action});
                    
                    let previewContainer;
                    if (key === 'admin_logo') {
                        previewContainer = document.querySelector('.logo-preview');
                    } else if (key === 'favicon') {
                        previewContainer = document.querySelector('.favicon-preview');
                    }
                    
                    if (previewContainer) {
                        // Show loading state
                        previewContainer.innerHTML = '<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> Đang xóa...</div>';
                        
                        // Create form data
                        const formData = new FormData();
                        formData.append('_token', csrfToken);
                        
                        // Send AJAX request to delete the file
                        fetch(action, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': csrfToken
                            },
                            body: formData
                        })
                        .then(response => {
                            console.log('Delete response', response);
                            return response.json();
                        })
                        .then(data => {
                            console.log('Delete result', data);
                            if (data.success) {
                                // Show empty state
                                if (key === 'admin_logo') {
                                    previewContainer.innerHTML = `
                                        <div class="border rounded p-3 text-center text-muted" style="max-width: 120px;">
                                            <i class="fas fa-image fa-2x mb-2"></i>
                                            <p class="small mb-0">Chưa có logo</p>
                                        </div>`;
                                } else {
                                    previewContainer.innerHTML = `
                                        <div class="border rounded p-3 text-center text-muted" style="max-width: 50px;">
                                            <i class="fas fa-image fa-lg mb-1"></i>
                                            <p class="small mb-0" style="font-size: 9px;">Chưa có</p>
                                        </div>`;
                                }
                                
                                // Show success message
                                const successAlert = document.createElement('div');
                                successAlert.className = 'alert alert-success mt-2';
                                successAlert.textContent = 'File đã được xóa thành công';
                                
                                // Find the card containing the settings
                                const card = previewContainer.closest('.card');
                                card.appendChild(successAlert);
                                
                                // Remove the alert after 3 seconds
                                setTimeout(() => {
                                    successAlert.remove();
                                }, 3000);
                            } else {
                                // Show error message
                                previewContainer.innerHTML = `
                                    <div class="alert alert-danger">
                                        Không thể xóa file. Vui lòng thử lại.
                                    </div>`;
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            previewContainer.innerHTML = `
                                <div class="alert alert-danger">
                                    Có lỗi xảy ra. Vui lòng thử lại.
                                </div>`;
                        });
                    }
                }
            });
        });
    });
</script> 