<!-- Telegram JavaScript Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Helper function to show loading state
    function showLoading(button) {
        button.classList.add('is-loading');
        button.disabled = true;
        const spinner = button.querySelector('.spinner-border');
        if (spinner) {
            spinner.classList.remove('d-none');
        }
        return button.innerHTML;
    }
    
    // Helper function to hide loading state
    function hideLoading(button, originalHtml) {
        button.classList.remove('is-loading');
        button.disabled = false;
        const spinner = button.querySelector('.spinner-border');
        if (spinner) {
            spinner.classList.add('d-none');
        }
    }

    // Connect Bot functionality
    const connectBtn = document.getElementById('connect-bot');
    if (connectBtn) {
        connectBtn.addEventListener('click', function () {
            const token = document.getElementById('telegram_token').value.trim();
            if (!token) {
                Swal.fire({
                    title: 'Thiếu thông tin',
                    text: 'Vui lòng nhập <PERSON>!',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Show loading
            connectBtn.classList.add('is-loading');
            connectBtn.disabled = true;
            const originalBtnText = connectBtn.innerHTML;
            connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Đang kết nối...';

            // Call API to verify token
            fetch('/api/settings/telegram/verify-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({token: token})
            })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    connectBtn.classList.remove('is-loading');
                    connectBtn.disabled = false;
                    connectBtn.innerHTML = originalBtnText;

                    if (data.success) {
                        document.getElementById('bot-connected').classList.remove('d-none');
                        document.getElementById('bot-name').textContent = data.botName;
                        document.getElementById('bot-chat-link').href = 'https://t.me/' + data.botUsername;

                        // Show success message
                        Swal.fire({
                            title: 'Kết nối thành công!',
                            text: 'Bot Telegram đã được kết nối thành công.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        // Display error with centered X icon
                        Swal.fire({
                            title: 'Lỗi kết nối',
                            html: `<div class="text-center mb-4">
                                <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                                </div>
                               </div>
                               <div>Đã xảy ra lỗi: ${data.message || 'Không thể kết nối với bot. Vui lòng kiểm tra token và thử lại.'}</div>`,
                            icon: false,
                            confirmButtonText: 'OK'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    connectBtn.classList.remove('is-loading');
                    connectBtn.disabled = false;
                    connectBtn.innerHTML = originalBtnText;

                    // Display error with centered X icon
                    Swal.fire({
                        title: 'Lỗi kết nối',
                        html: `<div class="text-center mb-4">
                            <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                            </div>
                           </div>
                           <div>Đã xảy ra lỗi khi kết nối tới bot. Vui lòng thử lại sau.</div>`,
                        icon: false,
                        confirmButtonText: 'OK'
                    });
                });
        });
    }

    // Auto-fetch Chat ID
    const checkChatIdBtn = document.getElementById('check-chatid');
    if (checkChatIdBtn) {
        checkChatIdBtn.addEventListener('click', function () {
            // Show loading
            checkChatIdBtn.classList.add('is-loading');
            checkChatIdBtn.disabled = true;
            const originalBtnText = checkChatIdBtn.innerHTML;
            checkChatIdBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Đang lấy Chat ID...';

            fetch('/api/settings/telegram/get-chat-ids', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    checkChatIdBtn.classList.remove('is-loading');
                    checkChatIdBtn.disabled = false;
                    checkChatIdBtn.innerHTML = originalBtnText;

                    if (data.success && data.chatUsers && data.chatUsers.length > 0) {
                        // Display user list
                        displayChatUsers(data.chatUsers);

                        Swal.fire({
                            title: 'Thành công!',
                            text: `Đã tìm thấy ${data.chatUsers.length} người dùng.`,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Chưa có Chat ID',
                            text: 'Không tìm thấy người dùng nào. Vui lòng gửi tin nhắn cho bot trước.',
                            icon: 'info',
                            confirmButtonText: 'OK'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    checkChatIdBtn.classList.remove('is-loading');
                    checkChatIdBtn.disabled = false;
                    checkChatIdBtn.innerHTML = originalBtnText;

                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Đã xảy ra lỗi khi lấy Chat ID. Vui lòng thử lại sau.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                });
        });
    }

    // Display chat users in table
    function displayChatUsers(chatUsers) {
        const tableContainer = document.getElementById('chat-users-table-container');
        const noUsersAlert = document.getElementById('no-chat-users-alert');
        const tableBody = document.querySelector('#chat-users-table tbody');

        if (!tableContainer || !noUsersAlert || !tableBody) return;

        // Clear existing rows
        tableBody.innerHTML = '';

        if (!chatUsers || chatUsers.length === 0) {
            tableContainer.classList.add('d-none');
            noUsersAlert.classList.remove('d-none');
            
            // Clear the textarea for chat IDs
            const chatIdsTextarea = document.getElementById('telegram_chat_ids');
            if (chatIdsTextarea) {
                chatIdsTextarea.value = '';
            }
            
            // Update hidden field to store empty user data
            let chatUsersField = document.getElementById('telegram_chat_users_data');
            if (!chatUsersField) {
                chatUsersField = document.createElement('input');
                chatUsersField.type = 'hidden';
                chatUsersField.id = 'telegram_chat_users_data';
                chatUsersField.name = 'telegram_chat_users_data';
                document.querySelector('.telegram-setup').appendChild(chatUsersField);
            }
            chatUsersField.value = JSON.stringify([]);
            
            return;
        }

        // Show table, hide alert
        tableContainer.classList.remove('d-none');
        noUsersAlert.classList.add('d-none');

        // Add rows for each user
        chatUsers.forEach(user => {
            const row = document.createElement('tr');
            row.dataset.chatId = user.id;

            // Checkbox column
            const checkboxCell = document.createElement('td');
            checkboxCell.className = 'text-center align-middle';
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input chat-user-checkbox';
            checkbox.checked = user.selected || false;
            checkbox.dataset.chatId = user.id;
            checkbox.addEventListener('change', updateSelectedChatIds);
            checkboxCell.appendChild(checkbox);

            // User name column
            const nameCell = document.createElement('td');
            nameCell.className = 'align-middle';
            const userName = document.createElement('div');
            userName.textContent = user.name;

            if (user.username) {
                const userNameSmall = document.createElement('div');
                userNameSmall.className = 'text-muted small';
                userNameSmall.textContent = '@' + user.username;
                nameCell.appendChild(userName);
                nameCell.appendChild(userNameSmall);
            } else {
                nameCell.appendChild(userName);
            }

            // Chat ID column
            const idCell = document.createElement('td');
            idCell.className = 'align-middle text-muted small';
            idCell.textContent = user.id;

            // Time column
            const timeCell = document.createElement('td');
            timeCell.className = 'align-middle text-muted small';
            timeCell.textContent = user.time || '-';

            // Action column
            const actionCell = document.createElement('td');
            actionCell.className = 'text-center align-middle';

            // Create action buttons container
            const btnGroup = document.createElement('div');
            btnGroup.className = 'd-flex gap-1 justify-content-center';

            // Test button
            const testBtn = document.createElement('button');
            testBtn.type = 'button';
            testBtn.className = 'btn btn-sm btn-outline-info test-chat-btn';
            testBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            testBtn.title = 'Gửi tin nhắn test';
            testBtn.dataset.chatId = user.id;
            testBtn.addEventListener('click', function () {
                sendTestMessage(user.id, user.name);
            });

            // Delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
            deleteBtn.title = 'Xóa người nhận';
            deleteBtn.dataset.chatId = user.id;
            deleteBtn.addEventListener('click', function () {
                removeUser(user.id);
            });

            // Add buttons to group
            btnGroup.appendChild(testBtn);
            btnGroup.appendChild(deleteBtn);
            actionCell.appendChild(btnGroup);

            // Add cells to row
            row.appendChild(checkboxCell);
            row.appendChild(nameCell);
            row.appendChild(idCell);
            row.appendChild(timeCell);
            row.appendChild(actionCell);

            // Add row to table
            tableBody.appendChild(row);
        });

        // Update selected chat IDs
        updateSelectedChatIds();
    }

    // Remove user from table
    function removeUser(chatId) {
        const row = document.querySelector(`#chat-users-table tr[data-chat-id="${chatId}"]`);
        if (row) {
            row.remove();
            updateSelectedChatIds();

            // Check if table is empty
            const tableBody = document.querySelector('#chat-users-table tbody');
            if (tableBody.children.length === 0) {
                document.getElementById('chat-users-table-container').classList.add('d-none');
                document.getElementById('no-chat-users-alert').classList.remove('d-none');
            }
        }
    }

    // Update selected chat IDs
    function updateSelectedChatIds() {
        const checkboxes = document.querySelectorAll('.chat-user-checkbox:checked');
        const selectedIds = Array.from(checkboxes).map(cb => cb.dataset.chatId);
        
        // Collect detailed user data
        const chatUsers = [];
        checkboxes.forEach(cb => {
            const chatId = cb.dataset.chatId;
            if (!chatId) return; // Skip if no chat ID
            
            const row = document.querySelector(`#chat-users-table tr[data-chat-id="${chatId}"]`);
            if (row) {
                const nameCell = row.querySelector('td:nth-child(2)');
                if (!nameCell) return; // Skip if no name cell
                
                const nameElement = nameCell.querySelector('div:first-child');
                const name = nameElement ? nameElement.textContent : 'Unnamed User';
                
                const usernameElement = nameCell.querySelector('div.text-muted');
                const username = usernameElement ? usernameElement.textContent.replace('@', '') : null;
                
                chatUsers.push({
                    id: chatId,
                    name: name,
                    username: username,
                    selected: true
                });
            }
        });

        // Update hidden textarea
        const chatIdsTextarea = document.getElementById('telegram_chat_ids');
        if (chatIdsTextarea) {
            chatIdsTextarea.value = selectedIds.join('\n');
        }
        
        // Create or update hidden field for user data
        try {
            let chatUsersField = document.getElementById('telegram_chat_users_data');
            if (!chatUsersField) {
                const container = document.querySelector('.telegram-setup');
                if (container) {
                    chatUsersField = document.createElement('input');
                    chatUsersField.type = 'hidden';
                    chatUsersField.id = 'telegram_chat_users_data';
                    chatUsersField.name = 'telegram_chat_users_data';
                    container.appendChild(chatUsersField);
                }
            }
            
            if (chatUsersField) {
                chatUsersField.value = JSON.stringify(chatUsers);
            }
        } catch (e) {
            console.error('Error updating chat users data:', e);
        }

        // Always enable save button, even with no recipients
        const saveBtn = document.getElementById('save-chat-ids');
        if (saveBtn) {
            saveBtn.disabled = false;
        }
    }

    // Save selected chat IDs
    const saveChatIdsBtn = document.getElementById('save-chat-ids');
    if (saveChatIdsBtn) {
        saveChatIdsBtn.addEventListener('click', function () {
            const checkboxes = document.querySelectorAll('.chat-user-checkbox:checked');
            // Make sure we always have an array, even if empty
            const selectedIds = Array.from(checkboxes).map(cb => cb.dataset.chatId);
            
            // Get detailed user data
            const chatUsersField = document.getElementById('telegram_chat_users_data');
            const chatUsers = chatUsersField ? JSON.parse(chatUsersField.value || '[]') : [];

            // Get the notification settings - with null checks
            const telegramEnabledElement = document.getElementById('telegram_enabled');
            const telegramEnabled = telegramEnabledElement ? telegramEnabledElement.checked : false;

            // Show loading
            saveChatIdsBtn.classList.add('is-loading');
            saveChatIdsBtn.disabled = true;
            saveChatIdsBtn.querySelector('.spinner-border').classList.remove('d-none');

            // Always ensure chatIds is at least an empty array, not null
            // First save chat IDs
            fetch('/api/settings/telegram/save-chat-ids', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    chatIds: selectedIds.length > 0 ? selectedIds : [], // Send empty array instead of array with empty string
                    chatUsers: chatUsers
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Then save notification settings
                        return fetch('/api/settings/telegram/save-notification-settings', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                telegram_enabled: telegramEnabled ? '1' : '0',
                                telegram_notify_participants: document.getElementById('telegram_notify_participants') ? 
                                                              document.getElementById('telegram_notify_participants').checked ? '1' : '0' : '0',
                                telegram_notify_comments: document.getElementById('telegram_notify_comments') ? 
                                                          document.getElementById('telegram_notify_comments').checked ? '1' : '0' : '0'
                            })
                        });
                    } else {
                        throw new Error(data.message || 'Không thể lưu danh sách người nhận');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    saveChatIdsBtn.classList.remove('is-loading');
                    saveChatIdsBtn.disabled = false;
                    saveChatIdsBtn.querySelector('.spinner-border').classList.add('d-none');

                    if (data.success) {
                        // If we saved with no users, update the display to show "no users" message
                        const checkboxes = document.querySelectorAll('.chat-user-checkbox:checked');
                        if (checkboxes.length === 0) {
                            const tableContainer = document.getElementById('chat-users-table-container');
                            const noUsersAlert = document.getElementById('no-chat-users-alert');
                            
                            if (tableContainer && noUsersAlert) {
                                tableContainer.classList.add('d-none');
                                noUsersAlert.classList.remove('d-none');
                            }
                        }
                        
                        Swal.fire({
                            title: 'Lưu thành công!',
                            text: 'Cài đặt thông báo Telegram đã được lưu',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        throw new Error(data.message || 'Không thể lưu cài đặt thông báo');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    saveChatIdsBtn.classList.remove('is-loading');
                    saveChatIdsBtn.disabled = false;
                    saveChatIdsBtn.querySelector('.spinner-border').classList.add('d-none');

                    // Display error with centered X icon
                    Swal.fire({
                        title: 'Lỗi lưu dữ liệu',
                        html: `<div class="text-center mb-4">
                            <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                            </div>
                           </div>
                           <div>Đã xảy ra lỗi: ${error.message || 'Không thể lưu cài đặt'}</div>`,
                        icon: false,
                        confirmButtonText: 'OK'
                    });
                });
        });
    }

    // Test connection button
    const testConnectionBtn = document.getElementById('test-connection');
    if (testConnectionBtn) {
        testConnectionBtn.addEventListener('click', function () {
            const token = document.getElementById('telegram_token').value.trim();
            const selectedCheckboxes = document.querySelectorAll('.chat-user-checkbox:checked');
            const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.chatId);

            // Check if token is provided
            if (!token) {
                Swal.fire({
                    title: 'Thiếu thông tin',
                    text: 'Vui lòng nhập Bot Token trước khi kiểm tra kết nối',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return;
            }
            
            // If no recipients selected, show confirmation
            if (selectedIds.length === 0) {
                Swal.fire({
                    title: 'Không có người nhận nào được chọn',
                    text: 'Bạn có muốn tiếp tục kiểm tra kết nối với Bot Token đã nhập?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Tiếp tục',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Continue with test without recipients
                        sendTestRequest(token, []);
                    }
                });
                return;
            }
            
            // If we have token and recipients, proceed with test
            sendTestRequest(token, selectedIds);
        });
        
        // Function to send test request
        function sendTestRequest(token, chatIds) {
            // Show loading
            testConnectionBtn.classList.add('is-loading');
            testConnectionBtn.disabled = true;
            const originalBtnText = testConnectionBtn.innerHTML;
            testConnectionBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Đang gửi tin nhắn...';

            fetch('/api/settings/telegram/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    token: token,
                    chat_ids: chatIds
                })
            })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    testConnectionBtn.classList.remove('is-loading');
                    testConnectionBtn.disabled = false;
                    testConnectionBtn.innerHTML = originalBtnText;

                    if (data.success) {
                        Swal.fire({
                            title: 'Kiểm tra thành công!',
                            text: `Đã gửi tin nhắn thành công đến ${data.successCount || 'các'} người nhận.`,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        // Display error with centered X icon
                        Swal.fire({
                            title: 'Lỗi kết nối',
                            html: `<div class="text-center mb-4">
                                <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                                </div>
                               </div>
                               <div>Đã xảy ra lỗi: ${data.message || 'Không thể gửi tin nhắn đến người nhận.'}</div>`,
                            icon: false,
                            confirmButtonText: 'OK'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    testConnectionBtn.classList.remove('is-loading');
                    testConnectionBtn.disabled = false;
                    testConnectionBtn.innerHTML = originalBtnText;

                    // Display error with centered X icon
                    Swal.fire({
                        title: 'Lỗi kết nối',
                        html: `<div class="text-center mb-4">
                            <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                            </div>
                           </div>
                           <div>Đã xảy ra lỗi khi kiểm tra kết nối. Vui lòng thử lại sau.</div>`,
                        icon: false,
                        confirmButtonText: 'OK'
                    });
                });
        }
    }

    // Send test message to a specific chat ID
    function sendTestMessage(chatId, userName) {
        const token = document.getElementById('telegram_token').value.trim();

        if (!token) {
            Swal.fire({
                title: 'Thiếu thông tin',
                text: 'Vui lòng nhập Bot Token trước khi gửi tin nhắn test',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Show loading
        const testBtn = document.querySelector(`.test-chat-btn[data-chat-id="${chatId}"]`);
        if (!testBtn) return;

        testBtn.classList.add('is-loading');
        testBtn.disabled = true;
        const originalBtnHTML = testBtn.innerHTML;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        fetch('/api/settings/telegram/test-send-to-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                chat_id: chatId
            })
        })
            .then(response => response.json())
            .then(data => {
                // Reset button
                testBtn.classList.remove('is-loading');
                testBtn.disabled = false;
                testBtn.innerHTML = originalBtnHTML;

                if (data.success) {
                    Swal.fire({
                        title: 'Gửi thành công!',
                        text: `Đã gửi tin nhắn test đến ${userName}`,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    // Display error with centered X icon
                    Swal.fire({
                        title: 'Lỗi gửi tin nhắn',
                        html: `<div class="text-center mb-4">
                            <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                            </div>
                           </div>
                           <div>Đã xảy ra lỗi: ${data.message || 'Không thể gửi tin nhắn đến người nhận.'}</div>`,
                        icon: false,
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                testBtn.classList.remove('is-loading');
                testBtn.disabled = false;
                testBtn.innerHTML = originalBtnHTML;

                // Display error with centered X icon
                Swal.fire({
                    title: 'Lỗi gửi tin nhắn',
                    html: `<div class="text-center mb-4">
                        <div class="d-inline-block" style="border-radius: 50%; background-color: #f8d7da; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-times" style="font-size: 40px; color: #dc3545;"></i>
                        </div>
                       </div>
                       <div>Đã xảy ra lỗi khi gửi tin nhắn. Vui lòng thử lại sau.</div>`,
                    icon: false,
                    confirmButtonText: 'OK'
                });
            });
    }

    // Initialize: Load chat users if available
    function initTelegramSettings() {
        try {
            const chatIdsValue = document.getElementById('telegram_chat_ids');
            const chatUsersData = document.getElementById('telegram_chat_users_data');
            
            // Try to load from users data field first (preferred)
            if (chatUsersData && chatUsersData.value && chatUsersData.value.trim() !== '') {
                try {
                    const userData = JSON.parse(chatUsersData.value);
                    if (userData && Array.isArray(userData)) {
                        // User data is in an array format
                        displayChatUsers(userData);
                        return;
                    } else if (userData && typeof userData === 'object') {
                        // User data is in object format with ids as keys
                        const userArray = [];
                        
                        // Handle both object with keys and object with nested structure
                        if (Object.keys(userData).length > 0) {
                            for (const key in userData) {
                                const user = userData[key] || {};
                                if (typeof user === 'object') {
                                    userArray.push({
                                        id: user.id || key,
                                        name: user.name || ('User ' + key),
                                        username: user.username || null,
                                        selected: true,
                                        time: user.time || null
                                    });
                                }
                            }
                            
                            if (userArray.length > 0) {
                                displayChatUsers(userArray);
                                return;
                            }
                        }
                    }
                } catch (e) {
                    console.error('Error parsing chat users data:', e);
                }
            }
            
            // Fallback to loading from chat IDs if no user data was successfully loaded
            if (chatIdsValue && chatIdsValue.value && chatIdsValue.value.trim() !== '') {
                const chatIds = chatIdsValue.value.split('\n').filter(id => id.trim() !== '');
                
                // If we have chat IDs but no user data, create basic user objects
                if (chatIds.length > 0) {
                    const basicUsers = chatIds.map(id => ({
                        id: id.trim(),
                        name: 'User ' + id.trim(),
                        username: null,
                        selected: true,
                        time: null
                    }));
                    
                    displayChatUsers(basicUsers);
                    return;
                }
                
                // Otherwise try to fetch from the server
                fetch('/api/settings/telegram/get-chat-ids', {
                    method: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]') ? 
                            document.querySelector('meta[name="csrf-token"]').getAttribute('content') : '',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.chatUsers && Array.isArray(data.chatUsers) && data.chatUsers.length > 0) {
                        displayChatUsers(data.chatUsers);
                    }
                })
                .catch(error => console.error('Error loading chat users:', error));
            }
        } catch (error) {
            console.error('Error initializing Telegram settings:', error);
        }
    }

    // Initialize telegram setup if we're on the notifications tab
    if (document.getElementById('telegram_token')) {
        initTelegramSettings();

        // Load checkbox states from localStorage if available
        const notifyParticipantsCheckbox = document.getElementById('telegram_notify_participants');
        const notifyCommentsCheckbox = document.getElementById('telegram_notify_comments');
        const telegramEnabledCheckbox = document.getElementById('telegram_enabled');

        if (localStorage.getItem('telegram_notify_participants') && notifyParticipantsCheckbox) {
            notifyParticipantsCheckbox.checked = localStorage.getItem('telegram_notify_participants') === '1';
            if (notifyParticipantsCheckbox.checked) {
                notifyParticipantsCheckbox.setAttribute('checked', '');
            } else {
                notifyParticipantsCheckbox.removeAttribute('checked');
            }
        }

        if (localStorage.getItem('telegram_notify_comments') && notifyCommentsCheckbox) {
            notifyCommentsCheckbox.checked = localStorage.getItem('telegram_notify_comments') === '1';
            if (notifyCommentsCheckbox.checked) {
                notifyCommentsCheckbox.setAttribute('checked', '');
            } else {
                notifyCommentsCheckbox.removeAttribute('checked');
            }
        }

        if (localStorage.getItem('telegram_enabled') && telegramEnabledCheckbox) {
            telegramEnabledCheckbox.checked = localStorage.getItem('telegram_enabled') === '1';
            if (telegramEnabledCheckbox.checked) {
                telegramEnabledCheckbox.setAttribute('checked', '');
            } else {
                telegramEnabledCheckbox.removeAttribute('checked');
            }
        }
    }
    
    // Handle the save all settings button
    const saveAllSettingsBtn = document.getElementById('save-all-settings');
    if (saveAllSettingsBtn) {
        saveAllSettingsBtn.addEventListener('click', function() {
            // Get notification settings with null checks
            const telegramEnabledElement = document.getElementById('telegram_enabled');
            const telegramEnabled = telegramEnabledElement ? telegramEnabledElement.checked : false;
            
            const chatIdsTextarea = document.getElementById('telegram_chat_ids');
            const chatIds = chatIdsTextarea && chatIdsTextarea.value ? 
                            chatIdsTextarea.value.trim().split('\n').filter(id => id.trim()) : [];
            
            if (telegramEnabled && chatIds.length === 0) {
                Swal.fire({
                    title: 'Không có người nhận thông báo',
                    text: 'Bạn đã bật thông báo Telegram nhưng chưa thêm người nhận nào. Bạn có muốn tiếp tục lưu cài đặt?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Tiếp tục lưu',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        submitSettingsForm();
                    }
                });
            } else {
                submitSettingsForm();
            }
            
            function submitSettingsForm() {
                // Show loading
                saveAllSettingsBtn.classList.add('is-loading');
                saveAllSettingsBtn.disabled = true;
                saveAllSettingsBtn.querySelector('.spinner-border').classList.remove('d-none');
                
                // Submit the form
                const form = document.querySelector('form[action*="settings"]');
                if (form) {
                    form.submit();
                } else {
                    // If no form found, restore button and show error
                    saveAllSettingsBtn.classList.remove('is-loading');
                    saveAllSettingsBtn.disabled = false;
                    saveAllSettingsBtn.querySelector('.spinner-border').classList.add('d-none');
                    
                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Không thể tìm thấy form lưu cài đặt',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            }
        });
    }

});
</script>
