<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i> <PERSON><PERSON><PERSON> hình <PERSON>h toán Sepay</h5>
    </div>
    <div class="card-body">
        <div class="mb-4">
            <label for="webhook_url" class="form-label fw-bold">Webhook URL</label>
            <div class="input-group">
                <input type="text" id="webhook_url" class="form-control" value="{{ url('/api/payments/sepay/webhook') }}" readonly>
                <button class="btn btn-outline-secondary" type="button" id="copyWebhookUrl">
                    <i class="fas fa-copy"></i> Sao chép
                </button>
            </div>
            <div class="form-text text-muted">Sao chép webhook URL này và thiết lập trong bảng điều khiển merchant <PERSON><PERSON> để nhận thông báo thanh toán.</div>
        </div>

        <div class="mb-4">
            <label for="sepay_api_key" class="form-label fw-bold">Khóa API</label>
            <div class="input-group">
                <input type="password" name="sepay_api_key" id="sepay_api_key" 
                       class="form-control @error('sepay_api_key') is-invalid @enderror" 
                       value="{{ old('sepay_api_key', setting('sepay_api_key')) }}">
                <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                    <i class="fas fa-eye" id="toggleApiKeyIcon"></i>
                </button>
                <button class="btn btn-outline-secondary" type="button" id="generateApiKey">
                    <i class="fas fa-key"></i> Tạo mới
                </button>
            </div>
            @error('sepay_api_key')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text text-muted">Khóa API bảo mật cho tích hợp Sepay. Nhấp "Tạo mới" để tạo khóa bảo mật mới.</div>
        </div>

        <div class="mb-4">
            <label for="sepay_merchant_name" class="form-label fw-bold">Tên tài khoản</label>
            <input type="text" name="sepay_merchant_name" id="sepay_merchant_name" 
                   class="form-control @error('sepay_merchant_name') is-invalid @enderror" 
                   value="{{ old('sepay_merchant_name', setting('sepay_merchant_name')) }}">
            @error('sepay_merchant_name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-4">
            <label for="sepay_bank_code" class="form-label fw-bold">Ngân hàng</label>
            <select name="sepay_bank_code" id="sepay_bank_code" 
                    class="form-select select2 @error('sepay_bank_code') is-invalid @enderror">
                <option value="">-- Chọn ngân hàng --</option>
                @foreach(getBankData() as $bank)
                <option value="{{ $bank['code'] }}" 
                        {{ old('sepay_bank_code', setting('sepay_bank_code')) == $bank['code'] ? 'selected' : '' }}>
                    {{ $bank['name'] }}
                </option>
                @endforeach
            </select>
            @error('sepay_bank_code')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror

            @if(setting('sepay_bank_code') == '970448')
                <div class="mt-3">
                    {!! getBankData('970448') !!}
                </div>
            @endif
        </div>

        <div class="mb-4">
            <label for="sepay_account_number" class="form-label fw-bold">Số tài khoản</label>
            <input type="text" name="sepay_account_number" id="sepay_account_number" 
                   class="form-control @error('sepay_account_number') is-invalid @enderror" 
                   value="{{ old('sepay_account_number', setting('sepay_account_number')) }}">
            @error('sepay_account_number')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-4">
            <div class="form-check form-switch">
                <input type="checkbox" name="sepay_active" id="sepay_active" 
                       class="form-check-input @error('sepay_active') is-invalid @enderror" 
                       value="1" {{ old('sepay_active', setting('sepay_active')) ? 'checked' : '' }}>
                <label for="sepay_active" class="form-check-label">Kích hoạt thanh toán Sepay</label>
            </div>
            @error('sepay_active')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

@push('styles')
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 38px;
        padding-left: 12px;
        color: #212529;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    .select2-results__option--highlighted {
        background-color: #4176F9 !important;
    }
    .select2-container--default .select2-results__option--highlighted[aria-selected="true"] {
        background-color: #4176F9;
    }
</style>
@endpush

@push('scripts')
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Select2 for bank dropdown
        setTimeout(function() {
            if ($('#sepay_bank_code').length) {
                $('#sepay_bank_code').select2({
                    placeholder: '-- Chọn ngân hàng --',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return 'Không tìm thấy kết quả';
                        },
                        searching: function() {
                            return 'Đang tìm kiếm...';
                        }
                    }
                });
            }
        }, 100);
        
        // Copy webhook URL functionality
        const copyWebhookUrlBtn = document.getElementById('copyWebhookUrl');
        const webhookUrlInput = document.getElementById('webhook_url');
        
        if (copyWebhookUrlBtn && webhookUrlInput) {
            copyWebhookUrlBtn.addEventListener('click', function() {
                webhookUrlInput.select();
                document.execCommand('copy');
                
                // Show success message
                Swal.fire({
                    title: 'Đã sao chép!',
                    text: 'Webhook URL đã được sao chép vào clipboard.',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            });
        }
        
        // Toggle API key visibility
        const toggleApiKeyBtn = document.getElementById('toggleApiKey');
        const toggleApiKeyIcon = document.getElementById('toggleApiKeyIcon');
        const apiKeyInput = document.getElementById('sepay_api_key');
        
        if (toggleApiKeyBtn && apiKeyInput && toggleApiKeyIcon) {
            toggleApiKeyBtn.addEventListener('click', function() {
                if (apiKeyInput.type === 'password') {
                    apiKeyInput.type = 'text';
                    toggleApiKeyIcon.classList.remove('fa-eye');
                    toggleApiKeyIcon.classList.add('fa-eye-slash');
                } else {
                    apiKeyInput.type = 'password';
                    toggleApiKeyIcon.classList.remove('fa-eye-slash');
                    toggleApiKeyIcon.classList.add('fa-eye');
                }
            });
        }
        
        // API Key generation
        const generateApiKeyBtn = document.getElementById('generateApiKey');
        
        if (generateApiKeyBtn && apiKeyInput) {
            generateApiKeyBtn.addEventListener('click', function() {
                // Call the server to generate a secure API key
                fetch('{{ route('settings.regenerate-api-key') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Set the generated key in the input field
                        apiKeyInput.value = data.api_key;
                        
                        // Show confirmation message
                        Swal.fire({
                            title: 'Đã tạo khóa API!',
                            text: 'Một khóa API mới đã được tạo. Đừng quên lưu cài đặt của bạn.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        throw new Error('Không thể tạo khóa API');
                    }
                })
                .catch(error => {
                    console.error('Lỗi:', error);
                    
                    // Generate key locally as fallback
                    const generateKey = () => {
                        // Only use alphanumeric characters (no special chars)
                        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
                        const length = 32;
                        let result = '';
                        
                        // Use crypto API if available for better randomness
                        if (window.crypto && window.crypto.getRandomValues) {
                            const values = new Uint8Array(length);
                            window.crypto.getRandomValues(values);
                            for (let i = 0; i < length; i++) {
                                result += chars[values[i] % chars.length];
                            }
                        } else {
                            // Fallback to Math.random (less secure)
                            for (let i = 0; i < length; i++) {
                                result += chars[Math.floor(Math.random() * chars.length)];
                            }
                        }
                        
                        return result;
                    };
                    
                    // Set the generated key in the input field
                    apiKeyInput.value = generateKey();
                    
                    Swal.fire({
                        title: 'Lưu ý',
                        text: 'Đã tạo khóa API trên trình duyệt do không thể kết nối với máy chủ.',
                        icon: 'warning',
                        timer: 3000
                    });
                });
            });
        }
        
        // Show QR code when OCB is selected
        const bankSelect = document.getElementById('sepay_bank_code');
        if (bankSelect) {
            $('#sepay_bank_code').on('change', function() {
                const qrCodeContainer = document.getElementById('ocb_qr_container');
                if (this.value === '970448') {
                    if (!qrCodeContainer) {
                        const container = document.createElement('div');
                        container.id = 'ocb_qr_container';
                        container.className = 'mt-3';
                        container.innerHTML = `{!! getBankData('970448') !!}`;
                        this.parentNode.appendChild(container);
                    }
                } else {
                    if (qrCodeContainer) {
                        qrCodeContainer.remove();
                    }
                }
            });
        }
    });
</script>
@endpush 