@extends('layouts.app')

@section('content')
    <div class="container">
        <h1 class="main-title mb-4"><i class="fas fa-cogs"></i> <PERSON><PERSON>i đặt hệ thống</h1>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <div class="card">
            <div class="card-body">
                <!-- Nav tabs -->
                @if(auth()->user()->can("settings tab scripts") ||
                                     auth()->user()->can("settings tab email")||
                                    auth()->user()->can("settings tab limits")||
                                     auth()->user()->can("settings tab security")||
                                    auth()->user()->can("settings tab notifications")||
                                    auth()->user()->can("settings tab payment")||
                                    auth()->user()->can("settings tab appearance")||
                                    auth()->user()->can("settings tab general"))
                    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                        @foreach($groups as $group)
                            <li class="nav-item" role="presentation">
                                @if($group === 'general' && auth()->user()->can("settings tab general"))

                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-sliders-h me-2"></i> Chung
                                    </a>
                                @elseif($group === 'appearance' && auth()->user()->can("settings tab appearance"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-paint-brush me-2"></i> Giao diện
                                    </a>
                                @elseif($group === 'security' && auth()->user()->can("settings tab security"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-shield-alt me-2"></i> Bảo mật
                                    </a>
                                @elseif($group === 'scripts' && auth()->user()->can("settings tab scripts"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-code me-2"></i> Scripts
                                    </a>
                                @elseif($group === 'email' && auth()->user()->can("settings tab email"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-envelope me-2"></i> Email
                                    </a>
                                @elseif($group === 'notifications' && auth()->user()->can("settings tab notifications"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-bell me-2"></i> Thông báo
                                    </a>
                                @elseif($group === 'payment' && auth()->user()->can("settings tab payment"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-credit-card me-2"></i> Thanh toán
                                    </a>
                                @elseif($group === 'limits' && auth()->user()->can("settings tab limits"))
                                    <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                       href="{{ route('settings.index', ['group' => $group]) }}">
                                        <i class="fas fa-hdd me-2"></i> Giới hạn
                                    </a>
                                @else
                                    @if(($group === 'scripts' && auth()->user()->can("settings tab scripts")) ||
                                        ($group === 'email' && auth()->user()->can("settings tab email"))||
                                        ($group === 'limits' && auth()->user()->can("settings tab limits"))||
                                        ($group === 'security' && auth()->user()->can("settings tab security"))||
                                        ($group === 'notifications' && auth()->user()->can("settings tab notifications"))||
                                        ($group === 'payment' && auth()->user()->can("settings tab payment"))||
                                        ($group === 'appearance' && auth()->user()->can("settings tab appearance"))||
                                        ($group === 'general' && auth()->user()->can("settings tab general")))
                                        <a class="nav-link {{ $activeGroup === $group ? 'active' : '' }}"
                                           href="{{ route('settings.index', ['group' => $group]) }}">
                                            {{ ucfirst($group) }}
                                        </a>
                                    @endif
                                @endif
                            </li>
                        @endforeach
                    </ul>
                    <!-- Form -->

                    <form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="group" value="{{ $activeGroup }}">

                        @if($activeGroup === 'notifications' && auth()->user()->can("settings tab notifications"))
                            @include('settings.partials.telegram_setup')
                        @elseif($activeGroup === 'payment' && auth()->user()->can("settings tab payment"))
                            @include('settings.partials.payment_setup')
                        @elseif($activeGroup === 'appearance' && auth()->user()->can("settings tab appearance"))
                            @include('settings.partials.appearance_setup')
                        @elseif($activeGroup === 'limits' && auth()->user()->can("settings tab limits"))
                            @include('settings.partials.limits_setup')
                        @elseif($activeGroup === 'security' && auth()->user()->can("settings tab security"))
                            @include('settings.partials.recaptcha_setup')
                            <div class="mb-4">
                                <hr class="my-4">
                            </div>
                            @foreach($settings as $setting)
                                <div class="mb-4">
                                    <label for="{{ $setting->key }}" class="form-label fw-bold">
                                        {{ $setting->label }}
                                    </label>

                                    @if($setting->type === 'text')
                                        <input type="text" name="{{ $setting->key }}" id="{{ $setting->key }}"
                                               class="form-control @error($setting->key) is-invalid @enderror"
                                               value="{{ old($setting->key, $setting->value) }}">

                                    @elseif($setting->type === 'textarea')
                                        <textarea name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                  class="form-control @error($setting->key) is-invalid @enderror"
                                                  rows="3">{{ old($setting->key, $setting->value) }}</textarea>

                                    @elseif($setting->type === 'password')
                                        <input type="password" name="{{ $setting->key }}" id="{{ $setting->key }}"
                                               class="form-control @error($setting->key) is-invalid @enderror"
                                               value="{{ old($setting->key, $setting->value) }}"
                                               autocomplete="new-password">

                                    @elseif($setting->type === 'image')
                                        <div class="row">
                                            <div class="col-md-9">
                                                <input type="file" name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                       class="form-control @error($setting->key) is-invalid @enderror">
                                            </div>
                                            <div class="col-md-3">
                                                @if($setting->value)
                                                    <div class="d-flex align-items-center">
                                                        <img src="{{ Storage::url($setting->value) }}"
                                                             alt="{{ $setting->label }}"
                                                             class="img-thumbnail me-2"
                                                             style="max-height: 40px; max-width: 100px;">

                                                        <form
                                                            action="{{ route('settings.delete-file', ['key' => $setting->key]) }}"
                                                            method="POST" class="d-inline delete-image-form">
                                                            @csrf
                                                            <button type="button"
                                                                    class="btn btn-sm btn-danger delete-image-btn"
                                                                    data-key="{{ $setting->key }}"
                                                                    data-group="{{ $setting->group }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                    @elseif($setting->type === 'code')
                                        <textarea name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                  class="form-control code-editor @error($setting->key) is-invalid @enderror"
                                                  rows="8">{{ old($setting->key, $setting->value) }}</textarea>

                                    @elseif($setting->type === 'boolean')
                                        <div class="form-check form-switch">
                                            <input type="checkbox" name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                   class="form-check-input @error($setting->key) is-invalid @enderror"
                                                   value="1" {{ old($setting->key, $setting->value) ? 'checked' : '' }}>
                                        </div>
                                    @endif

                                    @error($setting->key)
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror

                                    @if($setting->description)
                                        <div class="form-text text-muted">{{ $setting->description }}</div>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            @if(($activeGroup === 'scripts' && auth()->user()->can("settings tab scripts")) ||
                                ($activeGroup === 'email' && auth()->user()->can("settings tab email"))||
                                ($activeGroup === 'limits' && auth()->user()->can("settings tab limits"))||
                                ($activeGroup === 'general' && auth()->user()->can("settings tab general")))
                                @foreach($settings as $setting)
                                    <div class="mb-4">
                                        <label for="{{ $setting->key }}" class="form-label fw-bold">
                                            {{ $setting->label }}
                                        </label>

                                        @if($setting->type === 'text')
                                            <input type="text" name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                   class="form-control @error($setting->key) is-invalid @enderror"
                                                   value="{{ old($setting->key, $setting->value) }}">

                                        @elseif($setting->type === 'textarea')
                                            <textarea name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                      class="form-control @error($setting->key) is-invalid @enderror"
                                                      rows="3">{{ old($setting->key, $setting->value) }}</textarea>

                                        @elseif($setting->type === 'password')
                                            <input type="password" name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                   class="form-control @error($setting->key) is-invalid @enderror"
                                                   value="{{ old($setting->key, $setting->value) }}"
                                                   autocomplete="new-password">

                                        @elseif($setting->type === 'image')
                                            <div class="row">
                                                <div class="col-md-9">
                                                    <input type="file" name="{{ $setting->key }}"
                                                           id="{{ $setting->key }}"
                                                           class="form-control @error($setting->key) is-invalid @enderror">
                                                </div>
                                                <div class="col-md-3">
                                                    @if($setting->value)
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ Storage::url($setting->value) }}"
                                                                 alt="{{ $setting->label }}"
                                                                 class="img-thumbnail me-2"
                                                                 style="max-height: 40px; max-width: 100px;">

                                                            <form
                                                                action="{{ route('settings.delete-file', ['key' => $setting->key]) }}"
                                                                method="POST" class="d-inline delete-image-form">
                                                                @csrf
                                                                <button type="button"
                                                                        class="btn btn-sm btn-danger delete-image-btn"
                                                                        data-key="{{ $setting->key }}"
                                                                        data-group="{{ $setting->group }}">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                        @elseif($setting->type === 'code')
                                            <textarea name="{{ $setting->key }}" id="{{ $setting->key }}"
                                                      class="form-control code-editor @error($setting->key) is-invalid @enderror"
                                                      rows="8">{{ old($setting->key, $setting->value) }}</textarea>

                                        @elseif($setting->type === 'boolean')
                                            <div class="form-check form-switch">
                                                <input type="checkbox" name="{{ $setting->key }}"
                                                       id="{{ $setting->key }}"
                                                       class="form-check-input @error($setting->key) is-invalid @enderror"
                                                       value="1" {{ old($setting->key, $setting->value) ? 'checked' : '' }}>
                                            </div>
                                        @endif

                                        @error($setting->key)
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror

                                        @if($setting->description)
                                            <div class="form-text text-muted">{{ $setting->description }}</div>
                                        @endif
                                    </div>
                                @endforeach
                            @endif
                        @endif
                        @if(($group === 'scripts' && auth()->user()->can("settings tab scripts")) ||
                                                            ($group === 'email' && auth()->user()->can("settings tab email"))||
                                                            ($group === 'limits' && auth()->user()->can("settings tab limits"))||
                                                            ($group === 'security' && auth()->user()->can("settings tab security"))||
                                                            ($group === 'notifications' && auth()->user()->can("settings tab notifications"))||
                                                            ($group === 'payment' && auth()->user()->can("settings tab payment"))||
                                                            ($group === 'appearance' && auth()->user()->can("settings tab appearance"))||
                                                            ($group === 'general' && auth()->user()->can("settings tab general")))
                            @if($activeGroup !== 'appearance')
                                <div class="mt-4">
                                    <button type="submit" id="save-settings-btn" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Lưu cài đặt
                                    </button>
                                </div>
                            @endif
                        @endif
                    </form>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    @if($activeGroup === 'scripts')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const codeEditors = document.querySelectorAll('.code-editor');
                codeEditors.forEach(editor => {
                    CodeMirror.fromTextArea(editor, {
                        lineNumbers: true,
                        mode: 'htmlmixed',
                        theme: 'default',
                        lineWrapping: true,
                        height: 'auto',
                        viewportMargin: Infinity
                    });
                });
            });
        </script>
    @endif

    @if($activeGroup === 'notifications' && auth()->user()->can("settings tab notifications"))
        @include('settings.partials.telegram_scripts')
    @endif

    @if($activeGroup === 'security' && auth()->user()->can("settings tab security"))
        @include('settings.partials.recaptcha_scripts')
    @endif

    @if($activeGroup === 'appearance' && auth()->user()->can("settings tab appearance"))
        @include('settings.partials.appearance_scripts')
    @endif

    <script>
        // Xử lý form lưu cài đặt
        document.addEventListener('DOMContentLoaded', function () {
            // Xử lý nút xóa ảnh
            const deleteButtons = document.querySelectorAll('.delete-image-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function (e) {
                    e.preventDefault();
                    const key = this.getAttribute('data-key');
                    const group = this.getAttribute('data-group');
                    const form = this.closest('form');

                    // Sử dụng SweetAlert2 để confirm
                    Swal.fire({
                        title: 'Xác nhận xóa?',
                        text: 'Bạn có chắc chắn muốn xóa hình ảnh này?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Xóa',
                        cancelButtonText: 'Hủy'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Thực hiện submit form xóa
                            form.submit();

                            // Hiển thị loading
                            Swal.fire({
                                title: 'Đang xóa...',
                                text: 'Vui lòng đợi trong giây lát',
                                allowOutsideClick: false,
                                showConfirmButton: false,
                                willOpen: () => {
                                    Swal.showLoading();
                                }
                            });
                        }
                    });
                });
            });
        });
    </script>
@endpush

@push('styles')
    @if($activeGroup === 'scripts' )
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
        <style>
            .CodeMirror {
                height: auto;
                min-height: 200px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        </style>
    @endif
@endpush
