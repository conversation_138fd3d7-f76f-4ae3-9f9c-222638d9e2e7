@extends('layouts.app')

@section('content')
<div class="reset-page">
    <div class="animated-background"></div>
    <div class="reset-container">
        <div class="reset-box">
            <!-- Logo and Heading -->
            <div class="text-center mb-4">
                <div class="logo-container">
                    <i class="fas fa-key key-icon"></i>
                </div>
                <h1 class="reset-title">Đặt Lại Mật K<PERSON>ẩu</h1>
                <p class="reset-subtitle">Nhập địa chỉ email của bạn để nhận liên kết đặt lại mật khẩu</p>
            </div>

            @if (session('status'))
                <div class="alert alert-success mb-4" role="alert">
                    <i class="fas fa-check-circle mr-2"></i> {{ session('status') }}
                </div>
            @endif
            
            <!-- Reset Form -->
            <form method="POST" action="{{ route('password.email') }}" class="reset-form">
                @csrf

                <!-- Email Input -->
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope input-icon"></i>
                        Địa chỉ Email
                    </label>
                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" 
                           name="email" value="{{ old('email') }}" placeholder="Nhập địa chỉ email của bạn" 
                           required autocomplete="email" autofocus>
                    @error('email')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block btn-reset">
                        <i class="fas fa-paper-plane mr-2"></i> Gửi Liên Kết Đặt Lại
                    </button>
                </div>

                <!-- Back to Login Link -->
                <div class="text-center mt-3">
                    <a class="back-to-login-link" href="{{ route('login') }}">
                        <i class="fas fa-arrow-left mr-1"></i> Quay lại đăng nhập
                    </a>
                </div>
            </form>
        </div>
        
        <!-- Footer -->
        <div class="reset-footer">
            © {{ date('Y') }} Hệ Thống Quản Lý Webinar. Tất cả quyền được bảo lưu.
            <div class="version-info">Phiên bản 1.0</div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .top-navbar {
        background-color: transparent;
        box-shadow: none;
    }
    
    body {
        background: #1a1a2e;
        min-height: 100vh;
        margin: 0;
        overflow-x: hidden;
    }
    
    .reset-page {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        position: relative;
        z-index: 1;
    }
    
    .animated-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460, #e94560);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
        z-index: -1;
    }
    
    @keyframes gradient {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }
    
    .reset-container {
        width: 100%;
        max-width: 500px;
    }
    
    .reset-box {
        background-color: white;
        border-radius: 16px;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
        padding: 40px;
        transition: all 0.3s ease;
    }
    
    .reset-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
    }
    
    .logo-container {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #FF7D1A 0%, #FF9F4D 100%);
        border-radius: 50%;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .key-icon {
        font-size: 36px;
        color: white;
    }
    
    .reset-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
    }
    
    .reset-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
    }
    
    .alert-success {
        background-color: rgba(72, 187, 120, 0.1);
        color: #2f855a;
        border-color: rgba(72, 187, 120, 0.2);
        padding: 15px;
        border-radius: 8px;
    }
    
    .reset-form {
        margin-top: 20px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #444;
    }
    
    .input-icon {
        color: #FF7D1A;
        margin-right: 10px;
    }
    
    .form-control {
        height: 50px;
        padding: 10px 15px;
        border-radius: 8px;
        border: 2px solid #e1e5eb;
        font-size: 16px;
        transition: all 0.3s;
    }
    
    .form-control:focus {
        border-color: #FF7D1A;
        box-shadow: 0 0 0 0.2rem rgba(255, 125, 26, 0.15);
    }
    
    .btn-reset {
        background: #FF7D1A;
        border: none;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
    
    .btn-reset:hover {
        background: #E56800;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 125, 26, 0.3);
    }
    
    .btn-reset i {
        margin-right: 10px;
    }
    
    .back-to-login-link {
        color: #FF7D1A;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .back-to-login-link:hover {
        color: #E56800;
        text-decoration: underline;
    }
    
    .reset-footer {
        text-align: center;
        margin-top: 30px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
    }
    
    .version-info {
        font-size: 12px;
        margin-top: 5px;
        color: rgba(255, 255, 255, 0.6);
    }
    
    @media (max-width: 576px) {
        .reset-box {
            padding: 30px 20px;
        }
        
        .logo-container {
            width: 70px;
            height: 70px;
        }
        
        .key-icon {
            font-size: 30px;
        }
        
        .reset-title {
            font-size: 20px;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add subtle animation to form elements
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach((group, index) => {
            group.style.opacity = '0';
            group.style.transform = 'translateY(20px)';
            group.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            
            setTimeout(() => {
                group.style.opacity = '1';
                group.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });
    });
</script>
@endpush
