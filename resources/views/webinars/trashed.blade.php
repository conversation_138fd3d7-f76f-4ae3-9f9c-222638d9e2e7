@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="main-title m-0">
            <i class="fas fa-trash"></i> Webinar đã xóa
        </h1>
        <div>
            <a href="{{ route('webinars.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại
            </a>
        </div>
    </div>

    @if($webinars->isEmpty())
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> Không có webinar nào trong thùng rác.
        </div>
    @else
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i> Danh sách webinar đã xóa
                    </h5>
                    <span class="badge bg-danger">{{ $webinars->total() }} webinar</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Tiêu đề</th>
                                <th>Chủ sở hữu</th>
                                <th>Người thuyết trình</th>
                                <th>Video</th>
                                <th>Ngày xóa</th>
                                <th width="200">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($webinars as $webinar)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $webinar->title }}</div>
                                        <small class="text-muted">ID: {{ $webinar->id }}</small>
                                    </td>
                                    <td>{{ $webinar->user->name }}</td>
                                    <td>{{ $webinar->speaker }}</td>
                                    <td>
                                        @if($webinar->video_path)
                                            @if($webinar->s3_url || str_starts_with($webinar->video_path, 's3://'))
                                                <span class="badge bg-info">
                                                    <i class="fas fa-cloud me-1"></i> S3
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-hdd me-1"></i> Local
                                                </span>
                                            @endif
                                            @if($webinar->video_file_size)
                                                <br><small class="text-muted">@filesize($webinar->video_file_size)</small>
                                            @endif
                                        @else
                                            <span class="text-muted">Không có video</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $webinar->deleted_at->format('d/m/Y H:i') }}</div>
                                        <small class="text-muted">{{ $webinar->deleted_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- Restore Button -->
                                            <form action="{{ route('webinars.restore', $webinar->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success" 
                                                        onclick="return confirm('Bạn có chắc chắn muốn khôi phục webinar này?')"
                                                        title="Khôi phục">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>

                                            <!-- Force Delete Button -->
                                            <form action="{{ route('webinars.force-delete', $webinar->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                        onclick="return confirm('Bạn có chắc chắn muốn xóa vĩnh viễn webinar này? Hành động này không thể hoàn tác!')"
                                                        title="Xóa vĩnh viễn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @if($webinars->hasPages())
                <div class="card-footer">
                    {{ $webinars->links() }}
                </div>
            @endif
        </div>

        <!-- Cleanup Info -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-info-circle me-2"></i>Thông tin dọn dẹp tự động:</h6>
            <ul class="mb-0">
                <li>Webinar đã xóa sẽ được dọn dẹp tự động sau 7 ngày</li>
                <li>Files video trên S3 và local sẽ được xóa vĩnh viễn</li>
                <li>Bạn có thể khôi phục webinar trước khi bị dọn dẹp tự động</li>
                <li>Hoặc xóa vĩnh viễn ngay lập tức bằng nút <span class="badge bg-danger">Xóa vĩnh viễn</span></li>
            </ul>
        </div>
    @endif
</div>
@endsection
