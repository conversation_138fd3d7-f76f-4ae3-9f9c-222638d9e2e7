@extends('layouts.app')
@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="main-title m-0">
            <i class="fas fa-info-circle"></i>QUẢN LÝ LIVESTREAM LEARNING
        </h1>
        <div class="d-flex flex-wrap gap-2">

            <a href="{{ route('webinars.students', $webinar) }}"
               class="btn btn-light d-flex flex-column align-items-center p-2">
                <i class="fas fa-users mb-1 fs-5"></i>
                <span class="btn-text">Học sinh</span>
            </a>
            <a href="{{ route('webinars.index') }}" class="btn btn-light d-flex flex-column align-items-center p-2">
                <i class="fas fa-arrow-left mb-1 fs-5"></i>
                <span class="btn-text">Quay lại</span>
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h2 class="text-dark mb-3 fw-bold">
                        <i class="fas fa-video text-primary me-2"></i> Tên Webinar: {{ $webinar->title }}
                    </h2>
                    <p class="text-muted mb-4 d-flex align-items-center">
                        <i class="fas fa-user-tie text-primary me-2"></i> Người Thuyết Trình: <span
                            class="fw-semibold ms-1">{{ $webinar->speaker }}</span>
                    </p>
                    <div class="card mb-4">
                        <div class="card-header d-flex align-items-center">
                            <i class="fas fa-link text-primary me-2"></i>
                            <h5 class="mb-0">Cấu hình Livestream</h5>
                        </div>
                        <div class="card-body">
                            <form action="{{route("webinars.livestream.post", $webinar)}}" method="POST">
                                @csrf
                                <div class="form-group">
                                    <label class="form-label">Link live</label>
                                    <div class="input-group">
                                        <select name="type_live" class="form-select"
                                                style="    flex: 0.2 1 auto;">
                                            <option
                                                {{old("type_live", $webinar->livestreams["type"] ?? null) == "youtube" ? "selected" : ''}}
                                                value="youtube">
                                                Youtube
                                            </option>
                                            <option
                                                {{old("type_live", $webinar->livestreams["type"] ?? null) == "facebook" ? "selected" : ''}}
                                                value="facebook">
                                                Facebook
                                            </option>
                                            <option
                                                {{old("type_live", $webinar->livestreams["type"] ?? null) == "m3u8" ? "selected" : ''}}
                                                value="m3u8">
                                                Link m3u8
                                            </option>
                                            <option
                                                {{old("type_live", $webinar->livestreams["type"] ?? null) == "jitsi" ? "selected" : ''}}
                                                value="jitsi">
                                                Jitsi
                                            </option>
                                        </select>
                                        <input type="text" class="form-control" name="link_live"
                                               value="{{old("link_live", $webinar->livestreams["link"] ?? "")}}"
                                               placeholder="Nhập link live. VD: https://www.facebook.com/IamRubyNguyen/videos/2474717342891862/">
                                    </div>
                                    @error("link_live")
                                    <span class="text-danger">{{$message}}</span>
                                    @enderror
                                    @error("type_live")
                                    <span class="text-danger">{{$message}}</span>
                                    @enderror
                                </div>
                                <div class="form-group mt-3">
                                    <label for="" class="form-label">Chọn bộ câu hỏi hiển thị</label>
                                    <div class="input-group">
                                        <select name="question_set_id" id="question_set_id"
                                                class="form-select" {{isset($webinar->livestreams["is_enabled_question"]) && $webinar->livestreams["is_enabled_question"]==1 ? "disabled" : ""}}>
                                            <option value="">Chọn bộ câu hỏi</option>
                                            @foreach($questionSets as $questionSet)
                                                <option
                                                    {{old("question_set_id", $webinar->livestreams["question_set_id"] ?? null) == $questionSet->id ? "selected" : ''}}
                                                    value="{{$questionSet->id}}">{{$questionSet->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group mt-3">
                                    <label for="" class="form-label">Chọn câu hỏi cụ thể</label>
                                    <div class="input-group">
                                        <select name="question_id" id="question_id"
                                                class="form-select" {{isset($webinar->livestreams["is_enabled_question"]) && $webinar->livestreams["is_enabled_question"]==1 ? "disabled" : ((!isset($webinar->livestreams["question_set_id"]) || empty($webinar->livestreams["question_set_id"])) ? "disabled" : "")}}>
                                            <option value="">Vui lòng chọn bộ câu hỏi trước</option>
                                        </select>
                                        @if(isset($webinar->livestreams["is_enabled_question"]) && $webinar->livestreams["is_enabled_question"]==1)
                                            <button
                                                {{!isset($webinar->livestreams["is_live_learning"]) || $webinar->livestreams["is_live_learning"]==0?"disabled":""}} class="btn btn-outline-primary toggle-question"
                                                type="button"
                                                data-url="{{route("webinars.livestream.toggle-question",$webinar)}}">
                                                <i class="fa-solid fa-eye-low-vision"></i> Ẩn câu hỏi
                                            </button>
                                        @else
                                            <button
                                                {{!isset($webinar->livestreams["is_live_learning"]) || $webinar->livestreams["is_live_learning"]==0?"disabled":""}} class="btn btn-outline-primary toggle-question enable"
                                                type="button"
                                                data-url="{{route("webinars.livestream.toggle-question",$webinar)}}">
                                                <i class="fas fa-eye me-1"></i> Hiển thị câu hỏi
                                            </button>

                                        @endif
                                    </div>
                                </div>

                                <!-- Question Preview Section -->
                                <div id="question-preview" class="form-group mt-3" style="display: none;">
                                    <label class="form-label">Xem trước câu hỏi</label>
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title" id="preview-title">Tiêu đề câu hỏi</h6>
                                            <p class="card-text">
                                                <small class="text-muted">Loại: <span id="preview-type"></span></small>
                                            </p>
                                            <div id="preview-answers">
                                                <!-- Answers will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mt-3">
                                    @if(isset($webinar->livestreams["is_live_learning"]) && $webinar->livestreams["is_live_learning"]==1)
                                        <button class="btn btn-outline-primary start-live" name="is_live_learning"
                                                value="0"
                                                type="submit">
                                            <i class="fas fa-rss me-1"></i> Kết thúc live
                                        </button>
                                    @else
                                        <button class="btn btn-outline-primary start-live" name="is_live_learning"
                                                value="1"
                                                type="submit">
                                            <i class="fas fa-rss me-1"></i>Bắt đầu livestream
                                        </button>
                                    @endif
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function () {
            // Check if question_set_id has value on page load
            const initialQuestionSetId = $('#question_set_id').val();
            const savedQuestionId = "{{old('question_id', $webinar->livestreams['question_id'] ?? '')}}";

            if (initialQuestionSetId) {
                loadQuestionsForSet(initialQuestionSetId, savedQuestionId);
            }

            $(".toggle-ads").click(function () {
                const $button = $(this); // Lưu lại button được click
                const isEnable = $button.hasClass("enable");
                const originalHtml = isEnable
                    ? `<i class="fas fa-eye me-1"></i> Hiển thị`
                    : `<i class="fa-solid fa-eye-low-vision"></i> Tắt`;

                // Hiển thị spinner
                $button.html(`<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...`);

                const url = $button.data("url");
                const advertisement_id = $('select[name="advertisement_id"]').val();

                const formData = {
                    _token: "{{csrf_token()}}",
                    advertisement_id: advertisement_id,
                    status: isEnable ? 1 : 0,
                };

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        if (response.error) {
                            Swal.fire({
                                title: response.mess,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                            $button.html(originalHtml);
                            if (isEnable) {
                                $button.addClass("enable");
                            } else {
                                $button.removeClass("enable");
                            }
                        } else {
                            if (response.flag) {
                                $button.removeClass("enable");
                                $button.html(`<i class="fa-solid fa-eye-low-vision"></i> Tắt`);
                            } else {
                                $button.addClass("enable");
                                $button.html(`<i class="fas fa-eye me-1"></i> Hiển thị`);
                            }
                            Swal.fire({
                                title: response.mess,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function () {
                        // Nếu AJAX lỗi, quay lại trạng thái ban đầu
                        Swal.fire({
                            title: "Có lỗi xảy ra khi xử lý yêu cầu.",
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        $button.html(originalHtml);
                        if (isEnable) {
                            $button.addClass("enable");
                        } else {
                            $button.removeClass("enable");
                        }
                    }
                });
            });

            // Function to load questions for a question set
            function loadQuestionsForSet(questionSetId, selectedQuestionId = '') {
                const $questionSelect = $('#question_id');
                const $questionButton = $('.toggle-question');
                const isQuestionDisplaying = !$questionButton.hasClass('enable'); // If doesn't have enable class, question is currently being displayed

                if (questionSetId) {
                    // Enable question dropdown and button only if question is not currently being displayed
                    if (!isQuestionDisplaying) {
                        $questionSelect.prop('disabled', false);
                    }
                    $questionButton.prop('disabled', false);

                    // Show loading state
                    $questionSelect.html('<option value="">Đang tải câu hỏi...</option>');

                    // Hide question preview
                    $('#question-preview').hide();

                    // Load questions via AJAX
                    $.ajax({
                        url: `/question-sets/${questionSetId}/questions`,
                        type: 'GET',
                        success: function (response) {
                            if (response.success && response.questions) {
                                let options = '<option value="">Chọn câu hỏi</option>';
                                window.questionsData = {}; // Store questions data for preview

                                response.questions.forEach(function (question) {
                                    // Store question data for preview
                                    window.questionsData[question.id] = question;

                                    // Create preview of answers for dropdown
                                    let answerPreview = '';
                                    if (question.answers && question.answers.length > 0) {
                                        answerPreview = question.answers.slice(0, 2).join(', ');
                                        if (question.answers.length > 2) {
                                            answerPreview += '...';
                                        }
                                    }

                                    const displayText = `${question.title}`;
                                    const isSelected = selectedQuestionId && selectedQuestionId == question.id ? 'selected' : '';
                                    options += `<option value="${question.id}" title="${question.title}" ${isSelected}>${displayText}</option>`;
                                });
                                $questionSelect.html(options);

                                // If a question was pre-selected, trigger preview
                                if (selectedQuestionId && window.questionsData[selectedQuestionId]) {
                                    $questionSelect.trigger('change');
                                }
                            } else {
                                $questionSelect.html('<option value="">Không có câu hỏi nào</option>');
                            }
                        },
                        error: function () {
                            $questionSelect.html('<option value="">Lỗi khi tải câu hỏi</option>');
                            Swal.fire({
                                title: "Có lỗi xảy ra khi tải danh sách câu hỏi",
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    });
                } else {
                    // Disable question dropdown and button
                    $questionSelect.prop('disabled', true).html('<option value="">Vui lòng chọn bộ câu hỏi trước</option>');
                    $questionButton.prop('disabled', true);
                    $('#question-preview').hide();
                }
            }

            // Function to update select states based on question display status
            function updateSelectStates(isQuestionDisplaying) {
                const $questionSetSelect = $('#question_set_id');
                const $questionSelect = $('#question_id');

                if (isQuestionDisplaying) {
                    // Disable both selects when question is being displayed
                    $questionSetSelect.prop('disabled', true);
                    $questionSelect.prop('disabled', true);
                } else {
                    // Enable question set select when question is not being displayed
                    $questionSetSelect.prop('disabled', false);
                    // Enable question select only if question set has value
                    if ($questionSetSelect.val()) {
                        $questionSelect.prop('disabled', false);
                    }
                }
            }

            // Handle question set selection
            $('#question_set_id').change(function () {
                const questionSetId = $(this).val();
                loadQuestionsForSet(questionSetId);
            });

            // Handle question selection for preview
            $('#question_id').change(function () {
                const questionId = $(this).val();
                const $previewSection = $('#question-preview');

                if (questionId && window.questionsData && window.questionsData[questionId]) {
                    const question = window.questionsData[questionId];

                    // Update preview content
                    $('#preview-title').text(question.title);
                    $('#preview-type').text(question.type);

                    // Generate answers display
                    let answersHtml = '<strong>Đáp án:</strong><ul class="list-unstyled mt-2">';
                    if (question.answers && question.answers.length > 0) {
                        question.answers.forEach(function (answer, index) {
                            answersHtml += `<li><span class="badge bg-light text-dark me-2">${String.fromCharCode(65 + index)}</span>${answer.label??answer}</li>`;
                        });
                    } else {
                        answersHtml += '<li class="text-muted">Không có đáp án</li>';
                    }
                    answersHtml += '</ul>';

                    $('#preview-answers').html(answersHtml);
                    $previewSection.show();
                } else {
                    $previewSection.hide();
                }
            });

            // Handle question display toggle
            $('body').on('click', '.toggle-question', function () {
                const $button = $(this);
                const questionId = $('#question_id').val();

                if (!questionId) {
                    Swal.fire({
                        title: "Vui lòng chọn câu hỏi trước",
                        icon: 'warning',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                const isEnable = $button.hasClass("enable");
                const originalHtml = isEnable
                    ? `<i class="fas fa-question-circle me-1"></i> Hiển thị câu hỏi`
                    : `<i class="fas fa-eye-slash me-1"></i> Ẩn câu hỏi`;

                // Show loading state
                $button.html(`<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...`);

                // TODO: Implement question display API call
                const url = $button.data("url");
                const question_set_id = $('select[name="question_set_id"]').val();
                const question_id = $('select[name="question_id"]').val();


                const formData = {
                    _token: "{{csrf_token()}}",
                    question_set_id: question_set_id,
                    question_id: question_id,
                    status: isEnable ? 1 : 0,
                };
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        if (response.error) {
                            Swal.fire({
                                title: response.mess,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                            $button.html(originalHtml);
                            if (isEnable) {
                                $button.addClass("enable");
                            } else {
                                $button.removeClass("enable");
                            }
                        } else {
                            if (response.flag) {
                                $button.removeClass("enable");
                                $button.html(`<i class="fas fa-eye-slash me-1"></i> Ẩn câu hỏi`);
                                // Question is now being displayed, disable selects
                                updateSelectStates(true);
                            } else {
                                $button.addClass("enable");
                                $button.html(`<i class="fas fa-question-circle me-1"></i> Hiển thị câu hỏi`);
                                // Question is now hidden, enable selects
                                updateSelectStates(false);
                            }
                            Swal.fire({
                                title: response.mess,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function () {
                        // Nếu AJAX lỗi, quay lại trạng thái ban đầu
                        Swal.fire({
                            title: "Có lỗi xảy ra khi xử lý yêu cầu.",
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        $button.html(originalHtml);
                        if (isEnable) {
                            $button.addClass("enable");
                        } else {
                            $button.removeClass("enable");
                        }
                    }
                });
            });
        });
    </script>
@endpush

@push('styles')
    <style>
        .btn {
            position: relative;
            min-width: 80px;
        }

        .btn-text {
            font-size: 0.85rem;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
            display: block;
            text-align: center;
        }

        /* Analytics Dashboard Styles */
        .analytics-dashboard {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            color: white;
        }

        .analytics-dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .revenue-section {
            display: flex;
            flex-direction: column;
        }

        .revenue-label {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .revenue-amount {
            font-size: 48px;
            font-weight: 800;
            color: white;
            line-height: 1;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .live-info-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 12px;
        }

        .live-time-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 12px 16px;
            min-width: 200px;
        }

        .live-start-time {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .live-start-time i {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .live-duration-display {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .duration-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .live-timer {
            font-size: 20px;
            font-weight: 700;
            color: #4facfe;
            line-height: 1;
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .offline-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            min-width: 200px;
        }

        .next-live-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .next-live-info i {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        .current-time-display {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .current-time-display i {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .live-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 0.5px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .live-badge.active {
            background: rgba(46, 204, 113, 0.9);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .live-badge.inactive {
            background: rgba(149, 165, 166, 0.9);
            color: white;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: livePulse 1.5s infinite ease-in-out;
        }

        .offline-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
        }

        @keyframes livePulse {

            0%,
            100% {
                transform: scale(1);
                opacity: 1;
            }

            50% {
                transform: scale(1.3);
                opacity: 0.7;
            }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 16px;
            padding: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .metric-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .metric-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .metric-item:hover::before {
            opacity: 1;
        }

        .metric-icon {
            font-size: 24px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .metric-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: white;
            line-height: 1;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        /* Animation for number changes */
        .metric-value.updating {
            animation: numberPulse 0.3s ease-in-out;
        }

        @keyframes numberPulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
                color: #4facfe;
            }

            100% {
                transform: scale(1);
            }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .revenue-amount {
                font-size: 40px;
            }
        }

        @media (max-width: 992px) {
            .analytics-dashboard {
                padding: 30px;
            }

            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .metric-value {
                font-size: 20px;
            }

            .revenue-amount {
                font-size: 36px;
            }
        }

        @media (max-width: 768px) {
            .d-flex.justify-content-between {
                flex-direction: column;
                align-items: stretch !important;
                gap: 1rem;
            }

            .d-flex.flex-wrap {
                justify-content: stretch;
            }

            .d-flex.flex-wrap .btn {
                flex: 1;
            }

            .btn-text {
                font-size: 0.75rem;
            }

            .analytics-dashboard {
                padding: 25px 20px;
                border-radius: 16px;
                margin: 0 -15px;
            }

            .dashboard-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 15px;
                margin-bottom: 25px;
            }

            .live-info-section {
                flex-direction: column;
                align-items: center;
                width: 100%;
            }

            .live-time-info,
            .offline-info {
                min-width: auto;
                width: 100%;
                max-width: 300px;
            }

            .live-timer {
                font-size: 18px;
            }

            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;
            }

            .metric-item {
                padding: 15px;
            }

            .metric-value {
                font-size: 16px;
            }

            .metric-label {
                font-size: 10px;
            }

            .revenue-amount {
                font-size: 32px;
            }

            .revenue-label {
                font-size: 14px;
            }
        }

        @media (max-width: 576px) {
            .analytics-dashboard {
                padding: 20px 15px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .metric-item {
                padding: 12px;
            }

            .metric-value {
                font-size: 18px;
            }

            .metric-icon {
                font-size: 20px;
                margin-bottom: 8px;
            }

            .revenue-amount {
                font-size: 28px;
            }
        }

        /* Loading animation */
        .analytics-dashboard.loading {
            opacity: 0;
            transform: translateY(20px);
        }

        .analytics-dashboard.loaded {
            animation: dashboardFadeIn 0.8s ease-out forwards;
        }

        @keyframes dashboardFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Staggered animation for metrics */
        .metric-item {
            opacity: 0;
            transform: translateY(20px);
            animation: metricFadeIn 0.6s ease-out forwards;
        }

        .metric-item:nth-child(1) {
            animation-delay: 0.1s;
        }

        .metric-item:nth-child(2) {
            animation-delay: 0.2s;
        }

        .metric-item:nth-child(3) {
            animation-delay: 0.3s;
        }

        .metric-item:nth-child(4) {
            animation-delay: 0.4s;
        }

        .metric-item:nth-child(5) {
            animation-delay: 0.5s;
        }

        .metric-item:nth-child(6) {
            animation-delay: 0.6s;
        }

        .metric-item:nth-child(7) {
            animation-delay: 0.7s;
        }

        .metric-item:nth-child(8) {
            animation-delay: 0.8s;
        }

        .metric-item:nth-child(9) {
            animation-delay: 0.9s;
        }

        .metric-item:nth-child(10) {
            animation-delay: 1.0s;
        }

        @keyframes metricFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Question Preview Styles */
        #question-preview {
            animation: slideIn 0.3s ease-out;
        }

        #question-preview .card {
            border: 1px solid #e3f2fd;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #question-preview .card-body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        #question-preview .card-title {
            color: #1976d2;
            font-weight: 600;
        }

        #preview-answers ul li {
            padding: 0.25rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        #preview-answers ul li:last-child {
            border-bottom: none;
        }

        #preview-answers .badge {
            min-width: 1.5rem;
            font-weight: 600;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Question dropdown improvements */
        #question_id option {
            padding: 0.5rem;
        }
    </style>
@endpush
