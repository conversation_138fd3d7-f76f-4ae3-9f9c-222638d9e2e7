@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-video"></i> QUẢN LÝ WEBINAR
    </h1>
    <div>
        @can('create', App\Models\Webinar::class)
            <a href="{{ route('webinars.create') }}" class="btn btn-primary me-2">
                <i class="fas fa-plus me-1"></i> Tạo Webinar
            </a>
        @endcan
        <a href="{{ route('webinars.trashed') }}" class="btn btn-outline-danger">
            <i class="fas fa-trash me-1"></i> Thùng rác
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <i class="fas fa-list me-2"></i>
            <h5 class="m-0"><PERSON><PERSON></h5>
        </div>
        <div>
            <button class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-filter me-1"></i> Lọc
            </button>
        </div>
    </div>
    <div class="card-body">
        @if($webinars->isEmpty())
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> Chưa có webinar nào.
                @can('create', App\Models\Webinar::class)
                    <a href="{{ route('webinars.create') }}" class="alert-link">Tạo webinar đầu tiên của bạn</a>.
                @endcan
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Tên Webinar</th>
                            <th>Người Thuyết Trình</th>
                            <th>Số Người Xem</th>
                            <th>Đường Dẫn Tham Gia</th>
                            <th>Buổi Kế Tiếp</th>
                            <th style="width: 150px;">Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($webinars as $webinar)
                            <tr>
                                <td class="fw-medium">
                                    <a href="{{ route('webinars.show', $webinar) }}" class="text-decoration-none ">
                                        {{ $webinar->title }}
                                    </a>
                                </td>
                                <td>{{ $webinar->speaker }}</td>
                                <td>
                                    <span class="badge badge-primary">{{ $webinar->virtual_viewers }}</span>
                                </td>
                                <td>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-sm" value="{{ $webinar->join_url }}" id="join-url-{{ $webinar->id }}" readonly>
                                        <button class="btn btn-outline-secondary btn-sm copy-btn" type="button" data-clipboard-target="#join-url-{{ $webinar->id }}" title="Sao chép">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    @if($webinar->schedules && count($webinar->schedules) > 0)
                                        @php
                                            $nextSchedule = null;
                                            $now = \Carbon\Carbon::now();

                                            foreach($webinar->schedules as $schedule) {
                                                $scheduledTime = \Carbon\Carbon::parse($schedule['date'] . ' ' . $schedule['time']);

                                                // Kiểm tra thời gian theo cùng định dạng với trang chi tiết
                                                $isUpcoming = false;
                                                if ($scheduledTime->format('Y-m-d') > $now->format('Y-m-d')) {
                                                    $isUpcoming = true; // Ngày sau ngày hiện tại
                                                } elseif ($scheduledTime->format('Y-m-d') == $now->format('Y-m-d') &&
                                                        (int)$scheduledTime->format('H') >= (int)$now->format('H')) {
                                                    $isUpcoming = true; // Cùng ngày và giờ sau hoặc bằng giờ hiện tại
                                                }

                                                if ($isUpcoming && (!$nextSchedule ||
                                                    \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->isAfter($scheduledTime))) {
                                                    $nextSchedule = $schedule;
                                                }
                                            }
                                        @endphp

                                        @if($nextSchedule)
                                            <span class="badge badge-primary">
                                                <i class="far fa-calendar me-1"></i>
                                                {{ \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->format('d/m/Y H:i') }}
                                            </span>
                                        @else
                                            <span class="badge badge-secondary">
                                                <i class="fas fa-times-circle me-1"></i>
                                                Không có buổi sắp tới
                                            </span>
                                        @endif
                                    @else
                                        <span class="badge badge-warning">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            Chưa lên lịch
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @can('update', $webinar)
                                            <a href="{{ route('webinars.edit', $webinar) }}" class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('webinars.upload', $webinar) }}" class="btn btn-sm btn-outline-success" title="Tải lên video">
                                                <i class="fas fa-upload"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-secondary hash-id-btn"
                                                    data-id="{{ $webinar->id }}"
                                                    data-hash="{{ $webinar->hashId() }}"
                                                    title="Tạo mới mã hash">
                                                <i class="fas fa-key"></i>
                                            </button>
                                        @endcan
                                        @can('delete', $webinar)
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                                    data-id="{{ $webinar->id }}"
                                                    data-title="{{ $webinar->title }}"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <form id="delete-form-{{ $webinar->id }}" action="{{ route('webinars.destroy', $webinar) }}" method="POST" class="d-none">
                                                @csrf
                                                @method('DELETE')
                                            </form>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $webinars->links() }}
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý sao chép đường dẫn
        var clipboard = new ClipboardJS('.copy-btn');

        clipboard.on('success', function(e) {
            const button = e.trigger;
            const originalHTML = button.innerHTML;

            button.innerHTML = '<i class="fas fa-check"></i>';

            // Hiển thị thông báo
            Swal.fire({
                title: 'Đã sao chép!',
                text: 'Đường dẫn đã được sao chép vào clipboard',
                icon: 'success',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });

            setTimeout(function() {
                button.innerHTML = originalHTML;
            }, 2000);

            e.clearSelection();
        });

        // Xử lý tạo mới mã hash
        const hashIdButtons = document.querySelectorAll('.hash-id-btn');

        hashIdButtons.forEach(button => {
            button.addEventListener('click', function() {
                const webinarId = this.getAttribute('data-id');
                const currentHash = this.getAttribute('data-hash');

                Swal.fire({
                    title: 'Thông tin mã hash',
                    html: `
                        <div class="text-start mb-3">
                            <p><strong>ID:</strong> ${webinarId}</p>
                            <p><strong>Mã hash hiện tại:</strong> ${currentHash}</p>
                            <p><strong>URL:</strong> ${window.location.origin}/webinars/${currentHash}</p>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <button id="check-hash-btn" class="btn btn-info">Kiểm tra mã hash</button>
                            <button id="regenerate-hash-btn" class="btn btn-warning">Tạo mới mã hash</button>
                        </div>
                        <div id="hash-result" class="mt-3"></div>
                    `,
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: 'Đóng',
                    cancelButtonColor: '#6c757d',
                    allowOutsideClick: false,
                    didOpen: () => {
                        // Xử lý nút kiểm tra mã hash
                        document.getElementById('check-hash-btn').addEventListener('click', function() {
                            const hashResult = document.getElementById('hash-result');
                            hashResult.innerHTML = '<div class="alert alert-info">Đang kiểm tra...</div>';

                            fetch('{{ route("webinars.check-hash-id") }}', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                body: JSON.stringify({
                                    hash_id: currentHash
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    if (data.exists) {
                                        hashResult.innerHTML = `
                                            <div class="alert alert-success">
                                                <p class="mb-0"><strong>Mã hash hợp lệ</strong></p>
                                                <p class="mb-0">Trỏ đến webinar: ${data.title} (ID: ${data.webinar_id})</p>
                                            </div>
                                        `;
                                    } else {
                                        hashResult.innerHTML = `
                                            <div class="alert alert-warning">
                                                <p class="mb-0"><strong>Mã hash không hợp lệ</strong></p>
                                                <p class="mb-0">Không tìm thấy webinar tương ứng</p>
                                            </div>
                                        `;
                                    }
                                } else {
                                    hashResult.innerHTML = `
                                        <div class="alert alert-danger">
                                            <p class="mb-0"><strong>Lỗi:</strong> ${data.message}</p>
                                        </div>
                                    `;
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                hashResult.innerHTML = `
                                    <div class="alert alert-danger">
                                        <p class="mb-0"><strong>Lỗi kết nối:</strong> Vui lòng thử lại sau</p>
                                    </div>
                                `;
                            });
                        });

                        // Xử lý nút tạo mới mã hash
                        document.getElementById('regenerate-hash-btn').addEventListener('click', function() {
                            const hashResult = document.getElementById('hash-result');
                            hashResult.innerHTML = '<div class="alert alert-info">Đang tạo mới mã hash...</div>';

                            fetch('{{ route("webinars.regenerate-hash-id") }}', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                body: JSON.stringify({
                                    webinar_id: webinarId
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    hashResult.innerHTML = `
                                        <div class="alert alert-success">
                                            <p class="mb-0"><strong>Tạo mới thành công!</strong></p>
                                            <p class="mb-0">Mã hash mới: ${data.hash_id}</p>
                                            <p class="mb-0">URL mới: ${data.url}</p>
                                        </div>
                                    `;

                                    // Cập nhật thuộc tính data-hash trên nút
                                    button.setAttribute('data-hash', data.hash_id);
                                } else {
                                    hashResult.innerHTML = `
                                        <div class="alert alert-danger">
                                            <p class="mb-0"><strong>Lỗi:</strong> ${data.message}</p>
                                        </div>
                                    `;
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                hashResult.innerHTML = `
                                    <div class="alert alert-danger">
                                        <p class="mb-0"><strong>Lỗi kết nối:</strong> Vui lòng thử lại sau</p>
                                    </div>
                                `;
                            });
                        });
                    }
                });
            });
        });

        // Xử lý xóa webinar
        const deleteButtons = document.querySelectorAll('.delete-btn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const webinarId = this.getAttribute('data-id');
                const webinarTitle = this.getAttribute('data-title');

                Swal.fire({
                    title: 'Xác nhận xóa?',
                    html: `Bạn có chắc chắn muốn xóa webinar <strong>${webinarTitle}</strong>?<br>Hành động này không thể hoàn tác!`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Xóa',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        document.getElementById(`delete-form-${webinarId}`).submit();
                    }
                });
            });
        });
    });
</script>
@endpush
