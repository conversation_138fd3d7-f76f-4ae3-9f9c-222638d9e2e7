@extends('layouts.app')
@section('content')
    <!-- Header với gradient background -->
    <div class="students-header mb-4">
        <div class="header-content d-flex justify-content-between align-items-center">
            <div class="header-title">
                <h1 class="main-title m-0 text-white">
                    <i class="fas fa-graduation-cap me-3"></i>
                    <span class="title-text">QUẢN LÝ HỌC SINH</span>
                </h1>
                <p class="subtitle text-white-50 mt-2 mb-0">
                    <i class="fas fa-video me-2"></i>{{ $webinar->title }}
                </p>
            </div>
            <div class="header-actions d-flex flex-wrap gap-2">
                <a href="{{ route('webinars.livestream.class', $webinar) }}" class="btn btn-modern btn-back">
                    <i class="fas fa-arrow-left me-2"></i>
                    <span>Quay lại</span>
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Thêm học sinh -->
        <div class="col-12 mb-4">
            <div class="card modern-card add-student-card">
                <div class="card-header modern-header">
                    <div class="header-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h5 class="mb-0 header-title">Thêm học sinh vào lớp</h5>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#createStudentModal">
                            <i class="fas fa-user-plus me-1"></i>
                            <span class="d-none d-sm-inline">Tạo mới</span>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="add-student-form">
                        @csrf
                        <div class="search-form">
                            <div class="row">
                                <div class="col-12">
                                                            <label class="form-label fw-medium">
                            <i class="fas fa-user-search me-2"></i>Tìm participant có sẵn để chuyển thành học sinh
                        </label>
                        <div class="input-group" style="flex-wrap: nowrap;">
                            <select id="student-search" class="form-select" style="width: 100%;">
                                <option value="">Nhập email hoặc số điện thoại để tìm participant...</option>
                            </select>
                            <button type="button" class="btn btn-success" id="add-existing-student" disabled>
                                <i class="fas fa-plus me-1"></i>
                                Chuyển thành học sinh
                            </button>
                        </div>
                        <small class="text-muted mt-1 d-block">
                            <i class="fas fa-info-circle me-1"></i>
                            Click để hiển thị danh sách participants. Chọn participant rồi click "Chuyển thành học sinh"
                        </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Danh sách học sinh -->
        <div class="col-12">
            <div class="card modern-card students-list-card">
                <div class="card-header modern-header students-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon students-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="header-info">
                            <h5 class="mb-0 header-title">Danh sách học sinh</h5>
                            <div class="student-count">
                                <span id="total-students">0</span> học sinh
                            </div>
                        </div>
                    </div>
                    <div class="header-controls">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search-table"
                                placeholder="Lọc học sinh trong webinar này...">

                            <button class="btn btn-outline-secondary" id="refresh-list">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body modern-body">
                    <div class="table-container">
                        <table class="table modern-table" id="students-table">
                            <thead class="modern-thead">
                                <tr>
                                    <th width="5%" class="text-center">#</th>
                                    <th width="20%">
                                        <i class="fas fa-user me-2"></i>Tên
                                    </th>
                                    <th width="20%">
                                        <i class="fas fa-envelope me-2"></i>Email
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-phone me-2"></i>Số điện thoại
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-calendar me-2"></i>Ngày tham gia
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-sticky-note me-2"></i>Ghi chú
                                    </th>
                                    <th width="10%" class="text-center">
                                        <i class="fas fa-cogs me-2"></i>Thao tác
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="modern-tbody">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Students pagination" class="mt-3">
                        <ul class="pagination pagination-sm justify-content-center" id="pagination">
                            <!-- Pagination will be rendered here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal tạo học sinh mới -->
    <div class="modal fade" id="createStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus text-primary me-2"></i>Tạo học sinh mới
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="create-student-form">
                    <div class="modal-body">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Tên <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Số điện thoại <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="phone" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Mật khẩu <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" name="password" required>
                                        <button type="button" class="btn btn-outline-secondary" id="toggle-password">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Ghi chú</label>
                            <textarea class="form-control" name="note" rows="3"
                                placeholder="Ghi chú về học sinh..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Tạo và thêm vào lớp
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal chỉnh sửa học sinh -->
    <div class="modal fade" id="editStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit text-primary me-2"></i>Chỉnh sửa thông tin học sinh
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="edit-student-form">
                    <div class="modal-body">
                        @csrf
                        <input type="hidden" name="student_id" id="edit-student-id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Tên <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" id="edit-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" name="email" id="edit-email" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Số điện thoại <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="phone" id="edit-phone" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Mật khẩu mới (để trống nếu không đổi)</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" name="password" id="edit-password">
                                        <button type="button" class="btn btn-outline-secondary" id="toggle-edit-password">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Ghi chú</label>
                            <textarea class="form-control" name="note" id="edit-note" rows="3"
                                placeholder="Ghi chú về học sinh..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Cập nhật
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"
        rel="stylesheet" />
    <style>
        /* Header Styles */
        .students-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        .header-content {
            position: relative;
        }

        .main-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: #495057;
        }

        .title-text {
            color: #495057;
        }

        .subtitle {
            font-size: 1rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        /* Modern Buttons */
        .btn-modern {
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-video {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .btn-video:hover {
            background: #c82333;
            border-color: #bd2130;
            color: white;
        }

        .btn-back {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .btn-back:hover {
            background: #5a6268;
            border-color: #545b62;
            color: white;
        }

        /* Modern Cards */
        .modern-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: box-shadow 0.2s ease;
            background: white;
        }

        .modern-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        .modern-header {
            background: #f8f9fa;
            color: #495057;
            padding: 1.25rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-icon {
            width: 40px;
            height: 40px;
            background: #e9ecef;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #6c757d;
        }

        .students-icon {
            background: #e3f2fd;
            color: #1976d2;
        }

        .header-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
        }

        .student-count {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.25rem;
        }

        .students-header {
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-controls {
            align-items: center;
        }

        /* Search Form Styles */
        .search-form .input-group .form-select {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .search-form .input-group .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            white-space: nowrap;
        }

        .search-form .input-group .btn:disabled {
            opacity: 0.65;
        }

        /* Header Controls Input Group */
        .header-controls .input-group {
            flex-wrap: nowrap;
        }

        .header-controls .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .header-controls .btn:not(:last-child) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-left: 0;
        }

        .header-controls .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: 0;
        }

        .btn-refresh {
            transition: all 0.2s ease;
        }

        .btn-refresh:hover {
            color: #6c757d;
            background-color: transparent;
            border-color: #6c757d;
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }

        .modern-table {
            margin: 0;
            border-collapse: collapse;
        }

        .modern-thead {
            background: #f8f9fa;
            color: #495057;
        }

        .modern-thead th {
            padding: 1rem;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .modern-tbody tr {
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .modern-tbody tr:hover {
            background: #f8f9fa;
        }

        .modern-tbody td {
            padding: 1rem;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        /* Avatar Styles */
        .avatar-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
            background: #007bff;
            color: white;
        }

        /* Action Buttons */
        .action-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .action-btn.btn-outline-primary {
            background: white;
            color: #007bff;
            border: 1px solid #007bff;
        }

        .action-btn.btn-outline-primary:hover {
            background: #007bff;
            color: white;
        }

        .action-btn.btn-outline-danger {
            background: white;
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .action-btn.btn-outline-danger:hover {
            background: #dc3545;
            color: white;
        }

        /* Modal Enhancements */
        .modal-header {
            background: #f8f9fa;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.375rem 0.375rem 0 0;
        }

        .modal-content {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .form-control {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 10px 12px;
            font-size: 0.95rem;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
        }

        .search-form {
            position: relative;
            z-index: 10;
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 15px;
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Pagination */
        .pagination {
            margin-top: 1.5rem;
        }

        .page-link {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            color: #495057;
            transition: all 0.2s ease;
        }

        .page-link:hover {
            background: #e9ecef;
            border-color: #dee2e6;
            color: #495057;
        }

        .page-item.active .page-link {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        .select2-container--bootstrap-5 .select2-selection__rendered {
            line-height: unset !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .students-header {
                padding: 1.5rem;
            }

            .main-title {
                font-size: 1.5rem;
            }

            .btn-modern {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .action-btn {
                width: 32px;
                height: 32px;
            }

            .avatar-circle {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            .modern-tbody td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }

            .modern-thead th {
                padding: 0.75rem 0.5rem;
                font-size: 0.8rem;
            }

            .students-header .d-flex {
                flex-direction: column;
                gap: 1rem;
            }

            .header-actions {
                width: 100%;
            }
        }

        @media (max-width: 576px) {
            .header-actions {
                flex-direction: column;
                width: 100%;
            }

            .btn-modern {
                width: 100%;
                justify-content: center;
            }

            .table-container {
                font-size: 0.8rem;
            }

            .header-controls .input-group {
                flex-direction: column;
            }

            .header-controls .form-control {
                border-radius: 0.375rem;
                margin-bottom: 0.5rem;
            }

            .header-controls .btn {
                border-radius: 0.375rem;
                border-left: 1px solid;
                margin-bottom: 0.25rem;
            }

            .header-controls .btn:not(:last-child) {
                border-radius: 0.375rem;
            }

            .header-controls .btn:last-child {
                border-radius: 0.375rem;
                margin-bottom: 0;
            }

        }
    </style>
@endpush

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function () {
            let currentPage = 1;
            let searchTimeout;

            // Initialize Select2 for student search
            $('#student-search').select2({
                theme: 'bootstrap-5',
                allowClear: true,
                minimumInputLength: 3,
                dropdownParent: $('body'),
                ajax: {
                    url: '{{ route("webinars.students.search", $webinar) }}',
                    dataType: 'json',
                    delay: 300,
                    data: function (params) {
                        return {
                            q: params.term,
                            page: params.page || 1
                        };
                    },
                                    processResults: function (data) {
                    return {
                        results: data.users.map(participant => ({
                            id: participant.id,
                            text: `${participant.name} (${participant.email}) - ${participant.phone || 'Chưa có SĐT'}`,
                            user: participant
                        })),
                        pagination: {
                            more: data.users.length === 10
                        }
                    };
                }
                }
            });

            // Enable/disable add button based on selection
            $('#student-search').on('select2:select', function (e) {
                $('#add-existing-student').prop('disabled', false);
            });

            $('#student-search').on('select2:unselect', function (e) {
                $('#add-existing-student').prop('disabled', true);
            });

            // Add existing student to class
            $('#add-existing-student').click(function () {
                const selectedData = $('#student-search').select2('data')[0];
                if (!selectedData) return;

                const button = $(this);
                const originalHtml = button.html();

                button.html('<span class="spinner-border spinner-border-sm me-1"></span>Đang chuyển đổi...');
                button.prop('disabled', true);

                $.ajax({
                    url: '{{ route("webinars.students.add", $webinar) }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        user_id: selectedData.id
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });

                            // Clear selection and reload table
                            $('#student-search').val(null).trigger('change');
                            loadStudents();
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function (xhr) {
                        const errorMessage = xhr.responseJSON?.message || 'Có lỗi xảy ra khi chuyển participant thành học sinh';
                        Swal.fire({
                            title: 'Lỗi!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    },
                    complete: function () {
                        button.html(originalHtml);
                        button.prop('disabled', false);
                    }
                });
            });

            // Create new student form
            $('#create-student-form').submit(function (e) {
                e.preventDefault();

                const form = $(this);
                const submitBtn = form.find('button[type="submit"]');
                const originalHtml = submitBtn.html();

                submitBtn.html('<span class="spinner-border spinner-border-sm me-1"></span>Đang tạo...');
                submitBtn.prop('disabled', true);

                $.ajax({
                    url: '{{ route("webinars.students.create", $webinar) }}',
                    method: 'POST',
                    data: form.serialize(),
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });

                            // Reset form and close modal
                            form[0].reset();
                            $('#createStudentModal').modal('hide');
                            loadStudents();
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function (xhr) {
                        let errorMessage = 'Có lỗi xảy ra khi tạo học sinh';

                        if (xhr.responseJSON?.errors) {
                            const errors = Object.values(xhr.responseJSON.errors).flat();
                            errorMessage = errors.join('\n');
                        } else if (xhr.responseJSON?.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    },
                    complete: function () {
                        submitBtn.html(originalHtml);
                        submitBtn.prop('disabled', false);
                    }
                });
            });

            // Edit student form
            $('#edit-student-form').submit(function (e) {
                e.preventDefault();

                const form = $(this);
                const submitBtn = form.find('button[type="submit"]');
                const originalHtml = submitBtn.html();
                const studentId = $('#edit-student-id').val();

                submitBtn.html('<span class="spinner-border spinner-border-sm me-1"></span>Đang cập nhật...');
                submitBtn.prop('disabled', true);

                $.ajax({
                    url: '{{ route("webinars.students.update", $webinar) }}',
                    method: 'PUT',
                    data: form.serialize(),
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });

                            // Close modal and reload table
                            $('#editStudentModal').modal('hide');
                            loadStudents();
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function (xhr) {
                        let errorMessage = 'Có lỗi xảy ra khi cập nhật học sinh';

                        if (xhr.responseJSON?.errors) {
                            const errors = Object.values(xhr.responseJSON.errors).flat();
                            errorMessage = errors.join('\n');
                        } else if (xhr.responseJSON?.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    },
                    complete: function () {
                        submitBtn.html(originalHtml);
                        submitBtn.prop('disabled', false);
                    }
                });
            });

            // Password toggle functionality
            $('#toggle-password, #toggle-edit-password').click(function () {
                const button = $(this);
                const input = button.closest('.input-group').find('input');
                const icon = button.find('i');

                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    input.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Load students function
            function loadStudents(page = 1, search = '') {
                const tableBody = $('#students-table tbody');
                const loadingHtml = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                        </div>
                        <div class="mt-2">Đang tải danh sách học sinh...</div>
                    </td>
                </tr>
            `;

                tableBody.html(loadingHtml);

                $.ajax({
                    url: '{{ route("webinars.students.list", $webinar) }}',
                    method: 'GET',
                    data: {
                        page: page,
                        search: search
                    },
                    success: function (response) {
                        if (response.success) {
                            renderStudentsTable(response.data);
                            renderPagination(response.pagination);
                            $('#total-students').text(response.pagination.total);
                        } else {
                            tableBody.html(`
                            <tr>
                                <td colspan="7" class="text-center py-4 text-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    ${response.message || 'Có lỗi xảy ra khi tải dữ liệu'}
                                </td>
                            </tr>
                        `);
                        }
                    },
                    error: function () {
                        tableBody.html(`
                        <tr>
                            <td colspan="7" class="text-center py-4 text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Có lỗi xảy ra khi tải danh sách học sinh
                            </td>
                        </tr>
                    `);
                    }
                });
            }

            // Render students table
            function renderStudentsTable(students) {
                const tableBody = $('#students-table tbody');

                if (students.length === 0) {
                    tableBody.html(`
                    <tr>
                        <td colspan="7" class="text-center py-4 text-muted">
                            <i class="fas fa-users me-2"></i>
                            Chưa có học sinh nào trong lớp
                        </td>
                    </tr>
                `);
                    return;
                }

                let html = '';
                students.forEach((student, index) => {
                    const rowNumber = ((currentPage - 1) * 10) + index + 1;
                    html += `
                    <tr class="fade-in">
                        <td>${rowNumber}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2 bg-primary text-white">
                                    ${student.name.charAt(0).toUpperCase()}
                                </div>
                                <strong>${student.name}</strong>
                            </div>
                        </td>
                        <td>${student.email}</td>
                        <td>${student.phone || '<span class="text-muted">Chưa có</span>'}</td>
                        <td>
                            <small class="text-muted">
                                ${new Date(student.joined_at).toLocaleDateString('vi-VN')}
                            </small>
                        </td>
                        <td>
                            ${student.note ? `<small class="text-muted">${student.note}</small>` : '<span class="text-muted">-</span>'}
                        </td>
                        <td>
                            <div class="d-flex">
                                <button class="btn btn-outline-primary action-btn edit-student" 
                                        data-student='${JSON.stringify(student)}' 
                                        title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger action-btn remove-student" 
                                        data-id="${student.id}" 
                                        data-name="${student.name}" 
                                        title="Xóa khỏi lớp">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                });

                tableBody.html(html);
            }

            // Render pagination
            function renderPagination(pagination) {
                const paginationContainer = $('#pagination');

                if (pagination.last_page <= 1) {
                    paginationContainer.empty();
                    return;
                }

                let html = '';

                // Previous button
                if (pagination.current_page > 1) {
                    html += `
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${pagination.current_page - 1}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                `;
                }

                // Page numbers
                const startPage = Math.max(1, pagination.current_page - 2);
                const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

                for (let i = startPage; i <= endPage; i++) {
                    html += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
                }

                // Next button
                if (pagination.current_page < pagination.last_page) {
                    html += `
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${pagination.current_page + 1}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                `;
                }

                paginationContainer.html(html);
            }

            // Handle pagination clicks
            $(document).on('click', '.page-link', function (e) {
                e.preventDefault();
                const page = $(this).data('page');
                if (page) {
                    currentPage = page;
                    loadStudents(page, $('#search-table').val());
                }
            });

            // Handle table search
            $('#search-table').on('input', function () {
                const searchTerm = $(this).val();

                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentPage = 1;
                    loadStudents(1, searchTerm);
                }, 300);
            });

            // Handle refresh button
            $('#refresh-list').click(function () {
                $('#search-table').val('');
                currentPage = 1;
                loadStudents();
            });

            // Handle edit student
            $(document).on('click', '.edit-student', function () {
                const student = $(this).data('student');

                $('#edit-student-id').val(student.id);
                $('#edit-name').val(student.name);
                $('#edit-email').val(student.email);
                $('#edit-phone').val(student.phone || '');
                $('#edit-note').val(student.note || '');
                $('#edit-password').val('');

                $('#editStudentModal').modal('show');
            });

            // Handle remove student
            $(document).on('click', '.remove-student', function () {
                const studentId = $(this).data('id');
                const studentName = $(this).data('name');

                Swal.fire({
                    title: 'Xác nhận xóa',
                    text: `Bạn có chắc chắn muốn xóa học sinh "${studentName}" khỏi lớp?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Xóa',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        removeStudent(studentId);
                    }
                });
            });

            // Remove student function
            function removeStudent(studentId) {
                $.ajax({
                    url: '{{ route("webinars.students.remove", $webinar) }}',
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}',
                        student_id: studentId
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                            loadStudents(currentPage, $('#search-table').val());
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function (xhr) {
                        const errorMessage = xhr.responseJSON?.message || 'Có lỗi xảy ra khi xóa học sinh';
                        Swal.fire({
                            title: 'Lỗi!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }

            // Initial load
            loadStudents();
        });
    </script>
@endpush