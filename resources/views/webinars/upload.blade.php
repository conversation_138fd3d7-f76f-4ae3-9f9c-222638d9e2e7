@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-upload"></i> TẢI LÊN VIDEO WEBINAR
    </h1>
    <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> Quay Lại
    </a>
</div>

<style>
/* Custom styles for upload components */
.progress {
    height: 25px;
    background-color: #f0f0f0;
    border-radius: 30px;
    margin-bottom: 15px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,.1);
}

.progress-bar {
    height: 100%;
    border-radius: 30px;
    background-size: 30px 30px;
    background-image: linear-gradient(135deg,
        rgba(255, 255, 255, .15) 25%, transparent 25%,
        transparent 50%, rgba(255, 255, 255, .15) 50%,
        rgba(255, 255, 255, .15) 75%, transparent 75%,
        transparent);
    transition: width 0.5s ease;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-status-container {
    min-height: 150px;
}

.upload-stats p {
    font-size: 14px;
}

#upload-details {
    border-top: 1px solid rgba(0,0,0,.06);
    padding-top: 10px;
    margin-top: 5px;
}

/* Ensure stable height for the status display */
#upload-status {
    min-height: 100px;
    transition: none; /* Prevent transitions which can cause flickering */
}

.alert-info, .alert-success, .alert-danger, .alert-primary {
    border-left: 4px solid;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.alert-success {
    border-left-color: #198754;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-primary {
    border-left-color: #0d6efd;
}
</style>

<div class="row">
    <div class="col-md-7">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-file-upload me-2"></i> Tải Lên Video</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h5>{{ $webinar->title }}</h5>
                    <p class="text-muted">Người Thuyết Trình: {{ $webinar->speaker }}</p>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Tải lên video cho webinar của bạn. Video dung lượng lớn sẽ được tải lên AWS S3 để đảm bảo tốc độ và ổn định.
                </div>

                @if($webinar->video_path)
                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Webinar này đã có video. Tải lên video mới sẽ thay thế video hiện tại.
                    </div>
                @endif

                <div class="mb-4">
                    <form id="upload-form" enctype="multipart/form-data" method="POST" action="{{ route('webinars.upload.post', $webinar) }}">
                        @csrf
                        <input type="hidden" name="chunk" value="0">
                        <input type="hidden" name="chunks" value="1">
                        <input type="hidden" name="storage" value="s3">

                        <div class="mb-3">
                            <label for="video" class="form-label fw-medium">Chọn Tệp Video</label>
                            <div class="input-group">
                                <input type="file" class="form-control" id="video" name="file" accept="video/mp4" required>
                                <button type="submit" class="btn btn-primary" id="upload-btn">
                                    <i class="fas fa-upload me-1"></i> Tải Lên
                                </button>
                            </div>
                            <div class="form-text">Chỉ chấp nhận định dạng MP4 (Dung lượng tối đa: 5GB)</div>
                        </div>

                        <div class="mb-3">
                            <label for="video_duration" class="form-label fw-medium">Tổng Số Phút</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="video_duration" name="video_duration" min="1" step="1" placeholder="Tự động tính khi chọn video" required>
                                <span class="input-group-text d-none" id="duration-calculating">
                                    <span class="spinner-border spinner-border-sm" role="status"></span>
                                    <span class="ms-1">Đang tính...</span>
                                </span>
                            </div>
                            <div class="form-text">Thời lượng sẽ được tự động tính khi chọn video MP4, hoặc bạn có thể điều chỉnh thủ công</div>
                        </div>

                        <div class="mb-3" id="file-info"></div>

                        <div class="progress mb-3 d-none" id="upload-progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                        </div>

                        <div class="upload-status-container">
                            <div id="upload-status" class="mb-3"></div>
                        </div>
                        <div id="error-log" class="mb-3 d-none alert alert-danger"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-5">
        @if($webinar->video_path)
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-play-circle me-2"></i> Video Hiện Tại</h5>
                </div>
                <div class="card-body p-2">
                    <div class="ratio ratio-16x9">
                        <video controls class="rounded">
                            @if($webinar->s3_url)
                                @php
                                    // Fix S3 URL if it doesn't contain the bucket name
                                    $s3Url = $webinar->s3_url;
                                    $bucket = config('filesystems.disks.vns3.bucket');

                                    // Check if URL already contains bucket name
                                    if (!str_contains($s3Url, '/' . $bucket . '/') && str_contains($s3Url, '/webinar-')) {
                                        // Insert bucket name into URL
                                        $baseUrl = substr($s3Url, 0, strpos($s3Url, '/webinar-'));
                                        $path = substr($s3Url, strpos($s3Url, '/webinar-'));
                                        $s3Url = $baseUrl . '/' . $bucket . $path;
                                    }
                                @endphp
                                <source src="{{ $s3Url }}" type="video/mp4">
                            @elseif(strpos($webinar->video_path, 's3://') === 0)
                                {{-- Skip if s3 path but no URL --}}
                            @else
                                <source src="{{ Storage::url(str_replace('public/', '', $webinar->video_path)) }}" type="video/mp4">
                            @endif
                            Trình duyệt của bạn không hỗ trợ thẻ video.
                        </video>
                    </div>

                    {{-- Video Information --}}
                    <div class="mt-3 p-2 bg-light rounded">
                        <h6 class="mb-2"><i class="fas fa-info-circle me-1"></i> Thông Tin Video</h6>
                        <div class="row text-sm">
                            @if($webinar->video_duration_minutes)
                                <div class="col-6">
                                    <strong>Thời lượng:</strong> {{ $webinar->video_duration_minutes }} phút
                                </div>
                            @endif
                            @if($webinar->video_file_size)
                                <div class="col-6">
                                    <strong>Dung lượng:</strong> @filesize($webinar->video_file_size)
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="card h-100 d-flex justify-content-center align-items-center">
                <div class="text-center p-4">
                    <i class="fas fa-film fa-4x text-muted mb-3"></i>
                    <h5>Chưa có video</h5>
                    <p class="text-muted">Tải lên video để xem trước tại đây</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/spark-md5/3.0.2/spark-md5.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('upload-form');
        const fileInput = document.getElementById('video');
        const fileInfo = document.getElementById('file-info');
        const progressBar = document.getElementById('upload-progress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        const statusDiv = document.getElementById('upload-status');
        const errorLog = document.getElementById('error-log');
        const uploadBtn = document.getElementById('upload-btn');

        // Define chunk size - 2MB chunks for better reliability
        const chunkSize = 2 * 1024 * 1024;

        // Show file info when a file is selected
        fileInput.addEventListener('change', function() {
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                const fileSize = (file.size / (1024 * 1024)).toFixed(2); // Convert to MB
                fileInfo.innerHTML = `
                    <div class="alert alert-info">
                        <strong>Đã chọn tệp:</strong> ${file.name} (${fileSize} MB)
                        <br><strong>Loại:</strong> ${file.type || 'Không xác định'}
                    </div>
                `;

                // Log file details
                console.log('File selected:', {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    lastModified: file.lastModified
                });

                // Calculate video duration
                calculateVideoDuration(file);
            } else {
                fileInfo.innerHTML = '';
            }
        });

        // Function to calculate video duration
        function calculateVideoDuration(file) {
            // Create a URL for the video file
            const videoUrl = URL.createObjectURL(file);
            // Create a video element to load the file
            const video = document.createElement('video');

            // Add loading indicator
            const durationInput = document.getElementById('video_duration');
            const calculatingIndicator = document.getElementById('duration-calculating');

            durationInput.value = '';
            durationInput.placeholder = 'Đang tính thời lượng...';
            durationInput.disabled = true;
            calculatingIndicator.classList.remove('d-none');

            // When metadata is loaded, we can access duration
            video.onloadedmetadata = function() {
                // Get duration in seconds
                const durationInSeconds = video.duration;
                // Convert to minutes and round up
                const durationInMinutes = Math.ceil(durationInSeconds / 60);

                // Set the value in the duration input
                durationInput.value = durationInMinutes;
                durationInput.placeholder = 'Nhập tổng số phút của video';
                durationInput.disabled = false;
                calculatingIndicator.classList.add('d-none');

                // Update the file info to include duration
                const fileInfoAlert = fileInfo.querySelector('.alert');
                if (fileInfoAlert) {
                    const file = fileInput.files[0];
                    const fileSize = (file.size / (1024 * 1024)).toFixed(2); // Convert to MB
                    fileInfoAlert.innerHTML = `
                        <strong>Đã chọn tệp:</strong> ${file.name} (${fileSize} MB)
                        <br><strong>Loại:</strong> ${file.type || 'Không xác định'}
                        <br><strong>Thời lượng:</strong> ${durationInMinutes} phút (${Math.floor(durationInSeconds / 60)}:${Math.round(durationInSeconds % 60).toString().padStart(2, '0')})
                    `;
                }

                // Release video object
                URL.revokeObjectURL(videoUrl);

                console.log(`Video duration calculated: ${durationInMinutes} minutes (${durationInSeconds} seconds)`);
            };

            // Handle errors
            video.onerror = function() {
                console.error('Error loading video for duration calculation');
                durationInput.placeholder = 'Nhập tổng số phút của video';
                durationInput.disabled = false;
                calculatingIndicator.classList.add('d-none');
                URL.revokeObjectURL(videoUrl);
            };

            // Set the source and load the video
            video.src = videoUrl;
        }

        // Handle form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            errorLog.classList.add('d-none');
            errorLog.innerHTML = '';

            if (fileInput.files.length === 0) {
                Swal.fire({
                    title: 'Lỗi',
                    text: 'Vui lòng chọn một tệp video trước khi tải lên',
                    icon: 'error',
                    confirmButtonText: 'Đã hiểu'
                });
                return;
            }

            const videoDuration = document.getElementById('video_duration').value;
            if (!videoDuration || videoDuration <= 0) {
                Swal.fire({
                    title: 'Lỗi',
                    text: 'Vui lòng nhập tổng số phút của video',
                    icon: 'error',
                    confirmButtonText: 'Đã hiểu'
                });
                return;
            }

            const file = fileInput.files[0];
            const fileSize = file.size / (1024 * 1024); // Convert to MB
            const filetype = file.type;

            // Check file type
            if (filetype !== 'video/mp4') {
                Swal.fire({
                    title: 'Lỗi',
                    text: 'Chỉ chấp nhận định dạng MP4',
                    icon: 'error',
                    confirmButtonText: 'Đã hiểu'
                });
                return;
            }

            // Check file size (5GB = 5120MB)
            if (fileSize > 5120) {
                Swal.fire({
                    title: 'Lỗi',
                    text: 'Kích thước tệp không được vượt quá 5GB',
                    icon: 'error',
                    confirmButtonText: 'Đã hiểu'
                });
                return;
            }

            @if($webinar->video_path)
            // Show confirmation dialog if replacing existing video
            Swal.fire({
                title: 'Xác nhận thay thế video?',
                text: 'Video hiện tại sẽ bị xóa và thay thế bằng video mới. Bạn có chắc chắn muốn tiếp tục?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Thay thế',
                cancelButtonText: 'Hủy',
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                customClass: {
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-primary',
                    popup: 'video-confirm-popup'
                },
                buttonsStyling: true
            }).then((result) => {
                if (result.isConfirmed) {
                    processUpload();
                }
            });
            @else
            // If no existing video, proceed directly
            processUpload();
            @endif
        });

        // Function to process the upload after confirmation
        function processUpload() {
            // Show loading state
            uploadBtn.disabled = true;
            progressBar.classList.remove('d-none');
            statusDiv.innerHTML = `<div class="alert alert-info">Đang chuẩn bị tải lên...</div>`;

            const file = fileInput.files[0];

            // Generate a unique file identifier
            const fileId = Date.now() + '-' + Math.random().toString(36).substring(2);

            // Calculate total chunks
            const totalChunks = Math.ceil(file.size / chunkSize);
            console.log(`File will be uploaded in ${totalChunks} chunks`);

            // Display total upload information
            displayUploadInfo(file, totalChunks, 0);

            // Start uploading chunks
            uploadChunk(file, 0, totalChunks, fileId);
        }

        // Function to display upload information
        function displayUploadInfo(file, totalChunks, currentChunk) {
            const fileSize = (file.size / (1024 * 1024)).toFixed(2); // Convert to MB
            const chunkSize = (file.size / totalChunks / (1024 * 1024)).toFixed(2); // Average chunk size in MB
            const videoDuration = document.getElementById('video_duration').value;

            statusDiv.innerHTML = `
                <div class="alert alert-info mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                        <strong>Đang tải lên video...</strong>
                    </div>
                    <div class="upload-stats mb-2">
                        <p class="mb-1"><i class="fas fa-file-video me-2"></i> Tệp: ${file.name} (${fileSize} MB)</p>
                        <p class="mb-1"><i class="fas fa-clock me-2"></i> Thời lượng: ${videoDuration} phút</p>
                        <p class="mb-1"><i class="fas fa-puzzle-piece me-2"></i> Tổng số phần: ${totalChunks}</p>
                        <p class="mb-1"><i class="fas fa-spinner me-2"></i> Đang tải phần: <span class="current-part-text">${currentChunk + 1}/${totalChunks}</span></p>
                    </div>
                    <div id="upload-details" class="mb-0">
                        <p class="mb-1">Tiến độ phần hiện tại: <span id="chunk-progress">0%</span></p>
                        <p class="mb-1">Tiến độ tổng thể: <span id="total-progress">0%</span></p>
                        <p class="mb-0">Xin vui lòng không đóng trình duyệt.</p>
                    </div>
                </div>
            `;
        }

        // Function to handle file upload through chunks
        function uploadChunk(file, chunkIndex, totalChunks, fileId) {
            // Calculate the chunk data
            const start = chunkIndex * chunkSize;
            const end = Math.min(start + chunkSize, file.size);
            const chunk = file.slice(start, end);

            // Create form data for this chunk
            const formData = new FormData();
            formData.append('file', chunk);
            formData.append('name', file.name);
            formData.append('chunk', chunkIndex);
            formData.append('chunks', totalChunks);
            formData.append('use_s3', 'true');
            formData.append('file_id', fileId);
            formData.append('video_duration', document.getElementById('video_duration').value);
            formData.append('_token', '{{ csrf_token() }}');

            // Update the main upload info display
            displayUploadInfo(file, totalChunks, chunkIndex);

            // Update progress display - only update the progress bar, not the entire status div
            const percentComplete = Math.round((chunkIndex / totalChunks) * 100);
            progressBarInner.style.width = percentComplete + '%';
            progressBarInner.setAttribute('aria-valuenow', percentComplete);
            progressBarInner.textContent = percentComplete + '%';

            // Update specific elements rather than regenerating the entire content
            updateProgressElements(chunkIndex, totalChunks, 0, percentComplete);

            // Send the chunk
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '{{ route('webinars.upload.post', $webinar) }}', true);

            // Set up event handlers
            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    // This is the progress within the current chunk
                    const chunkPercentComplete = Math.min(Math.round((e.loaded / e.total) * 100), 100);

                    // Calculate overall progress considering current chunk and completed chunks
                    // Make sure progress never exceeds 100%
                    let overallProgress = Math.round(((chunkIndex * chunkSize) + (e.loaded / e.total * chunkSize)) / file.size * 100);
                    overallProgress = Math.min(overallProgress, 100); // Cap at 100%

                    // Update just the progress bar without changing the status message
                    progressBarInner.style.width = overallProgress + '%';
                    progressBarInner.setAttribute('aria-valuenow', overallProgress);
                    progressBarInner.textContent = overallProgress + '%';

                    // Update specific elements instead of regenerating the entire content
                    updateProgressElements(chunkIndex, totalChunks, chunkPercentComplete, overallProgress);
                }
            };

            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);

                        if (response.success) {
                            if (chunkIndex < totalChunks - 1) {
                                // Continue with next chunk
                                console.log(`Chunk ${chunkIndex + 1}/${totalChunks} uploaded successfully`);
                                uploadChunk(file, chunkIndex + 1, totalChunks, fileId);
                            } else {
                                // All chunks completed
                                console.log('All chunks uploaded successfully');

                                // Show S3 upload notification but maintain the same structure
                                // Find the alert container instead of replacing the entire content
                                const alertContainer = statusDiv.querySelector('.alert');
                                if (alertContainer) {
                                    // Update the existing alert instead of creating a new one
                                    alertContainer.className = 'alert alert-primary mb-3';
                                    alertContainer.innerHTML = `
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                            <strong>Đang xử lý video trên máy chủ S3...</strong>
                                        </div>
                                        <div class="upload-stats mb-2">
                                            <p class="mb-1"><i class="fas fa-file-video me-2"></i> Tệp: ${file.name}</p>
                                            <p class="mb-1"><i class="fas fa-clock me-2"></i> Thời lượng: ${document.getElementById('video_duration').value} phút</p>
                                            <p class="mb-1"><i class="fas fa-cloud-upload-alt me-2"></i> Đang đồng bộ với S3</p>
                                        </div>
                                        <div id="upload-details" class="mb-0">
                                            <p class="mb-0">Video đã được tải lên thành công và đang được xử lý trên máy chủ S3. Xin vui lòng đợi trong giây lát...</p>
                                        </div>
                                    `;
                                } else {
                                    // Fallback if the alert container isn't found
                                    statusDiv.innerHTML = `
                                        <div class="alert alert-primary mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                                <strong>Đang xử lý video trên máy chủ S3...</strong>
                                            </div>
                                            <div class="upload-stats mb-2">
                                                <p class="mb-1"><i class="fas fa-file-video me-2"></i> Tệp: ${file.name}</p>
                                                <p class="mb-1"><i class="fas fa-clock me-2"></i> Thời lượng: ${document.getElementById('video_duration').value} phút</p>
                                                <p class="mb-1"><i class="fas fa-cloud-upload-alt me-2"></i> Đang đồng bộ với S3</p>
                                            </div>
                                            <div id="upload-details" class="mb-0">
                                                <p class="mb-0">Video đã được tải lên thành công và đang được xử lý trên máy chủ S3. Xin vui lòng đợi trong giây lát...</p>
                                            </div>
                                        </div>
                                    `;
                                }

                                // After 3 seconds, show completion and redirect
                                setTimeout(function() {
                                    const finalAlertContainer = statusDiv.querySelector('.alert');
                                    if (finalAlertContainer) {
                                        // Update the existing alert instead of creating a new one
                                        finalAlertContainer.className = 'alert alert-success';
                                        finalAlertContainer.innerHTML = `
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <strong>${response.message || 'Tải lên thành công!'}</strong>
                                            </div>
                                            <p class="mb-0">Video đã được lưu trữ thành công trên máy chủ S3.</p>
                                        `;
                                    } else {
                                        // Fallback if the alert container isn't found
                                        statusDiv.innerHTML = `
                                            <div class="alert alert-success">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <strong>${response.message || 'Tải lên thành công!'}</strong>
                                                </div>
                                                <p class="mb-0">Video đã được lưu trữ thành công trên máy chủ S3.</p>
                                            </div>
                                        `;
                                    }

                                    // Redirect after success
                                    setTimeout(function() {
                                        window.location.href = '{{ route("webinars.show", $webinar) }}';
                                    }, 2000);
                                }, 3000);
                            }
                        } else {
                            handleUploadError(response.message || 'Lỗi không xác định');
                        }
                    } catch (e) {
                        handleUploadError('Lỗi xử lý phản hồi: ' + e.message);
                    }
                } else {
                    handleUploadError('Lỗi mạng: ' + xhr.status);
                }
            };

            xhr.onerror = function() {
                handleUploadError('Lỗi kết nối mạng');
            };

            xhr.send(formData);
        }

        // Helper function to update all progress elements without regenerating the entire content
        function updateProgressElements(chunkIndex, totalChunks, chunkPercentComplete, overallProgress) {
            // Get references to the specific elements to update
            const currentPartElement = document.querySelector('.current-part-text');
            const chunkProgressElement = document.getElementById('chunk-progress');
            const totalProgressElement = document.getElementById('total-progress');

            // Update only the text content of specific elements
            if (currentPartElement) {
                currentPartElement.textContent = `Phần ${chunkIndex + 1}/${totalChunks}`;
            }

            if (chunkProgressElement) {
                chunkProgressElement.textContent = `${chunkPercentComplete}%`;
            }

            if (totalProgressElement) {
                totalProgressElement.textContent = `${overallProgress}%`;
            }
        }

        function handleUploadError(errorMessage) {
            console.error('Upload error:', errorMessage);
            statusDiv.innerHTML = `<div class="alert alert-danger">Lỗi khi tải lên: ${errorMessage}</div>`;
            errorLog.classList.remove('d-none');
            errorLog.innerHTML = `<p>Chi tiết lỗi: ${errorMessage}</p>`;
            uploadBtn.disabled = false;
        }
    });
</script>
@endpush
