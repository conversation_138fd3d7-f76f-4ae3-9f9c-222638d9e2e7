@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Chỉnh sửa bộ câu hỏi</h1>
            <p class="mb-0 text-muted">Cập nhật thông tin bộ câu hỏi: {{ $questionSet->name }}</p>
        </div>
        <a href="{{ route('question-sets.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Form -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2 text-primary"></i>Thông tin bộ câu hỏi
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('question-sets.update', $questionSet) }}" method="POST" id="questionSetForm">
                        @csrf
                        @method('PUT')

                        <!-- Question Set Name -->
                        <div class="mb-4">
                            <label for="name" class="form-label fw-semibold">
                                Tên bộ câu hỏi <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name', $questionSet->name) }}" required
                                   placeholder="Nhập tên bộ câu hỏi...">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Tên để nhận diện bộ câu hỏi này</div>
                        </div>

                        <!-- Webinar Selection -->
                        <div class="mb-4">
                            <label for="webinar_id" class="form-label fw-semibold">
                                Webinar <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('webinar_id') is-invalid @enderror"
                                    id="webinar_id" name="webinar_id" required>
                                <option value="">-- Chọn webinar --</option>
                                @foreach($webinars as $webinar)
                                    <option value="{{ $webinar->id }}"
                                            {{ old('webinar_id', $questionSet->webinar_id) == $webinar->id ? 'selected' : '' }}>
                                        {{ $webinar->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('webinar_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Chọn webinar mà bộ câu hỏi này sẽ được sử dụng</div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                Mô tả (tùy chọn)
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Mô tả ngắn về bộ câu hỏi này...">{{ old('description', $questionSet->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="status" name="status"
                                       {{ old('status', $questionSet->status) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="status">
                                    Kích hoạt bộ câu hỏi
                                </label>
                            </div>
                            <div class="form-text">Bộ câu hỏi sẽ có thể sử dụng ngay sau khi cập nhật</div>
                        </div>

                        <hr class="my-4">

                        <!-- Questions Section -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2 text-primary"></i>Danh sách câu hỏi
                                </h5>
                                <div class="text-muted small">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Kéo xuống cuối để thêm câu hỏi mới
                                </div>
                            </div>

                            <div id="questions-container">
                                <!-- Questions will be added here -->
                            </div>

                            @error('questions')
                                <div class="text-danger mt-2">{{ $message }}</div>
                            @endif
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 pt-3 border-top">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>Hủy
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Cập nhật bộ câu hỏi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4 sticky-sidebar">
            <!-- Help Panel -->
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Hướng dẫn
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Các loại câu hỏi:</h6>
                    <div class="list-group list-group-flush">
                        <div class="list-group-item px-0 border-0">
                            <span class="badge bg-primary me-2">📝</span>
                            <strong>Trắc nghiệm 1 đáp án:</strong> Chọn một đáp án đúng
                        </div>
                        <div class="list-group-item px-0 border-0">
                            <span class="badge bg-primary me-2">📝</span>
                            <strong>Trắc nghiệm nhiều đáp án:</strong> Chọn nhiều đáp án đúng
                        </div>
                        <div class="list-group-item px-0 border-0">
                            <span class="badge bg-warning me-2">⭐</span>
                            <strong>Đánh giá:</strong> Thang điểm từ 1-5 sao
                        </div>
                        <div class="list-group-item px-0 border-0">
                            <span class="badge bg-dark me-2">✍️</span>
                            <strong>Tự luận:</strong> Câu hỏi mở, trả lời bằng văn bản
                        </div>
                    </div>

                    <hr>

                    <h6 class="text-primary">Lưu ý quan trọng:</h6>
                    <ul class="small text-muted">
                        <li>Mỗi bộ câu hỏi cần ít nhất 1 câu hỏi</li>
                        <li>Mỗi câu hỏi cần ít nhất 1 đáp án</li>
                        <li>Có thể thêm/xóa câu hỏi tùy ý</li>
                        <li>Đáp án sẽ tự động tạo theo loại câu hỏi</li>
                    </ul>
                </div>
            </div>

            <!-- Current Status -->
            <div class="card mt-3 shadow-sm">
                <div class="card-header bg-gradient-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Thống kê hiện tại
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="mb-0 text-primary">{{ $questionSet->questions->count() }}</h4>
                                <small class="text-muted">Tổng câu hỏi</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="mb-0 text-success">{{ $questionSet->questions->where('status', true)->count() }}</h4>
                            <small class="text-muted">Đã kích hoạt</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Overview Panel -->
            <div class="card shadow-sm mt-3" id="questions-overview">
                <div class="card-header bg-gradient-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-list-check me-2"></i>Danh sách câu hỏi đang chỉnh sửa
                        <span class="badge bg-light text-dark ms-2" id="questions-count">0</span>
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div id="questions-summary" class="questions-summary">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-question-circle fa-2x mb-2 opacity-50"></i>
                            <p class="small mb-0">Chưa có câu hỏi nào</p>
                            <p class="small">Thêm câu hỏi để xem danh sách</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.question-card {
    border: 2px solid #e3f2fd;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    position: relative;
    overflow: hidden;
}

.question-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    border-color: #007bff;
}

.question-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.question-number {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.question-body {
    padding: 1.5rem 1.25rem;
}

.answer-item {
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
}

.answer-item:hover {
    background: linear-gradient(145deg, #e9ecef, #f8f9fa);
    border-color: #007bff;
    transform: translateX(2px);
}

.answer-item:last-child {
    margin-bottom: 0;
}

.answer-label {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.85rem;
    flex-shrink: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.btn-remove-answer {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-delete-question {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.add-question-area {
    text-align: center;
    padding: 2rem;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    margin-top: 1rem;
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    transition: all 0.3s ease;
}

.add-question-area:hover {
    border-color: #007bff;
    background: linear-gradient(145deg, #e3f2fd, #f8f9fa);
}

.fade-in {
    animation: fadeIn 0.4s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
}

.questions-summary {
    max-height: 300px;
    overflow-y: auto;
}

.question-summary-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.question-summary-item:last-child {
    border-bottom: none;
}

.question-summary-item:hover {
    background-color: #f8f9fa;
}

.question-summary-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.question-summary-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-style: italic;
}

.question-summary-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
}

.question-type-badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
}

.answers-count {
    color: #6c757d;
    font-weight: 500;
}

/* Sticky Sidebar */
.sticky-sidebar {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.sticky-sidebar .card {
    margin-bottom: 1rem;
}

.sticky-sidebar .questions-summary {
    max-height: calc(50vh - 100px);
    overflow-y: auto;
}

/* Custom scrollbar for better UX */
.questions-summary::-webkit-scrollbar {
    width: 6px;
}

.questions-summary::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.questions-summary::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.questions-summary::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
@endpush

@push('scripts')
<script>
let questionIndex = 0;

// Load existing questions on page load
document.addEventListener('DOMContentLoaded', function() {
    // Get existing questions or old input data
    const existingQuestions = {!! json_encode(old('questions', $questionSet->questions->map(function($question) {
        $answers = [];
        if ($question->type !== 'open-survey' && is_array($question->answers)) {
            foreach ($question->answers as $answer) {
                if (is_array($answer) && isset($answer['label'])) {
                    $answers[] = $answer['label'];
                } else {
                    $answers[] = $answer;
                }
            }
        }
        return [
            'title' => $question->title,
            'description' => $question->description,
            'type' => $question->type,
            'answers' => $answers,
            'status' => $question->status
        ];
    }))) !!};

    if (existingQuestions.length > 0) {
        existingQuestions.forEach((question, index) => {
            addQuestion(question);
        });
    } else {
        addQuestion(); // Add default first question if empty
    }

    // Add the initial "Add Question" area
    updateAddQuestionArea();

    // Initial overview update
    setTimeout(updateQuestionsOverview, 500);
});

function addQuestion(questionData = null) {
    const container = document.getElementById('questions-container');
    const index = questionIndex++;

    // Calculate the actual question number for display only
    const currentQuestionCount = document.querySelectorAll('.question-card').length + 1;

    const questionHtml = `
        <div class="question-card fade-in" id="question-${index}" data-question-index="${index}">
            <div class="question-header">
                <div class="question-number">Câu hỏi #${currentQuestionCount}</div>
                <button type="button" class="btn btn-outline-danger btn-delete-question" onclick="removeQuestion(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="question-body">
                <!-- Question Title -->
                <div class="mb-4">
                    <label class="form-label fw-semibold">
                        Tiêu đề câu hỏi <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control" name="questions[${index}][title]" rows="2" required
                              placeholder="Nhập nội dung câu hỏi..."
                              onchange="updateQuestionsOverview()">${questionData?.title || ''}</textarea>
                </div>

                <!-- Question Description -->
                <div class="mb-4">
                    <label class="form-label fw-semibold">
                        Mô tả câu hỏi (tùy chọn)
                    </label>
                    <textarea class="form-control" name="questions[${index}][description]" rows="2"
                              placeholder="Thêm mô tả, hướng dẫn hoặc ghi chú cho câu hỏi này..."
                              onchange="updateQuestionsOverview()">${questionData?.description || ''}</textarea>
                    <div class="form-text">Mô tả sẽ hiển thị dưới tiêu đề câu hỏi để hỗ trợ người tham gia</div>
                </div>

                <!-- Question Type -->
                <div class="mb-4">
                    <label class="form-label fw-semibold">
                        Loại câu hỏi <span class="text-danger">*</span>
                    </label>
                    <select class="form-select" name="questions[${index}][type]" required onchange="updateAnswerSection(${index}); updateQuestionsOverview()">
                        <option value="">-- Chọn loại câu hỏi --</option>
                        <option value="single-choice" ${questionData?.type === 'single-choice' ? 'selected' : ''}>📝 Trắc nghiệm 1 đáp án</option>
                        <option value="multiple-choice" ${questionData?.type === 'multiple-choice' ? 'selected' : ''}>📝 Trắc nghiệm nhiều đáp án</option>
                        <option value="rating" ${questionData?.type === 'rating' ? 'selected' : ''}>⭐ Đánh giá thang điểm</option>
                        <option value="open-survey" ${questionData?.type === 'open-survey' ? 'selected' : ''}>✍️ Tự luận</option>
                    </select>
                </div>

                <!-- Answers Section -->
                <div class="mb-3" id="answers-section-${index}" style="display: none;">
                    <label class="form-label fw-semibold">
                        Đáp án <span class="text-danger">*</span>
                    </label>
                    <div id="answers-container-${index}">
                        <!-- Answers will be generated here -->
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary mt-3" onclick="addAnswer(${index})">
                        <i class="fas fa-plus me-1"></i>Thêm đáp án
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remove existing add area
    const existingAddArea = document.querySelector('.add-question-area');
    if (existingAddArea) {
        existingAddArea.remove();
    }

    container.insertAdjacentHTML('beforeend', questionHtml);

    // Initialize answers if questionData exists
    if (questionData && questionData.type) {
        setTimeout(() => {
            updateAnswerSection(index);
            // Clear auto-generated answers and add existing ones
            const answersContainer = document.getElementById(`answers-container-${index}`);
            answersContainer.innerHTML = '';

            if (questionData.answers && questionData.answers.length > 0) {
                questionData.answers.forEach(answer => {
                    addAnswer(index, answer);
                });
            }
        }, 100);
    }

    // Add new "Add Question" area
    updateAddQuestionArea();

    // Update questions overview
    updateQuestionsOverview();
}

function removeQuestion(index) {
    if (document.querySelectorAll('.question-card').length <= 1) {
        alert('Bộ câu hỏi phải có ít nhất một câu hỏi!');
        return;
    }

    document.getElementById(`question-${index}`).remove();
    updateQuestionNumbers();
    updateAddQuestionArea();

    // Update questions overview
    updateQuestionsOverview();
}

function updateQuestionNumbers() {
    const questions = document.querySelectorAll('.question-card');
    questions.forEach((card, index) => {
        const numberElement = card.querySelector('.question-number');
        numberElement.textContent = `Câu hỏi #${index + 1}`;

        // Get the original question index from data attribute
        const originalIndex = card.getAttribute('data-question-index');

        // Update only the display names for form fields, keep original indices for processing
        const titleTextarea = card.querySelector('textarea[name*="[title]"]');
        const typeSelect = card.querySelector('select[name*="[type]"]');
        const answerInputs = card.querySelectorAll('input[name*="[answers]"]');

        // Keep the original indices in field names to avoid validation issues
        if (titleTextarea && !titleTextarea.name.includes(`questions[${originalIndex}]`)) {
            titleTextarea.name = `questions[${originalIndex}][title]`;
        }
        if (typeSelect && !typeSelect.name.includes(`questions[${originalIndex}]`)) {
            typeSelect.name = `questions[${originalIndex}][type]`;
        }
        answerInputs.forEach(input => {
            if (!input.name.includes(`questions[${originalIndex}]`)) {
                input.name = `questions[${originalIndex}][answers][]`;
            }
        });
    });
}

function updateAddQuestionArea() {
    const container = document.getElementById('questions-container');

    // Remove existing add area
    const existingAddArea = document.querySelector('.add-question-area');
    if (existingAddArea) {
        existingAddArea.remove();
    }

    // Add new "Add Question" area
    const addAreaHtml = `
        <div class="add-question-area">
            <div class="mb-3">
                <i class="fas fa-plus-circle fa-2x text-primary mb-2"></i>
                <h6 class="text-primary mb-2">Thêm câu hỏi mới</h6>
                <p class="text-muted small mb-3">Nhấn nút bên dưới để thêm câu hỏi vào bộ câu hỏi này</p>
            </div>
            <button type="button" class="btn btn-primary px-4" onclick="addQuestion()">
                <i class="fas fa-plus me-2"></i>Thêm câu hỏi
            </button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', addAreaHtml);
}

function updateAnswerSection(questionIndex) {
    const typeSelect = document.querySelector(`select[name="questions[${questionIndex}][type]"]`);
    const answersSection = document.getElementById(`answers-section-${questionIndex}`);
    const answersContainer = document.getElementById(`answers-container-${questionIndex}`);

    const type = typeSelect.value;

    if (!type) {
        answersSection.style.display = 'none';
        return;
    }

    answersSection.style.display = 'block';
    answersContainer.innerHTML = '';

    // Auto-generate answers for specific types only if no existing data
    if (type === 'true-false') {
        addAnswer(questionIndex, 'Đúng');
        addAnswer(questionIndex, 'Sai');
    } else if (type === 'rating') {
        for (let i = 1; i <= 5; i++) {
            addAnswer(questionIndex, `${i} sao`);
        }
    } else if (type === 'quick-poll') {
        addAnswer(questionIndex, 'Có');
        addAnswer(questionIndex, 'Không');
    } else if (type === 'open-survey') {
        // Open survey questions don't need predefined answers
        answersSection.style.display = 'none';
    } else {
        // For multiple-choice and poll, add two default answers
        addAnswer(questionIndex, '');
        addAnswer(questionIndex, '');
    }
}

function addAnswer(questionIndex, answerText = '') {
    const container = document.getElementById(`answers-container-${questionIndex}`);
    const answerIndex = container.children.length;

    const answerHtml = `
        <div class="answer-item" id="answer-${questionIndex}-${answerIndex}">
            <div class="answer-label">${String.fromCharCode(65 + answerIndex)}</div>
            <input type="text" class="form-control" name="questions[${questionIndex}][answers][]"
                   value="${answerText}" placeholder="Nhập đáp án..." required
                   onchange="updateQuestionsOverview()">
            <button type="button" class="btn btn-sm btn-outline-danger btn-remove-answer"
                    onclick="removeAnswer(${questionIndex}, ${answerIndex})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', answerHtml);
    updateQuestionsOverview();
}

function removeAnswer(questionIndex, answerIndex) {
    const container = document.getElementById(`answers-container-${questionIndex}`);
    if (container.children.length <= 1) {
        alert('Mỗi câu hỏi phải có ít nhất một đáp án!');
        return;
    }

    document.getElementById(`answer-${questionIndex}-${answerIndex}`).remove();
    updateAnswerLabels(questionIndex);
    updateQuestionsOverview();
}

function updateAnswerLabels(questionIndex) {
    const container = document.getElementById(`answers-container-${questionIndex}`);
    Array.from(container.children).forEach((answer, index) => {
        const label = answer.querySelector('.answer-label');
        label.textContent = String.fromCharCode(65 + index);

        // Update answer input names but keep question index
        const input = answer.querySelector('input');
        input.name = `questions[${questionIndex}][answers][]`;
    });
}

// Remove client-side validation that conflicts with server validation
document.getElementById('questionSetForm').addEventListener('submit', function(e) {
    // Let the server handle validation
    // Remove client-side validation to avoid conflicts
    console.log('Form submitted - server will handle validation');
});

function updateQuestionsOverview() {
    const questions = document.querySelectorAll('.question-card');
    const count = questions.length;
    const summary = document.getElementById('questions-summary');
    const countBadge = document.getElementById('questions-count');

    countBadge.textContent = count;

    if (count === 0) {
        summary.innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-question-circle fa-2x mb-2 opacity-50"></i>
                <p class="small mb-0">Chưa có câu hỏi nào</p>
                <p class="small">Thêm câu hỏi để xem danh sách</p>
            </div>
        `;
        return;
    }

    let summaryHtml = '';
    questions.forEach((question, index) => {
        const titleTextarea = question.querySelector('textarea[name*="[title]"]');
        const descriptionTextarea = question.querySelector('textarea[name*="[description]"]');
        const typeSelect = question.querySelector('select[name*="[type]"]');
        const answerInputs = question.querySelectorAll('input[name*="[answers]"]');

        const title = titleTextarea?.value.trim() || 'Chưa có tiêu đề';
        const description = descriptionTextarea?.value.trim() || '';
        const type = typeSelect?.value || '';
        const answersCount = answerInputs?.length || 0;

        const typeLabels = {
            'single-choice': { text: 'Trắc nghiệm 1 đáp án', color: 'primary', icon: '📝' },
            'multiple-choice': { text: 'Trắc nghiệm nhiều đáp án', color: 'primary', icon: '📝' },
            'rating': { text: 'Đánh giá', color: 'warning', icon: '⭐' },
            'open-survey': { text: 'Tự luận', color: 'dark', icon: '✍️' }
        };

        const typeInfo = typeLabels[type] || { text: 'Chưa chọn', color: 'light', icon: '❓' };

        summaryHtml += `
            <div class="question-summary-item" onclick="scrollToQuestion(${index})">
                <div class="question-summary-title">${title}</div>
                ${description ? `<div class="question-summary-description">${description}</div>` : ''}
                <div class="question-summary-meta">
                    <span class="question-type-badge badge bg-${typeInfo.color}">
                        ${typeInfo.icon} ${typeInfo.text}
                    </span>
                    <span class="answers-count">${answersCount} đáp án</span>
                </div>
            </div>
        `;
    });

    summary.innerHTML = summaryHtml;
}

function scrollToQuestion(index) {
    const questionCards = document.querySelectorAll('.question-card');
    if (questionCards[index]) {
        questionCards[index].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Add highlight effect
        questionCards[index].style.borderColor = '#007bff';
        questionCards[index].style.boxShadow = '0 0 0 3px rgba(0,123,255,0.25)';

        setTimeout(() => {
            questionCards[index].style.borderColor = '';
            questionCards[index].style.boxShadow = '';
        }, 2000);
    }
}
</script>
@endpush
