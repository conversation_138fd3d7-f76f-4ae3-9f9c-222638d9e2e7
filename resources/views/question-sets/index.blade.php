@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Quản lý bộ câu hỏi</h1>
            <p class="mb-0 text-muted">Quản lý các bộ câu hỏi tương tác cho webinar</p>
        </div>
        <div class="btn-group">
            <a href="{{ route('question-sets.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Tạo bộ câu hỏi mới
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i><PERSON><PERSON> lọc
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('question-sets.index') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <input type="text" class="form-select" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Tìm theo tên bộ câu hỏi...">
                    </div>
                    <div class="col-md-3">
                        <label for="webinar_id" class="form-label">Webinar</label>
                        <select class="form-select" id="webinar_id" name="webinar_id">
                            <option value="">Tất cả webinar</option>
                            @foreach($webinars as $webinar)
                                <option value="{{ $webinar->id }}" {{ request('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                    {{ $webinar->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Trạng thái</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Tất cả</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Kích hoạt</option>
                            <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Tạm dừng</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Tìm kiếm
                        </button>
                        <a href="{{ route('question-sets.index') }}" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-refresh me-2"></i>Đặt lại
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Question Sets Table -->
    <div class="card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-list me-2"></i>Danh sách bộ câu hỏi ({{ $questionSets->total() }} bộ)
            </h6>
        </div>
        <div class="card-body p-0">
            @if($questionSets->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Tên bộ câu hỏi</th>
                                <th>Webinar</th>
                                <th>Số câu hỏi</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($questionSets as $questionSet)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ $questionSet->name }}</h6>
                                                @if($questionSet->description)
                                                    <small class="text-muted">{{ Str::limit($questionSet->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $questionSet->webinar->title }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $questionSet->total_questions }} câu</span>
                                        @if($questionSet->active_questions_count > 0)
                                            <br><small class="text-success">{{ $questionSet->active_questions_count }} đang hoạt động</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" 
                                                   type="checkbox" 
                                                   data-id="{{ $questionSet->id }}"
                                                   {{ $questionSet->status ? 'checked' : '' }}>
                                            <label class="form-check-label">
                                                <span class="badge bg-{{ $questionSet->status_color }}">
                                                    {{ $questionSet->status_label }}
                                                </span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $questionSet->created_at->format('d/m/Y H:i') }}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('question-sets.show', $questionSet) }}" 
                                               class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('question-sets.edit', $questionSet) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('question-sets.destroy', $questionSet) }}" 
                                                  method="POST" class="d-inline"
                                                  onsubmit="return confirm('Bạn có chắc chắn muốn xóa bộ câu hỏi này?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="card-footer bg-white">
                    {{ $questionSets->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Chưa có bộ câu hỏi nào</h5>
                    <p class="text-muted">Hãy tạo bộ câu hỏi đầu tiên cho webinar của bạn</p>
                    <a href="{{ route('question-sets.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tạo bộ câu hỏi mới
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Status toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const statusToggles = document.querySelectorAll('.status-toggle');
    
    statusToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const questionSetId = this.dataset.id;
            const isChecked = this.checked;
            
            fetch(`/question-sets/${questionSetId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update badge
                    const badge = this.parentElement.querySelector('.badge');
                    badge.className = `badge bg-${data.status ? 'success' : 'secondary'}`;
                    badge.textContent = data.status ? 'Kích hoạt' : 'Tạm dừng';
                    
                    // Show success message
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công!',
                            text: data.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                } else {
                    // Revert toggle state
                    this.checked = !isChecked;
                    
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: data.message
                        });
                    }
                }
            })
            .catch(error => {
                // Revert toggle state
                this.checked = !isChecked;
                console.error('Error:', error);
                
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: 'Có lỗi xảy ra khi cập nhật trạng thái'
                    });
                }
            });
        });
    });
});
</script>
@endpush 