@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Chi tiết bộ câu hỏi</h1>
            <p class="mb-0 text-muted">{{ $questionSet->name }}</p>
        </div>
        <div class="btn-group">
            <a href="{{ route('question-sets.edit', $questionSet) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Chỉnh sửa
            </a>
            <a href="{{ route('question-sets.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Question Set Info -->
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin bộ câu hỏi
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 30%" class="text-muted">Tên:</th>
                                    <td><strong>{{ $questionSet->name }}</strong></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Webinar:</th>
                                    <td>
                                        <span class="badge bg-info">{{ $questionSet->webinar->title }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Mô tả:</th>
                                    <td>{{ $questionSet->description ?: 'Không có mô tả' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 30%" class="text-muted">Được tạo:</th>
                                    <td>{{ $questionSet->created_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Cập nhật cuối:</th>
                                    <td>{{ $questionSet->updated_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Trạng thái:</th>
                                    <td>
                                        <span class="badge bg-{{ $questionSet->status_color }}">
                                            {{ $questionSet->status_label }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions List -->
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Danh sách câu hỏi ({{ $questionSet->questions->count() }} câu)
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if($questionSet->questions->count() > 0)
                        @foreach($questionSet->questions as $index => $question)
                            <div class="question-item {{ !$loop->last ? 'border-bottom' : '' }}">
                                <div class="p-4">
                                    <!-- Question Header -->
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-secondary me-2">{{ $index + 1 }}</span>
                                            <h6 class="mb-0">{{ $question->title }}</h6>
                                        </div>
                                        <div class="d-flex align-items-center gap-2">
                                            @php
                                                $typeLabels = [
                                                    'single-choice' => '📝 Trắc nghiệm 1 đáp án',
                                                    'multiple-choice' => '📝 Trắc nghiệm nhiều đáp án',
                                                    'rating' => '⭐ Đánh giá',
                                                    'open-survey' => '✍ Tự luận',
                                                ];
                                            @endphp
                                            <span class="badge bg-primary">{{ $typeLabels[$question->type] ?? $question->type }}</span>
                                            <span class="badge bg-{{ $question->status ? 'success' : 'secondary' }}">
                                                {{ $question->status ? 'Kích hoạt' : 'Tạm dừng' }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Question Answers -->
                                    <div class="answers-preview">
                                        @if($question->type === 'rating')
                                            <div class="rating-preview d-flex align-items-center gap-1">
                                                <span class="text-muted small me-2">Thang đánh giá:</span>
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="fas fa-star text-warning"></i>
                                                @endfor
                                                <span class="ms-2 small text-muted">(1-5 sao)</span>
                                            </div>
                                        @else
                                            <div class="row">
                                                @foreach($question->answers as $answerIndex => $answer)
                                                    <div class="col-md-6 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input"
                                                                   type="{{ $question->type === 'poll' ? 'checkbox' : 'radio' }}"
                                                                   name="answer_preview_{{ $question->id }}" disabled>
                                                            <label class="form-check-label">
                                                                <strong>{{ chr(65 + $answerIndex) }}.</strong> {{ $answer }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Question Stats (if has responses) -->
                                    @if($question->responses->count() > 0)
                                        <div class="mt-3 pt-3 border-top">
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="text-primary">
                                                        <strong>{{ $question->responses->count() }}</strong>
                                                        <br><small class="text-muted">Phản hồi</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-success">
                                                        <strong>{{ $question->responses->where('created_at', '>=', today())->count() }}</strong>
                                                        <br><small class="text-muted">Hôm nay</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-info">
                                                        <strong>{{ $question->responses->where('created_at', '>=', now()->subHour())->count() }}</strong>
                                                        <br><small class="text-muted">1 giờ qua</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có câu hỏi nào</h5>
                            <p class="text-muted">Hãy thêm câu hỏi vào bộ này</p>
                            <a href="{{ route('question-sets.edit', $questionSet) }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Thêm câu hỏi
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Statistics -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Thống kê
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h3 class="mb-0 text-primary">{{ $questionSet->questions->count() }}</h3>
                                <small class="text-muted">Tổng câu hỏi</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="mb-0 text-success">{{ $questionSet->questions->where('status', true)->count() }}</h3>
                            <small class="text-muted">Đang hoạt động</small>
                        </div>
                    </div>

                    <hr>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="mb-0 text-info">{{ $questionSet->questions->sum(function($q) { return $q->responses->count(); }) }}</h4>
                                <small class="text-muted">Tổng phản hồi</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="mb-0 text-warning">{{ $questionSet->questions->sum(function($q) { return $q->responses->where('created_at', '>=', today())->count(); }) }}</h4>
                            <small class="text-muted">Phản hồi hôm nay</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Types -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>Phân loại câu hỏi
                    </h6>
                </div>
                <div class="card-body">
                    @php
                        $typeStats = $questionSet->questions->groupBy('type');
                        $typeLabels = [
                            'single-choice' => ['name' => 'Trắc nghiệm 1 đáp án', 'icon' => '📝', 'color' => 'primary'],
                            'multiple-choice' => ['name' => 'Trắc nghiệm nhiều đáp án', 'icon' => '📝', 'color' => 'primary'],
                            'rating' => ['name' => 'Đánh giá', 'icon' => '⭐', 'color' => 'warning'],
                            'open-survey' => ['name' => 'Tự luận', 'icon' => '✍', 'color' => 'dark'],

                        ];
                    @endphp

                    @if($typeStats->count() > 0)
                        @foreach($typeStats as $type => $questions)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">{{ $typeLabels[$type]['icon'] ?? '❓' }}</span>
                                    <span>{{ $typeLabels[$type]['name'] ?? $type }}</span>
                                </div>
                                <span class="badge bg-{{ $typeLabels[$type]['color'] ?? 'secondary' }}">
                                    {{ $questions->count() }} câu
                                </span>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted small">Chưa có câu hỏi nào</p>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Thao tác
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('question-sets.edit', $questionSet) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa bộ câu hỏi
                        </a>

                        <button type="button" class="btn {{ $questionSet->status ? 'btn-warning' : 'btn-success' }}"
                                onclick="toggleStatus()">
                            <i class="fas {{ $questionSet->status ? 'fa-pause' : 'fa-play' }} me-2"></i>
                            {{ $questionSet->status ? 'Tạm dừng' : 'Kích hoạt' }}
                        </button>

                        @if($questionSet->questions->sum(function($q) { return $q->responses->count(); }) === 0)
                            <form action="{{ route('question-sets.destroy', $questionSet) }}" method="POST"
                                  onsubmit="return confirm('Bạn có chắc chắn muốn xóa bộ câu hỏi này?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash me-2"></i>Xóa bộ câu hỏi
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.question-item {
    transition: background-color 0.2s;
}

.question-item:hover {
    background-color: #f8f9fa;
}

.answers-preview .form-check {
    padding: 8px 15px;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.answers-preview .form-check:hover {
    background-color: #e9ecef;
}

.rating-preview {
    padding: 8px 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.card-header {
    border-bottom: 1px solid #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
function toggleStatus() {
    fetch(`/question-sets/{{ $questionSet->id }}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message and reload page
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công!',
                    text: data.message,
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            } else {
                alert(data.message);
                location.reload();
            }
        } else {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: data.message
                });
            } else {
                alert(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Có lỗi xảy ra khi cập nhật trạng thái'
            });
        } else {
            alert('Có lỗi xảy ra khi cập nhật trạng thái');
        }
    });
}
</script>
@endpush
