@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Quản lý câu hỏi</h1>
            <p class="mb-0 text-muted">Quản lý câu hỏi tương tác cho các webinar</p>
        </div>
        <div class="btn-group">
            <a href="{{ route('questions.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Tạo câu hỏi mới
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i><PERSON><PERSON> lọc
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('questions.index') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Tìm kiếm</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Tìm theo tiêu đề câu hỏi..." value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="webinar_id" class="form-label">Webinar</label>
                    <select class="form-select" id="webinar_id" name="webinar_id">
                        <option value="">Tất cả webinar</option>
                        @foreach($webinars as $webinar)
                            <option value="{{ $webinar->id }}" {{ request('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                {{ $webinar->title }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">Loại câu hỏi</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">Tất cả loại</option>
                        <option value="multiple-choice" {{ request('type') == 'multiple-choice' ? 'selected' : '' }}>Trắc nghiệm</option>
                        <option value="true-false" {{ request('type') == 'true-false' ? 'selected' : '' }}>Đúng/Sai</option>
                        <option value="poll" {{ request('type') == 'poll' ? 'selected' : '' }}>Khảo sát</option>
                        <option value="rating" {{ request('type') == 'rating' ? 'selected' : '' }}>Đánh giá</option>
                        <option value="quick-poll" {{ request('type') == 'quick-poll' ? 'selected' : '' }}>Khảo sát nhanh</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Trạng thái</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Tất cả</option>
                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Kích hoạt</option>
                        <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Tạm dừng</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Tìm kiếm
                    </button>
                    <a href="{{ route('questions.index') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-refresh me-2"></i>Đặt lại
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Questions Table -->
    <div class="card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-list me-2"></i>Danh sách câu hỏi ({{ $questions->total() }} câu hỏi)
            </h6>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success btn-sm" onclick="toggleAllQuestions(true)">
                    <i class="fas fa-toggle-on me-1"></i>Kích hoạt tất cả
                </button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleAllQuestions(false)">
                    <i class="fas fa-toggle-off me-1"></i>Tạm dừng tất cả
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if($questions->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 50px;">#</th>
                                <th>Tiêu đề câu hỏi</th>
                                <th>Webinar</th>
                                <th>Loại</th>
                                <th>Số lựa chọn</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th style="width: 150px;">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($questions as $question)
                                <tr id="question-{{ $question->id }}">
                                    <td>{{ $question->id }}</td>
                                    <td>
                                        <div class="fw-semibold">{{ $question->title }}</div>
                                        @if(strlen($question->title) > 100)
                                            <small class="text-muted">{{ Str::limit($question->title, 100) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($question->webinar)
                                            <span class="badge bg-info">{{ $question->webinar->title }}</span>
                                        @else
                                            <span class="text-muted">Không có</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($question->type)
                                            @case('multiple-choice')
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-list-ul me-1"></i>Trắc nghiệm
                                                </span>
                                                @break
                                            @case('true-false')
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-check-double me-1"></i>Đúng/Sai
                                                </span>
                                                @break
                                            @case('poll')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-poll me-1"></i>Khảo sát
                                                </span>
                                                @break
                                            @case('rating')
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-star me-1"></i>Đánh giá
                                                </span>
                                                @break
                                            @case('quick-poll')
                                                <span class="badge bg-info">
                                                    <i class="fas fa-poll-h me-1"></i>Khảo sát nhanh
                                                </span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ count($question->answers) }} lựa chọn
                                        </span>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   data-id="{{ $question->id }}" 
                                                   {{ $question->status ? 'checked' : '' }}>
                                            <label class="form-check-label">
                                                <span class="status-text">
                                                    {{ $question->status ? 'Kích hoạt' : 'Tạm dừng' }}
                                                </span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $question->created_at->format('d/m/Y H:i') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('questions.show', $question) }}" 
                                               class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('questions.edit', $question) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger btn-delete" 
                                                    data-id="{{ $question->id }}" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted">
                        Hiển thị {{ $questions->firstItem() ?? 0 }} đến {{ $questions->lastItem() ?? 0 }} 
                        trong tổng số {{ $questions->total() }} câu hỏi
                    </div>
                    <div>
                        {{ $questions->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                    <h5>Chưa có câu hỏi nào</h5>
                    <p class="text-muted">Hãy tạo câu hỏi đầu tiên cho webinar của bạn</p>
                    <a href="{{ route('questions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tạo câu hỏi mới
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa câu hỏi này?</p>
                <p class="text-muted small">Hành động này không thể hoàn tác.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.badge {
    font-size: 0.875em;
}

.form-switch {
    display: flex;
    align-items: center;
}

.form-switch .form-check-input {
    margin-right: 0.5rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
}

.table td {
    vertical-align: middle;
}

.status-toggle:checked + label .status-text {
    color: #198754;
    font-weight: 500;
}

.status-toggle:not(:checked) + label .status-text {
    color: #6c757d;
}

.question-preview {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Toggle status
    $('.status-toggle').change(function() {
        const questionId = $(this).data('id');
        const status = $(this).is(':checked') ? 1 : 0;
        
        $.ajax({
            url: `/questions/${questionId}/toggle-status`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                status: status
            },
            success: function(response) {
                const statusText = status ? 'Kích hoạt' : 'Tạm dừng';
                $(`#question-${questionId} .status-text`).text(statusText);
                
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công!',
                    text: `Đã ${statusText.toLowerCase()} câu hỏi`,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            },
            error: function() {
                // Revert the toggle
                $(`input[data-id="${questionId}"]`).prop('checked', !status);
                
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: 'Không thể cập nhật trạng thái',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        });
    });

    // Delete question
    $('.btn-delete').click(function() {
        const questionId = $(this).data('id');
        $('#deleteForm').attr('action', `/questions/${questionId}`);
        $('#deleteModal').modal('show');
    });

    // Success/Error messages
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'Thành công!',
            text: '{{ session('success') }}',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    @endif

    @if(session('error'))
        Swal.fire({
            icon: 'error',
            title: 'Lỗi!',
            text: '{{ session('error') }}',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    @endif
});

// Toggle all questions
function toggleAllQuestions(status) {
    const action = status ? 'kích hoạt' : 'tạm dừng';
    
    Swal.fire({
        title: `${status ? 'Kích hoạt' : 'Tạm dừng'} tất cả câu hỏi?`,
        text: `Bạn có chắc muốn ${action} tất cả câu hỏi?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: status ? '#198754' : '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Đồng ý',
        cancelButtonText: 'Hủy'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/questions/toggle-all-status',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    status: status ? 1 : 0
                },
                success: function(response) {
                    location.reload();
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: 'Không thể cập nhật trạng thái tất cả câu hỏi'
                    });
                }
            });
        }
    });
}
</script>
@endpush
