@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Tạo câu hỏi mới</h1>
            <p class="mb-0 text-muted">Tạo câu hỏi tương tác cho webinar</p>
        </div>
        <a href="{{ route('question-sets.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Form -->
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Thông tin câu hỏi
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('questions.store') }}" method="POST" id="questionForm">
                        @csrf

                        <!-- Webinar Selection -->
                        <div class="mb-4">
                            <label for="webinar_id" class="form-label fw-semibold">
                                Webinar <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('webinar_id') is-invalid @enderror" 
                                    id="webinar_id" name="webinar_id" required>
                                <option value="">-- Chọn webinar --</option>
                                @foreach($webinars as $webinar)
                                    <option value="{{ $webinar->id }}" 
                                            {{ old('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                        {{ $webinar->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('webinar_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Chọn webinar mà câu hỏi này sẽ được sử dụng</div>
                        </div>

                        <!-- Question Title -->
                        <div class="mb-4">
                            <label for="title" class="form-label fw-semibold">
                                Tiêu đề câu hỏi <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control @error('title') is-invalid @enderror" 
                                      id="title" name="title" rows="3" required 
                                      placeholder="Nhập nội dung câu hỏi...">{{ old('title') }}</textarea>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Nội dung câu hỏi sẽ hiển thị cho học sinh</div>
                        </div>

                        <!-- Question Type -->
                        <div class="mb-4">
                            <label for="type" class="form-label fw-semibold">
                                Loại câu hỏi <span class="text-danger">*</span>
                            </label>
                            <div class="row">
                                <div class="col-md-6">
                                    <select class="form-select @error('type') is-invalid @enderror" 
                                            id="type" name="type" required onchange="updateAnswersSection()">
                                        <option value="">-- Chọn loại câu hỏi --</option>
                                        <option value="multiple-choice" {{ old('type') == 'multiple-choice' ? 'selected' : '' }}>
                                            📝 Trắc nghiệm (Multiple Choice)
                                        </option>
                                        <option value="true-false" {{ old('type') == 'true-false' ? 'selected' : '' }}>
                                            ✅ Đúng/Sai (True/False)
                                        </option>
                                        <option value="poll" {{ old('type') == 'poll' ? 'selected' : '' }}>
                                            📊 Khảo sát ý kiến (Poll)
                                        </option>
                                        <option value="rating" {{ old('type') == 'rating' ? 'selected' : '' }}>
                                            ⭐ Đánh giá thang điểm (Rating)
                                        </option>
                                        <option value="quick-poll" {{ old('type') == 'quick-poll' ? 'selected' : '' }}>
                                            👍 Bình chọn nhanh (Quick Poll)
                                        </option>
                                        <option value="essay" {{ old('type') == 'essay' ? 'selected' : '' }}>
                                            ✍️ Tự luận (Essay)
                                        </option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="form-text" id="typeDescription">Chọn loại câu hỏi phù hợp với mục đích sử dụng</div>
                        </div>

                        <!-- Answers Section -->
                        <div class="mb-4" id="answersSection" style="display: none;">
                            <label class="form-label fw-semibold">
                                Lựa chọn trả lời <span class="text-danger">*</span>
                            </label>
                            <div id="answersContainer">
                                <!-- Dynamic answers will be generated here -->
                            </div>
                            @error('answers')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            @error('answers.*')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="status" name="status" 
                                       {{ old('status') ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="status">
                                    Kích hoạt câu hỏi
                                </label>
                            </div>
                            <div class="form-text">Câu hỏi sẽ có thể sử dụng ngay sau khi tạo</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>Hủy
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Tạo câu hỏi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Help Panel -->
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Hướng dẫn
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">📝 Trắc nghiệm</h6>
                        <p class="small mb-0">Nhiều lựa chọn, học sinh chọn 1 đáp án đúng</p>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <h6 class="text-success">✅ Đúng/Sai</h6>
                        <p class="small mb-0">Câu hỏi có 2 lựa chọn: Đúng hoặc Sai</p>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <h6 class="text-info">📊 Khảo sát</h6>
                        <p class="small mb-0">Thu thập ý kiến với nhiều lựa chọn</p>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <h6 class="text-warning">⭐ Đánh giá</h6>
                        <p class="small mb-0">Thang điểm từ 1-5 sao</p>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <h6 class="text-secondary">👍 Bình chọn nhanh</h6>
                        <p class="small mb-0">Câu hỏi Yes/No hoặc Like/Dislike</p>
                    </div>
                    <hr>
                    <div class="mb-0">
                        <h6 class="text-dark">✍️ Tự luận</h6>
                        <p class="small mb-0">Câu hỏi mở, học sinh trả lời bằng văn bản</p>
                    </div>
                </div>
            </div>

            <!-- Preview Panel -->
            <div class="card mt-3" id="previewPanel" style="display: none;">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>Xem trước
                    </h6>
                </div>
                <div class="card-body" id="previewContent">
                    <!-- Preview will be generated here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.answer-input-group {
    margin-bottom: 10px;
}

.answer-input-group .input-group {
    margin-bottom: 8px;
}

.answer-input-group .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}

.btn-remove-answer {
    border-radius: 0 0.375rem 0.375rem 0;
}

.preview-question {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 15px;
}

.preview-answers .form-check {
    margin-bottom: 8px;
}

.question-type-badge {
    font-size: 0.875em;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
@endpush

@push('scripts')
<script>
let answerCount = 0;

function updateAnswersSection() {
    const type = document.getElementById('type').value;
    const answersSection = document.getElementById('answersSection');
    const answersContainer = document.getElementById('answersContainer');
    const typeDescription = document.getElementById('typeDescription');
    
    // Clear previous answers
    answersContainer.innerHTML = '';
    answerCount = 0;
    
    if (!type) {
        answersSection.style.display = 'none';
        updatePreview();
        return;
    }
    
    answersSection.style.display = 'block';
    
    // Update description
    const descriptions = {
        'multiple-choice': 'Thêm các lựa chọn cho câu hỏi trắc nghiệm (tối thiểu 2 lựa chọn)',
        'true-false': 'Câu hỏi Đúng/Sai sẽ tự động có 2 lựa chọn: "Đúng" và "Sai"',
        'poll': 'Thêm các lựa chọn cho cuộc khảo sát (tối thiểu 2 lựa chọn)',
        'rating': 'Thang đánh giá từ 1-5 sao sẽ được tự động tạo',
        'quick-poll': 'Thêm các lựa chọn cho bình chọn nhanh (thường là 2 lựa chọn: Yes/No, Like/Dislike)',
        'essay': 'Câu hỏi tự luận, học sinh sẽ trả lời bằng văn bản tự do'
    };
    
    typeDescription.textContent = descriptions[type] || 'Chọn loại câu hỏi phù hợp với mục đích sử dụng';
    
    // Generate answers based on type
    switch(type) {
        case 'true-false':
            addFixedAnswers(['Đúng', 'Sai']);
            break;
        case 'rating':
            addRatingAnswers();
            break;
        case 'quick-poll':
            addFixedAnswers(['Có', 'Không']);
            break;
        case 'essay':
            // Essay questions don't need predefined answers
            answersSection.style.display = 'none';
            break;
        case 'multiple-choice':
        case 'poll':
            addDynamicAnswers(2); // Start with 2 answers
            break;
    }
    
    updatePreview();
}

function addFixedAnswers(answers) {
    answers.forEach((answer, index) => {
        const html = `
            <div class="answer-input-group fade-in">
                <div class="input-group">
                    <span class="input-group-text">${String.fromCharCode(65 + index)}</span>
                    <input type="text" class="form-control" name="answers[]" 
                           value="${answer}" readonly>
                </div>
            </div>
        `;
        document.getElementById('answersContainer').insertAdjacentHTML('beforeend', html);
        answerCount++;
    });
}

function addRatingAnswers() {
    for(let i = 1; i <= 5; i++) {
        const html = `
            <div class="answer-input-group fade-in">
                <div class="input-group">
                    <span class="input-group-text">${'⭐'.repeat(i)}</span>
                    <input type="text" class="form-control" name="answers[]" 
                           value="${i} sao" readonly>
                </div>
            </div>
        `;
        document.getElementById('answersContainer').insertAdjacentHTML('beforeend', html);
        answerCount++;
    }
}

function addDynamicAnswers(count) {
    for(let i = 0; i < count; i++) {
        addAnswer();
    }
    
    // Add button to add more answers
    const addButtonHtml = `
        <div class="mt-2">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addAnswer()">
                <i class="fas fa-plus me-1"></i>Thêm lựa chọn
            </button>
        </div>
    `;
    document.getElementById('answersContainer').insertAdjacentHTML('beforeend', addButtonHtml);
}

function addAnswer() {
    const oldValue = event && event.target ? '' : (answerCount < 4 ? ['A. Lựa chọn A', 'B. Lựa chọn B', 'C. Lựa chọn C', 'D. Lựa chọn D'][answerCount] || '' : '');
    
    const html = `
        <div class="answer-input-group fade-in">
            <div class="input-group">
                <span class="input-group-text">${String.fromCharCode(65 + answerCount)}</span>
                <input type="text" class="form-control" name="answers[]" 
                       placeholder="Nhập lựa chọn ${String.fromCharCode(65 + answerCount)}..." 
                       value="${oldValue}" onchange="updatePreview()">
                <button type="button" class="btn btn-outline-danger btn-remove-answer" 
                        onclick="removeAnswer(this)" ${answerCount < 2 ? 'disabled' : ''}>
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    // Insert before the add button
    const addButton = document.querySelector('#answersContainer .mt-2');
    if (addButton) {
        addButton.insertAdjacentHTML('beforebegin', html);
    } else {
        document.getElementById('answersContainer').insertAdjacentHTML('beforeend', html);
    }
    
    answerCount++;
    updateAnswerLabels();
    updatePreview();
}

function removeAnswer(button) {
    const answerGroup = button.closest('.answer-input-group');
    answerGroup.remove();
    answerCount--;
    updateAnswerLabels();
    updatePreview();
    
    // Disable remove buttons if only 2 answers left
    const removeButtons = document.querySelectorAll('.btn-remove-answer');
    removeButtons.forEach(btn => {
        btn.disabled = answerCount <= 2;
    });
}

function updateAnswerLabels() {
    const answerGroups = document.querySelectorAll('.answer-input-group');
    answerGroups.forEach((group, index) => {
        const label = group.querySelector('.input-group-text');
        const input = group.querySelector('input[name="answers[]"]');
        if (label && !input.readOnly) {
            label.textContent = String.fromCharCode(65 + index);
            input.placeholder = `Nhập lựa chọn ${String.fromCharCode(65 + index)}...`;
        }
    });
}

function updatePreview() {
    const title = document.getElementById('title').value;
    const type = document.getElementById('type').value;
    const answers = Array.from(document.querySelectorAll('input[name="answers[]"]')).map(input => input.value).filter(val => val.trim());
    
    const previewPanel = document.getElementById('previewPanel');
    const previewContent = document.getElementById('previewContent');
    
    if (!title || !type) {
        previewPanel.style.display = 'none';
        return;
    }
    
    previewPanel.style.display = 'block';
    
    const typeLabels = {
        'multiple-choice': '📝 Trắc nghiệm',
        'true-false': '✅ Đúng/Sai',
        'poll': '📊 Khảo sát',
        'rating': '⭐ Đánh giá',
        'quick-poll': '👍 Bình chọn nhanh',
        'essay': '✍️ Tự luận'
    };
    
    let answersHtml = '';
    if (type === 'rating') {
        answersHtml = `<div class="text-center">
            <div class="btn-group" role="group">
                ${answers.map((_, i) => `<button type="button" class="btn btn-outline-warning">⭐</button>`).join('')}
            </div>
        </div>`;
    } else if (type === 'essay') {
        answersHtml = `<div class="form-group">
            <textarea class="form-control" rows="4" placeholder="Học sinh sẽ nhập câu trả lời tại đây..." disabled></textarea>
        </div>`;
    } else {
        answersHtml = answers.map((answer, i) => `
            <div class="form-check">
                <input class="form-check-input" type="${type === 'poll' ? 'checkbox' : 'radio'}" disabled>
                <label class="form-check-label">${answer}</label>
            </div>
        `).join('');
    }
    
    previewContent.innerHTML = `
        <div class="preview-question">
            <div class="mb-2">
                <span class="badge bg-primary question-type-badge">${typeLabels[type]}</span>
            </div>
            <h6 class="mb-3">${title}</h6>
            <div class="preview-answers">
                ${answersHtml}
            </div>
        </div>
    `;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Restore old values if validation failed
    const oldType = '{{ old("type") }}';
    if (oldType) {
        updateAnswersSection();
        
        // Restore old answer values
        const oldAnswers = {!! json_encode(old('answers', [])) !!};
        if (oldAnswers.length > 0) {
            const answerInputs = document.querySelectorAll('input[name="answers[]"]');
            oldAnswers.forEach((answer, index) => {
                if (answerInputs[index]) {
                    answerInputs[index].value = answer;
                }
            });
        }
    }
    
    // Add event listeners
    document.getElementById('title').addEventListener('input', updatePreview);
});

// Form validation
document.getElementById('questionForm').addEventListener('submit', function(e) {
    const type = document.getElementById('type').value;
    const answers = Array.from(document.querySelectorAll('input[name="answers[]"]')).map(input => input.value.trim()).filter(val => val);
    
    if (!type) {
        e.preventDefault();
        alert('Vui lòng chọn loại câu hỏi!');
        return;
    }
    
    // Essay questions don't need answers validation
    if (type !== 'essay') {
        if (answers.length < 1) {
            e.preventDefault();
            alert('Vui lòng thêm ít nhất 1 lựa chọn!');
            return;
        }
        
        if ((type === 'multiple-choice' || type === 'poll') && answers.length < 2) {
            e.preventDefault();
            alert('Loại câu hỏi này cần ít nhất 2 lựa chọn!');
            return;
        }
    }
});
</script>
@endpush
