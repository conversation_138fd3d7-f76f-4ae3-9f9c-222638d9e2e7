@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Chi tiết câu hỏi #{{ $question->id }}</h1>
            <p class="mb-0 text-muted">Xem thông tin chi tiết và thống kê phản hồi</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('questions.edit', $question) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Chỉnh sửa
            </a>
            <a href="{{ route('question-sets.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Question Info -->
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Thông tin câu hỏi
                        <span class="badge {{ $question->status ? 'bg-success' : 'bg-secondary' }} ms-2">
                            {{ $question->status ? 'Đang hoạt động' : 'Tạm dừng' }}
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 30%" class="text-muted">Webinar:</th>
                                    <td>
                                        <a href="{{ route('webinars.show', $question->webinar) }}" class="text-decoration-none">
                                            {{ $question->webinar->title }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Loại câu hỏi:</th>
                                    <td>
                                        @switch($question->type)
                                            @case('multiple-choice')
                                                <span class="badge bg-primary">📝 Trắc nghiệm</span>
                                                @break
                                            @case('true-false')
                                                <span class="badge bg-success">✅ Đúng/Sai</span>
                                                @break
                                            @case('poll')
                                                <span class="badge bg-info">📊 Khảo sát</span>
                                                @break
                                            @case('rating')
                                                <span class="badge bg-warning">⭐ Đánh giá</span>
                                                @break
                                            @case('quick-poll')
                                                <span class="badge bg-secondary">👍 Bình chọn nhanh</span>
                                                @break
                                        @endswitch
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Số phản hồi:</th>
                                    <td>
                                        <span class="h5 mb-0 text-success">{{ $question->responses()->count() }}</span>
                                        <small class="text-muted">người đã trả lời</small>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 30%" class="text-muted">Được tạo:</th>
                                    <td>{{ $question->created_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Cập nhật cuối:</th>
                                    <td>{{ $question->updated_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Trạng thái:</th>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" 
                                                   {{ $question->status ? 'checked' : '' }} disabled>
                                            <label class="form-check-label">
                                                {{ $question->status ? 'Kích hoạt' : 'Tạm dừng' }}
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Content -->
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Nội dung câu hỏi
                    </h5>
                </div>
                <div class="card-body">
                    <div class="question-preview p-4 bg-light rounded">
                        <h4 class="mb-4">{{ $question->title }}</h4>
                        
                        @if($question->type === 'rating')
                            <div class="text-center">
                                <div class="btn-group" role="group">
                                    @foreach($question->answers as $index => $answer)
                                        <button type="button" class="btn btn-outline-warning">
                                            {{ str_repeat('⭐', $index + 1) }}
                                        </button>
                                    @endforeach
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">Thang đánh giá từ 1-5 sao</small>
                                </div>
                            </div>
                        @else
                            <div class="answers-list">
                                @foreach($question->answers as $index => $answer)
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="{{ $question->type === 'poll' ? 'checkbox' : 'radio' }}" 
                                               name="answer_preview" disabled>
                                        <label class="form-check-label">
                                            <strong>{{ chr(65 + $index) }}.</strong> {{ $answer }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Response Statistics -->
            @if($question->responses()->count() > 0)
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Thống kê phản hồi
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($question->answers as $index => $answer)
                                @php
                                    $responseCount = $question->responses()->where('answer', $answer)->count();
                                    $percentage = $question->responses()->count() > 0 
                                        ? round(($responseCount / $question->responses()->count()) * 100, 1) 
                                        : 0;
                                @endphp
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span><strong>{{ chr(65 + $index) }}.</strong> {{ $answer }}</span>
                                        <span class="text-muted">{{ $responseCount }} ({{ $percentage }}%)</span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-primary" style="width: {{ $percentage }}%"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Chưa có phản hồi nào</h5>
                        <p class="text-muted">Câu hỏi này chưa nhận được phản hồi từ người dùng.</p>
                    </div>
                </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Thống kê nhanh
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h3 mb-0 text-primary">{{ $question->id }}</div>
                                <small class="text-muted">ID Câu hỏi</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h3 mb-0 text-success">{{ $question->responses()->count() }}</div>
                                <small class="text-muted">Phản hồi</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h3 mb-0 text-info">{{ count($question->answers) }}</div>
                                <small class="text-muted">Lựa chọn</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h3 mb-0 {{ $question->status ? 'text-success' : 'text-secondary' }}">
                                    <i class="fas {{ $question->status ? 'fa-check-circle' : 'fa-pause-circle' }}"></i>
                                </div>
                                <small class="text-muted">Trạng thái</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Thao tác
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('questions.edit', $question) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa câu hỏi
                        </a>
                        
                        <form action="{{ route('questions.toggle-status', $question) }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn {{ $question->status ? 'btn-warning' : 'btn-success' }} w-100">
                                <i class="fas {{ $question->status ? 'fa-pause' : 'fa-play' }} me-2"></i>
                                {{ $question->status ? 'Tạm dừng' : 'Kích hoạt' }}
                            </button>
                        </form>

                        @if($question->responses()->count() === 0)
                            <form action="{{ route('questions.destroy', $question) }}" method="POST" 
                                  onsubmit="return confirm('Bạn có chắc chắn muốn xóa câu hỏi này?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash me-2"></i>Xóa câu hỏi
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-outline-danger w-100" disabled 
                                    title="Không thể xóa câu hỏi đã có phản hồi">
                                <i class="fas fa-lock me-2"></i>Không thể xóa
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Related Info -->
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin liên quan
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Webinar:</strong><br>
                        <a href="{{ route('webinars.show', $question->webinar) }}" class="text-decoration-none">
                            {{ $question->webinar->title }}
                        </a>
                    </div>
                    
                    @if($question->webinar->questions()->count() > 1)
                        <div class="mb-3">
                            <strong>Câu hỏi khác trong webinar này:</strong><br>
                            <small class="text-muted">
                                {{ $question->webinar->questions()->count() - 1 }} câu hỏi khác
                            </small>
                        </div>
                    @endif

                    <div class="small text-muted">
                        <strong>Lưu ý:</strong> 
                        @if($question->responses()->count() > 0)
                            Câu hỏi đã có phản hồi, một số thay đổi sẽ bị hạn chế.
                        @else
                            Bạn có thể chỉnh sửa tự do vì chưa có phản hồi nào.
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.question-preview {
    border: 2px dashed #dee2e6;
    background: #f8f9fa !important;
}

.answers-list .form-check {
    padding: 8px 15px;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.answers-list .form-check:hover {
    background-color: #e9ecef;
}

.progress {
    border-radius: 4px;
}

.card-header {
    border-bottom: 1px solid #dee2e6;
}
</style>
@endpush
