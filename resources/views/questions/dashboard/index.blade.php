@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header v<PERSON>i thông tin webinar -->
    <div class="dashboard-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1">
                    <i class="fas fa-video me-2 text-primary"></i>
                    Meeting: {{ $webinar->title }}
                </h2>
                <p class="text-muted mb-0">
                    <i class="fas fa-calendar me-1"></i>
                    {{ $webinar->start_time ? $webinar->start_time->format('d/m/Y H:i') : 'Chưa có lịch' }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="live-indicator">
                    <span class="badge badge-live">
                        <i class="fas fa-circle me-1"></i>LIVE
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card bg-gradient-primary">
                <div class="stats-content">
                    <h3 class="stats-number" id="unique-participants">{{ $stats['unique_participants'] ?? 0 }}</h3>
                    <p class="stats-label">Học sinh tham gia</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-success">
                <div class="stats-content">
                    <h3 class="stats-number" id="total-responses">{{ $stats['total_responses'] ?? 0 }}</h3>
                    <p class="stats-label">Phản hồi</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-comments"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-info">
                <div class="stats-content">
                    <h3 class="stats-number" id="active-questions">{{ $stats['active_questions'] ?? 0 }}</h3>
                    <p class="stats-label">Câu hỏi đang hoạt động</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-warning">
                <div class="stats-content">
                    <h3 class="stats-number" id="response-rate">{{ $stats['response_rate'] ?? 0 }}%</h3>
                    <p class="stats-label">Tỷ lệ phản hồi</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo các loại câu hỏi -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Demo Các Loại Câu Hỏi
                    </h5>
                    <p class="text-muted small mb-0">Chọn loại câu hỏi để xem giao diện thống kê tương ứng:</p>
                </div>
                <div class="card-body">
                    <div class="question-type-selector mb-3">
                        <label class="form-label">Chọn loại câu hỏi:</label>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="questionType" id="multiple-choice" value="multiple-choice" checked>
                            <label class="btn btn-outline-primary" for="multiple-choice">
                                <i class="fas fa-list-ul me-1"></i>Trắc nghiệm
                            </label>
                            <input type="radio" class="btn-check" name="questionType" id="rating" value="rating">
                            <label class="btn btn-outline-warning" for="rating">
                                <i class="fas fa-star me-1"></i>Đánh giá thang điểm
                            </label>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Chế độ Demo:</strong> Dữ liệu được mô phỏng để minh họa giao diện thống kê real-time cho từng loại câu hỏi.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Luồng phản hồi real-time -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-stream me-2"></i>Luồng Phản Hồi Real-time
                    </h6>
                    <div class="update-indicator">
                        <span class="badge bg-success">
                            <i class="fas fa-circle me-1"></i>Đang cập nhật
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div id="response-stream" class="response-stream">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>Đang chờ phản hồi...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kết quả real-time -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Kết Quả Real-time
                    </h6>
                    <div class="card-tools">
                        <button class="btn btn-sm btn-outline-secondary" id="auto-update-toggle">
                            <i class="fas fa-sync-alt me-1"></i>Cập nhật tự động
                        </button>
                        <button class="btn btn-sm btn-outline-primary" id="export-results">
                            <i class="fas fa-download me-1"></i>Xuất kết quả
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="realtime-results">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-pie fa-2x mb-2"></i>
                            <p>Chọn câu hỏi để xem kết quả</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê câu hỏi hiện tại -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Thống Kê Câu Hỏi Hiện Tại
                    </h6>
                </div>
                <div class="card-body">
                    @if($activeQuestions->count() > 0)
                        <div class="row">
                            @foreach($activeQuestions as $question)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="question-stats-card" data-question-id="{{ $question->id }}">
                                        <div class="question-header">
                                            <h6 class="question-title">{{ Str::limit($question->title, 60) }}</h6>
                                            <span class="question-type-badge badge bg-{{ $question->type === 'multiple-choice' ? 'primary' : ($question->type === 'true-false' ? 'secondary' : ($question->type === 'poll' ? 'success' : ($question->type === 'rating' ? 'warning' : 'info'))) }}">
                                                {{ ucfirst($question->type) }}
                                            </span>
                                        </div>
                                        <div class="question-stats">
                                            <div class="stat-item">
                                                <span class="stat-value">{{ $question->total_responses }}</span>
                                                <span class="stat-label">Phản hồi</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-value">{{ $question->today_responses }}</span>
                                                <span class="stat-label">Hôm nay</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-value realtime-count">{{ $question->real_time_responses }}</span>
                                                <span class="stat-label">Real-time</span>
                                            </div>
                                        </div>
                                        <div class="question-actions">
                                            <button class="btn btn-sm btn-primary view-detail" data-question-id="{{ $question->id }}">
                                                <i class="fas fa-eye me-1"></i>Xem chi tiết
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                            <h5>Chưa có câu hỏi active nào!</h5>
                            <p class="text-muted">Hãy kích hoạt câu hỏi từ trang quản lý để bắt đầu thu thập phản hồi.</p>
                            <a href="{{ route('questions.index') }}" class="btn btn-primary">
                                <i class="fas fa-cog me-1"></i>Quản lý câu hỏi
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Webinar Selector Modal -->
    <div class="modal fade" id="webinarSelectorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chọn Webinar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('questions.dashboard') }}" method="GET">
                        <div class="mb-3">
                            <label class="form-label">Webinar</label>
                            <select class="form-select" name="webinar_id" required>
                                @foreach($webinars as $webinarItem)
                                    <option value="{{ $webinarItem->id }}" {{ $webinarItem->id == $webinar->id ? 'selected' : '' }}>
                                        {{ $webinarItem->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                            <button type="submit" class="btn btn-primary">Chọn</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Demo Response Modal -->
<div class="modal fade" id="demoResponseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Demo Phản Hồi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Đây là demo để tạo phản hồi mẫu cho testing</p>
                <form id="demo-response-form">
                    <div class="mb-3">
                        <label class="form-label">Câu hỏi</label>
                        <select class="form-select" id="demo-question-id" required>
                            @foreach($activeQuestions as $question)
                                <option value="{{ $question->id }}">{{ $question->title }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tên người trả lời</label>
                        <input type="text" class="form-control" id="demo-user-name" placeholder="Nhập tên...">
                    </div>
                    <div id="demo-answers">
                        <!-- Dynamic content based on question type -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="submit-demo-response">Gửi phản hồi</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.live-indicator .badge-live {
    background: #dc3545;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    color: white;
    margin-bottom: 1rem;
}

.bg-gradient-primary { background: linear-gradient(45deg, #007bff, #0056b3); }
.bg-gradient-success { background: linear-gradient(45deg, #28a745, #1e7e34); }
.bg-gradient-info { background: linear-gradient(45deg, #17a2b8, #117a8b); }
.bg-gradient-warning { background: linear-gradient(45deg, #ffc107, #e0a800); }

.stats-content {
    position: relative;
    z-index: 2;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
    line-height: 1;
}

.stats-label {
    margin: 0.5rem 0 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.stats-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 3rem;
    opacity: 0.3;
}

/* Question Type Selector */
.question-type-selector .btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

/* Response Stream */
.response-stream {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background: #f8f9fa;
}

.response-item {
    background: white;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    border-left: 3px solid #007bff;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.response-meta {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.response-content {
    font-weight: 500;
}

/* Question Stats Cards */
.question-stats-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s;
    height: 100%;
}

.question-stats-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.question-header {
    margin-bottom: 1rem;
}

.question-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.question-type-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.question-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
}

.question-actions {
    text-align: center;
}

/* Update Indicator */
.update-indicator .badge {
    animation: pulse 2s infinite;
}

/* Results Chart */
.chart-container {
    position: relative;
    height: 300px;
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
        text-align: center;
    }

    .stats-number {
        font-size: 2rem;
    }

    .question-type-selector .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .question-type-selector .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let autoUpdateInterval;
let latestTimestamp = null;

$(document).ready(function() {
    // Initialize
    startAutoUpdate();
    loadDemoQuestion();

    // Question type selector
    $('input[name="questionType"]').change(function() {
        loadDemoResults($(this).val());
    });

    // Auto update toggle
    $('#auto-update-toggle').click(function() {
        if (autoUpdateInterval) {
            stopAutoUpdate();
            $(this).html('<i class="fas fa-play me-1"></i>Bắt đầu cập nhật');
        } else {
            startAutoUpdate();
            $(this).html('<i class="fas fa-pause me-1"></i>Dừng cập nhật');
        }
    });

    // View question detail
    $('.view-detail').click(function() {
        const questionId = $(this).data('question-id');
        window.open(`/questions/${questionId}/dashboard`, '_blank');
    });

    // Demo response
    $('#demo-question-id').change(function() {
        loadDemoQuestion();
    });

    $('#submit-demo-response').click(function() {
        submitDemoResponse();
    });
});

function startAutoUpdate() {
    updateStats();
    updateResponseStream();

    autoUpdateInterval = setInterval(function() {
        updateStats();
        updateResponseStream();
    }, 3000); // Update every 3 seconds

    $('#auto-update-toggle').html('<i class="fas fa-pause me-1"></i>Dừng cập nhật');
}

function stopAutoUpdate() {
    if (autoUpdateInterval) {
        clearInterval(autoUpdateInterval);
        autoUpdateInterval = null;
    }
    $('#auto-update-toggle').html('<i class="fas fa-play me-1"></i>Bắt đầu cập nhật');
}

function updateStats() {
    $.get('/api/questions/stats', {
        webinar_id: {{ $webinar->id }}
    }, function(data) {
        $('#unique-participants').text(data.unique_participants || 0);
        $('#total-responses').text(data.total_responses || 0);
        $('#active-questions').text(data.active_questions || 0);
        $('#response-rate').text((data.response_rate || 0) + '%');

        // Update question cards
        $('.question-stats-card').each(function() {
            const questionId = $(this).data('question-id');
            $(this).find('.realtime-count').text(Math.floor(Math.random() * 10)); // Demo data
        });
    });
}

function updateResponseStream() {
    $.get('/api/questions/responses', {
        webinar_id: {{ $webinar->id }},
        since: latestTimestamp
    }, function(data) {
        if (data.responses && data.responses.length > 0) {
            const $stream = $('#response-stream');

            data.responses.forEach(function(response) {
                const $item = $('<div class="response-item">' +
                    '<div class="response-meta">' +
                        '<i class="fas fa-user me-1"></i>' + (response.user_name || 'Anonymous') +
                        '<span class="ms-2"><i class="fas fa-clock me-1"></i>' + formatTime(response.responded_at) + '</span>' +
                    '</div>' +
                    '<div class="response-content">' +
                        '<strong>' + response.question.title + '</strong><br>' +
                        '<span class="text-primary">' + formatAnswer(response) + '</span>' +
                    '</div>' +
                '</div>');

                $stream.prepend($item);

                // Keep only last 20 items
                $stream.find('.response-item:gt(19)').remove();
            });

            latestTimestamp = data.latest_timestamp;
        }
    });
}

function loadDemoResults(questionType) {
    // Generate demo chart based on question type
    const $container = $('#realtime-results');

    let chartHtml = '<canvas id="results-chart" width="400" height="300"></canvas>';
    $container.html(chartHtml);

    // Generate demo data
    const demoData = generateDemoData(questionType);
    createChart(demoData, questionType);
}

function generateDemoData(questionType) {
    switch(questionType) {
        case 'multiple-choice':
            return {
                labels: ['Rất tốt', 'Tốt', 'Trung bình', 'Kém'],
                data: [45, 30, 20, 5],
                colors: ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
            };
        case 'true-false':
            return {
                labels: ['Đúng', 'Sai'],
                data: [70, 30],
                colors: ['#28a745', '#dc3545']
            };
        case 'poll':
            return {
                labels: ['Đỏ', 'Xanh', 'Vàng', 'Tím'],
                data: [25, 35, 20, 20],
                colors: ['#dc3545', '#007bff', '#ffc107', '#6f42c1']
            };
        case 'rating':
            return {
                labels: ['1⭐', '2⭐', '3⭐', '4⭐', '5⭐'],
                data: [5, 10, 20, 35, 30],
                colors: ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#28a745']
            };
        case 'quick-poll':
            return {
                labels: ['Có', 'Không', 'Cần suy nghĩ'],
                data: [60, 25, 15],
                colors: ['#28a745', '#dc3545', '#6c757d']
            };
    }
}

function createChart(data, type) {
    const ctx = document.getElementById('results-chart').getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.data,
                backgroundColor: data.colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function loadDemoQuestion() {
    // Load answers for selected question (demo)
    const questionType = $('input[name="questionType"]:checked').val();
    const $container = $('#demo-answers');

    let html = '';

    switch(questionType) {
        case 'multiple-choice':
        case 'true-false':
        case 'poll':
        case 'quick-poll':
            html = '<label class="form-label">Chọn đáp án</label>';
            const answers = questionType === 'true-false' ? ['Đúng', 'Sai'] : ['Đáp án A', 'Đáp án B', 'Đáp án C', 'Đáp án D'];
            answers.forEach((answer, index) => {
                html += `<div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${index}" id="answer${index}">
                    <label class="form-check-label" for="answer${index}">${answer}</label>
                </div>`;
            });
            break;
        case 'rating':
            html = '<label class="form-label">Đánh giá (1-5 sao)</label>';
            html += '<div class="rating-stars">';
            for(let i = 1; i <= 5; i++) {
                html += `<span class="star" data-rating="${i}">⭐</span>`;
            }
            html += '</div>';
            break;
    }

    $container.html(html);
}

function submitDemoResponse() {
    // Demo function to create sample responses
    const questionId = $('#demo-question-id').val();
    const userName = $('#demo-user-name').val() || 'Demo User';

    $.post('/api/questions/responses', {
        question_id: questionId,
        user_name: userName,
        selected_answers: [Math.floor(Math.random() * 4)],
        _token: '{{ csrf_token() }}'
    }, function(response) {
        $('#demoResponseModal').modal('hide');
        toastr.success('Demo response submitted!');
    });
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('vi-VN');
}

function formatAnswer(response) {
    if (response.rating) {
        return response.rating + ' sao';
    }
    if (response.selected_answers && response.selected_answers.length > 0) {
        return 'Đáp án: ' + response.selected_answers.join(', ');
    }
    return response.answer_text || 'Không có câu trả lời';
}

// Initialize demo data on load
loadDemoResults('multiple-choice');
</script>
@endpush
