@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Qu<PERSON>n <PERSON></h5>
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm Người Dùng
            </a>
        </div>
        <div class="card-body">
            @if($users->isEmpty())
                <div class="alert alert-info">
                    Chưa có người dùng nào. <a href="{{ route('admin.users.create') }}">Tạo người dùng đầu tiên</a>.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>Tên</th>
                            <th>Email</th>
                            <th><PERSON>ai <PERSON></th>
                            <th><PERSON><PERSON>ớ<PERSON> H<PERSON>n Webinar</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON></th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($users as $user)
                            <tr>
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    @foreach($user->roles as $role)
                                        @if($role->name=="administrator")
                                            <span class="badge bg-danger p-2 mb-2">Quản trị viên cấp cao</span>
                                        @elseif($role->name=="admin")
                                            <span class="badge bg-primary p-2 mb-2">Quản trị viên</span>
                                        @else
                                            <span class="badge bg-secondary p-2 mb-2">Telesale</span>
                                        @endif
                                    @endforeach
                                </td>
                                <td>{{ $user->webinar_limit }}</td>
                                <td>{{ $user->created_at->format('d/m/Y') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-info"
                                           title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-primary"
                                           title="Sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.users.destroy', $user) }}" method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa người dùng này?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger"
                                                    {{ $user->id === auth()->id() ? 'disabled' : '' }} title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-center mt-4">
                    {{ $users->links() }}
                </div>
            @endif
        </div>
    </div>
@endsection
