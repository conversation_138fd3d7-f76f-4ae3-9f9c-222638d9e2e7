@extends('layouts.app')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Thông Tin Người Dùng</h5>
        <div>
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh Sửa
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay Lại
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4>{{ $user->name }}</h4>
                <p class="text-muted">{{ $user->email }}</p>
                
                <div class="mb-4">
                    <h5>Vai Trò</h5>
                    @foreach($user->roles as $role)
                    <!-- làm check dạng switch(role) -->
                        @switch($role->name)
                            @case('admin')
                                <span class="badge bg-danger p-2 mb-2">Quản trị viên</span>
                                @break
                            @case('user')
                                <span class="badge bg-primary p-2 mb-2">Khách hàng</span>
                                @break
                            @case('staff')
                                <span class="badge bg-secondary p-2 mb-2">Nhân viên</span>
                                @break
                        @endswitch
                    @endforeach
                </div>
                
                <div class="mb-4">
                    <h5>Giới Hạn Webinar</h5>
                    <p>
                        @if($user->webinar_limit > 0)
                            <span class="badge bg-info p-2">{{ $user->webinar_limit }} webinar</span>
                        @else
                            <span class="badge bg-success p-2">Không giới hạn</span>
                        @endif
                    </p>
                </div>
                
                <div class="mb-4">
                    <h5>Thông Tin Tài Khoản</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Mã Người Dùng</th>
                                <td>{{ $user->id }}</td>
                            </tr>
                            <tr>
                                <th>Ngày Tạo</th>
                                <td>{{ $user->created_at->format('d/m/Y H:i:s') }}</td>
                            </tr>
                            <tr>
                                <th>Cập Nhật Lần Cuối</th>
                                <td>{{ $user->updated_at->format('d/m/Y H:i:s') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Thống Kê Webinar</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>Tổng Số Webinar</div>
                            <span class="badge bg-primary p-2">{{ $user->webinars->count() }}</span>
                        </div>
                        
                        @if($user->webinar_limit > 0)
                            <div class="progress mb-2">
                                @php
                                    $usedPercentage = min(100, ($user->webinars->count() / $user->webinar_limit) * 100);
                                @endphp
                                <div class="progress-bar {{ $usedPercentage > 80 ? 'bg-danger' : 'bg-success' }}" role="progressbar" style="width: {{ $usedPercentage }}%" aria-valuenow="{{ $usedPercentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="text-center text-muted small">
                                {{ $user->webinars->count() }} / {{ $user->webinar_limit }} webinar đã sử dụng
                            </div>
                        @endif
                        
                        <hr>
                        
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> Chỉnh Sửa Người Dùng
                            </a>
                            
                            @if($user->id !== auth()->id())
                                <form action="{{ route('admin.users.destroy', $user) }}" method="POST" onsubmit="return confirm('Bạn có chắc chắn muốn xóa người dùng này? Hành động này không thể hoàn tác.');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-trash"></i> Xóa Người Dùng
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 