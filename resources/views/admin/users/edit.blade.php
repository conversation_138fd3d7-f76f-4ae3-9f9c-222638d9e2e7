@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Chỉnh <PERSON><PERSON><PERSON>: {{ $user->name }}</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.users.update', $user) }}">
                @csrf
                @method('PUT')

                <div class="mb-3">
                    <label for="name" class="form-label">Họ tên</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                           value="{{ old('name', $user->name) }}" required>
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label"><PERSON><PERSON><PERSON> chỉ Email</label>
                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email"
                           name="email" value="{{ old('email', $user->email) }}" required>
                    @error('email')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <input type="password" class="form-control @error('password') is-invalid @enderror" id="password"
                           name="password">
                    <div class="form-text">Để trống nếu không muốn thay đổi mật khẩu</div>
                    @error('password')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="password-confirm" class="form-label">Xác nhận mật khẩu</label>
                    <input type="password" class="form-control" id="password-confirm" name="password_confirmation">
                </div>

                <div class="mb-3">
                    <label for="role" class="form-label">Vai trò</label>
                    <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                        <option value="">Chọn vai trò</option>
                        @foreach($roles as $role)
                            <option
                                value="{{ $role->name }}" {{ (old('role', $user->roles->first()?->name) == $role->name) ? 'selected' : '' }}>{{ $role->name }}</option>
                        @endforeach
                    </select>
                    @error('role')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
                @can("limit webinars")
                    <div class="mb-3">
                        <label for="webinar_limit" class="form-label">Giới hạn webinar</label>
                        <input type="number" class="form-control @error('webinar_limit') is-invalid @enderror"
                               id="webinar_limit" name="webinar_limit"
                               value="{{ old('webinar_limit', $user->webinar_limit) }}" min="0" required>
                        <div class="form-text">Số lượng webinar tối đa người dùng này có thể tạo (0 là không giới hạn)
                        </div>
                        @error('webinar_limit')
                        <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                        @enderror
                    </div>
                @endcan
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Hủy</a>
                    <button type="submit" class="btn btn-primary">Cập nhật người dùng</button>
                </div>
            </form>
        </div>
    </div>
@endsection
