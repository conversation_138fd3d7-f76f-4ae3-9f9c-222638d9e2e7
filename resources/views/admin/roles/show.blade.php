@extends('layouts.app')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/role-management.css') }}">
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 class="mb-0">Chi tiết vai trò</h3>
    <div>
        <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i> Quay lại
        </a>
        <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Chỉnh sửa
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Thông tin vai trò</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="fw-bold d-block">Tên vai trò:</label>
                    <span>{{ $role->name }}</span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold d-block">Ngày tạo:</label>
                    <span>{{ $role->created_at->format('d/m/Y H:i:s') }}</span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold d-block">Ngày cập nhật:</label>
                    <span>{{ $role->updated_at->format('d/m/Y H:i:s') }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Danh sách quyền</h5>
                <span class="badge bg-info">{{ $role->permissions->count() }}</span>
            </div>
            <div class="card-body">
                @if($role->permissions->isEmpty())
                    <div class="alert alert-info">
                        Vai trò này chưa được cấp quyền nào.
                    </div>
                @else
                    <div class="row g-3">
                        @foreach($role->permissions as $permission)
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-2 bg-light rounded border">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>{{ $permission->name }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection 