@extends('layouts.app')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/role-management.css') }}">
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 class="mb-0">Chỉnh sửa vai trò</h3>
    <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Quay lại
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.roles.update', $role) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">Tên vai trò <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                            id="name" name="name" value="{{ old('name', $role->name) }}" required autofocus
                            {{ $role->name === 'administrator' ? 'readonly' : '' }}>
                        @error('name')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                        @if($role->name === 'administrator')
                            <div class="form-text text-danger">
                                Vai trò administrator là mặc định, không thể chỉnh sửa tên.
                            </div>
                        @else
                            <div class="form-text">
                                Tên vai trò nên là chữ thường không dấu, ví dụ: manager, editor, staff
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <h5 class="border-bottom pb-2 mb-3">Phân quyền</h5>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="p-3 bg-light rounded border">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Chọn quyền cho vai trò này</h6>
                            <div>
                                <button type="button" class="btn btn-sm btn-primary select-all-btn"
                                    {{ $role->name === 'administrator' ? 'disabled' : '' }}>
                                    <i class="fas fa-check-square me-1"></i> Chọn tất cả
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary unselect-all-btn ms-2"
                                    {{ $role->name === 'administrator' ? 'disabled' : '' }}>
                                    <i class="fas fa-square me-1"></i> Bỏ chọn tất cả
                                </button>
                            </div>
                        </div>

                        <div class="row g-3">
                            @php
                                $groups = [
                                    'webinars' => 'Webinar',
                                    'product' => 'Sản phẩm',
                                    'advertisements' => 'Quảng cáo',
                                    'order' => 'Đơn hàng',
                                    'users' => 'Người dùng',
                                    'roles' => 'Vai trò',
                                ];
                                $otherPermissions = collect($groupedPermissions['other']);

                                $checkedPermissions = old('permissions', $role->permissions->pluck('name')->toArray());
                            @endphp
                            @foreach($groups as $prefix => $groupName)
                                @php
                                    $groupPermissions = $otherPermissions->filter(function($permission) use ($prefix) {
                                        return strpos($permission->name, $prefix.' ') === 0;
                                    });
                                    $otherPermissions = $otherPermissions->diff($groupPermissions);

                                @endphp
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $groupName }}</h6>
                                            @if(in_array($prefix, ['webinars', 'product', 'advertisements']))
                                                <div class="mb-2">
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio"
                                                            name="permissions[{{ $prefix }}]"
                                                            id="{{ $prefix }}_index"
                                                            value="{{ $groupedPermissions[$prefix]['index']->name }}"
                                                            {{ in_array($groupedPermissions[$prefix]['index']->name, $checkedPermissions) ? 'checked' : '' }}
                                                            {{ $role->name === 'administrator' ? 'disabled' : '' }}>
                                                        <label class="form-check-label" for="{{ $prefix }}_index">
                                                            Xem tất cả
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio"
                                                            name="permissions[{{ $prefix }}]"
                                                            id="{{ $prefix }}_view_only"
                                                            value="{{ $groupedPermissions[$prefix]['view_only']->name }}"
                                                            {{ in_array($groupedPermissions[$prefix]['view_only']->name, $checkedPermissions) ? 'checked' : '' }}
                                                            {{ $role->name === 'administrator' ? 'disabled' : '' }}>
                                                        <label class="form-check-label" for="{{ $prefix }}_view_only">
                                                            Chỉ xem của mình
                                                        </label>
                                                    </div>
                                                </div>
                                            @endif
                                            @foreach($groupPermissions as $permission)
                                                @if(!in_array($permission->name, [
                                                    $prefix.' view only you',
                                                    $prefix.' index',
                                                ]))
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                        id="permission{{ $permission->id }}"
                                                        name="permissions[]"
                                                        value="{{ $permission->name }}"
                                                        {{ in_array($permission->name, $checkedPermissions) ? 'checked' : '' }}
                                                        {{ $role->name === 'administrator' ? 'checked disabled' : '' }}>
                                                    <label class="form-check-label" for="permission{{ $permission->id }}">
                                                        {{ $permission->display_name }}
                                                    </label>
                                                </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            @if($otherPermissions->count())
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">Khác</h6>
                                            @foreach($otherPermissions as $permission)
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                        id="permission{{ $permission->id }}"
                                                        name="permissions[]"
                                                        value="{{ $permission->name }}"
                                                        {{ in_array($permission->name, $checkedPermissions) ? 'checked' : '' }}
                                                        {{ $role->name === 'administrator' ? 'checked disabled' : '' }}>
                                                    <label class="form-check-label" for="permission{{ $permission->id }}">
                                                        {{ $permission->display_name }}
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        @if($role->name === 'administrator')
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle me-1"></i> Vai trò admin luôn có tất cả các quyền.
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-times me-1"></i> Hủy
                </a>
                <button type="submit" class="btn btn-primary" {{ $role->name === 'administrator' ? 'disabled' : '' }}>
                    <i class="fas fa-save me-1"></i> Cập nhật vai trò
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script src="{{ asset('js/role-management.js') }}"></script>
@endpush

@endsection
