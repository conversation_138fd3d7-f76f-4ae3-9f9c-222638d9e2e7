@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section with Gradient Background -->
    <!-- <div class="module-header mb-5">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="header-content">
                    <h1 class="display-5 fw-bold text-white mb-2">
                        <i class="fas fa-puzzle-piece me-3"></i>
                        Quản lý Module
                    </h1>
                    <p class="lead text-white-50 mb-0">
                        <PERSON><PERSON><PERSON> hoạt và quản lý các tính năng mở rộng cho hệ thống
                    </p>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="header-actions">
                    <button type="button" class="btn btn-light btn-lg shadow-sm me-2" data-bs-toggle="modal" data-bs-target="#moduleStoreModal">
                        <i class="fas fa-store me-2"></i> C<PERSON>a hàng <PERSON>du<PERSON>
                    </button>
                    <button type="button" class="btn btn-success btn-lg shadow-sm" id="refreshModulesBtn">
                        <i class="fas fa-sync-alt me-2"></i> Làm mới
                    </button>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Enhanced Statistics Cards -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card-modern bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title text-white fw-bold text-uppercase mb-1">Tổng Module</h5>
                            <h2 class="card-text text-white fw-bold">{{ $modules->count() }}</h2>
                        </div>
                        <div class="col-auto">
                            <div class="stats-icon-modern">
                                <i class="fas fa-puzzle-piece"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card-modern bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title text-white fw-bold text-uppercase mb-1">Đã kích hoạt</h5>
                            <h2 class="card-text text-white fw-bold">{{ $modules->where('is_enabled', true)->count() }}</h2>
                        </div>
                        <div class="col-auto">
                            <div class="stats-icon-modern">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card-modern bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title text-white fw-bold text-uppercase mb-1">Chưa kích hoạt</h5>
                            <h2 class="card-text text-white fw-bold">{{ $modules->where('is_enabled', false)->count() }}</h2>
                        </div>
                        <div class="col-auto">
                            <div class="stats-icon-modern">
                                <i class="fas fa-pause-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card-modern bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title text-white fw-bold text-uppercase mb-1">Module Core</h5>
                            <h2 class="card-text text-white fw-bold">{{ $modules->where('is_core', true)->count() }}</h2>
                        </div>
                        <div class="col-auto">
                            <div class="stats-icon-modern">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filter Section -->
    <div class="filter-section mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-filter text-primary me-2"></i>
                            Lọc theo danh mục
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="filter-buttons d-flex flex-wrap gap-2 justify-content-md-end">
                            <button class="btn btn-outline-primary category-filter active" data-category="all">
                                <i class="fas fa-th-large me-1"></i> Tất cả
                            </button>
                            @foreach($categories as $category)
                                <button class="btn btn-outline-primary category-filter" data-category="{{ $category }}">
                                    @switch($category)
                                        @case('analytics')
                                            <i class="fas fa-chart-line me-1"></i>
                                            @break
                                        @case('communication')
                                            <i class="fas fa-comments me-1"></i>
                                            @break
                                        @case('security')
                                            <i class="fas fa-shield-alt me-1"></i>
                                            @break
                                        @case('integration')
                                            <i class="fas fa-plug me-1"></i>
                                            @break
                                        @case('marketing')
                                            <i class="fas fa-bullhorn me-1"></i>
                                            @break
                                        @case('education')
                                            <i class="fas fa-graduation-cap me-1"></i>
                                            @break
                                        @case('livestream')
                                            <i class="fas fa-video me-1"></i>
                                            @break
                                        @default
                                            <i class="fas fa-cube me-1"></i>
                                    @endswitch
                                    {{ ucfirst($category) }}
                                </button>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Modules Grid -->
    <div class="modules-grid">
        <div class="row" id="modulesContainer">
            @foreach($modules->groupBy('category') as $category => $categoryModules)
                @foreach($categoryModules as $module)
                    <div class="col-xl-4 col-lg-6 mb-4 module-card" data-category="{{ $module->category }}">
                        <div class="module-card-modern {{ $module->is_enabled ? 'enabled' : 'disabled' }}">
                            <!-- Card Header -->
                            <div class="module-header-modern {{ $module->is_enabled ? 'bg-gradient-success' : 'bg-gradient-secondary' }}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="module-title">
                                        <div class="module-icon-wrapper">
                                            <i class="{{ $module->icon }}"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 text-white fw-bold">{{ $module->display_name }}</h6>
                                            <small class="text-white-50">v{{ $module->version }}</small>
                                        </div>
                                    </div>
                                    <div class="module-controls">
                                        @if($module->is_core)
                                            <span class="badge bg-warning mb-2">
                                                <i class="fas fa-shield-alt"></i> Core
                                            </span>
                                        @endif
                                        <div class="form-check form-switch form-switch-lg">
                                            <input class="form-check-input module-toggle" 
                                                   type="checkbox" 
                                                   data-module-id="{{ $module->id }}"
                                                   {{ $module->is_enabled ? 'checked' : '' }}
                                                   {{ $module->is_core ? 'disabled' : '' }}>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Card Body -->
                            <div class="module-body-modern">
                                <p class="module-description">{{ $module->description }}</p>
                                
                                <!-- Module Price -->
                                <div class="module-price-section mb-3">
                                    @if($module->price > 0)
                                        <div class="price-tag">
                                            <span class="price-amount">{{ formatModulePrice($module->price, $module->currency) }}</span>
                                            <span class="price-label">/ Mua một lần</span>
                                        </div>
                                    @else
                                        <div class="price-tag free">
                                            <span class="price-amount">{{ formatModulePrice($module->price) }}</span>
                                            <span class="price-label">Không tốn phí</span>
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="module-meta">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="meta-item">
                                                <i class="fas fa-tag text-primary"></i>
                                                <span class="badge bg-primary-soft">{{ ucfirst($module->category) }}</span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="meta-item">
                                                <i class="fas fa-calendar text-info"></i>
                                                <small class="text-muted">{{ $module->updated_at->format('d/m/Y') }}</small>
                                            </div>
                                        </div>
                                        @if($module->permission)
                                            <div class="col-12">
                                                <div class="meta-item">
                                                    <i class="fas fa-key text-warning"></i>
                                                    <span class="badge bg-warning-soft">{{ $module->permission }}</span>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Card Footer -->
                            <div class="module-footer-modern">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="action-buttons">
                                        @if($module->route && $module->is_enabled)
                                            <button type="button" class="btn btn-sm btn-primary">
                                                <i class="fas fa-cog"></i> Cấu hình
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#moduleInfoModal"
                                                data-module="{{ json_encode($module) }}">
                                            <i class="fas fa-info-circle"></i> Chi tiết
                                        </button>
                                    </div>
                                    <div class="status-indicator">
                                        @if($module->is_enabled)
                                            <span class="status-dot status-active"></span>
                                            <small class="text-success fw-medium">Hoạt động</small>
                                        @else
                                            <span class="status-dot status-inactive"></span>
                                            <small class="text-muted">Tắt</small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endforeach
        </div>
    </div>
</div>

<!-- Enhanced Module Info Modal -->
<div class="modal fade" id="moduleInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title fw-bold">
                    <i class="fas fa-info-circle me-2"></i>
                    Thông tin Module
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <div id="moduleInfoContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Module Store Modal -->
<div class="modal fade" id="moduleStoreModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-info text-white border-0">
                <h5 class="modal-title fw-bold">
                    <i class="fas fa-store me-2"></i>
                    Cửa hàng Module
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-5">
                <div class="text-center">
                    <div class="store-placeholder">
                        <i class="fas fa-store fa-4x text-info mb-4"></i>
                        <h3 class="fw-bold mb-3">Cửa hàng Module</h3>
                        <p class="text-muted mb-4 lead">
                            Tính năng này sẽ được phát triển trong phiên bản tương lai.<br>
                            Bạn sẽ có thể tải xuống và cài đặt các module mở rộng từ cửa hàng.
                        </p>
                        <div class="coming-soon-features">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="feature-item">
                                        <i class="fas fa-download fa-2x text-primary mb-2"></i>
                                        <h6>Tải xuống Module</h6>
                                        <small class="text-muted">Cài đặt module từ kho ứng dụng</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="feature-item">
                                        <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                        <h6>Đánh giá & Review</h6>
                                        <small class="text-muted">Xem đánh giá từ cộng đồng</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="feature-item">
                                        <i class="fas fa-sync-alt fa-2x text-success mb-2"></i>
                                        <h6>Cập nhật tự động</h6>
                                        <small class="text-muted">Tự động cập nhật phiên bản mới</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Đóng
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
    /* Module Header with Theme Gradient Background */
    .module-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        margin: -1.5rem -15px 0 -15px;
        position: relative;
        overflow: hidden;
    }

    .module-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .header-content {
        position: relative;
        z-index: 2;
    }

    .header-actions {
        position: relative;
        z-index: 2;
    }

    /* Enhanced Statistics Cards with Theme Colors */
    .stats-card-modern {
        border-radius: 20px;
        border: none;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .stats-card-modern:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stats-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        z-index: 1;
    }

    .stats-card-modern .card-body {
        position: relative;
        z-index: 2;
        padding: 2rem;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #239c5a 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #e6b400 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, var(--secondary-color) 0%, #5e6c87 100%);
    }

    .stats-icon-modern {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        backdrop-filter: blur(10px);
    }

    /* Filter Section with Theme Colors */
    .filter-section .card {
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(250, 129, 40, 0.1);
    }

    .filter-buttons .btn {
        border-radius: 25px;
        padding: 0.5rem 1.2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
        color: var(--text-color);
    }

    .filter-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(250, 129, 40, 0.2);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    .category-filter.active {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-color: transparent;
        color: white;
        box-shadow: 0 5px 15px rgba(250, 129, 40, 0.4);
    }

    /* Modern Module Cards with Theme Colors */
    .module-card-modern {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        border: 2px solid transparent;
        position: relative;
    }

    .module-card-modern:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .module-card-modern.enabled {
        border-color: var(--success-color);
        box-shadow: 0 10px 30px rgba(39, 180, 105, 0.2);
    }

    .module-card-modern.enabled:hover {
        box-shadow: 0 20px 40px rgba(39, 180, 105, 0.3);
    }

    .module-card-modern.disabled {
        border-color: var(--border-color);
    }

    /* Module Header with Theme Colors */
    .module-header-modern {
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .module-header-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    }

    .module-title {
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 2;
    }

    .module-icon-wrapper {
        width: 50px;
        height: 50px;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        backdrop-filter: blur(10px);
    }

    .module-controls {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    /* Enhanced Form Switch with Theme Colors */
    .form-switch-lg .form-check-input {
        width: 3rem;
        height: 1.5rem;
        border-radius: 1rem;
        background-color: rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
    }

    .form-switch-lg .form-check-input:checked {
        background-color: #fff;
        border-color: #fff;
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
    }

    .form-switch-lg .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
    }

    /* Module Body */
    .module-body-modern {
        padding: 1.5rem;
    }

    .module-description {
        color: var(--text-muted);
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    /* Module Price Section */
    .module-price-section {
        text-align: center;
    }

    .price-tag {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--primary-light) 0%, rgba(250, 129, 40, 0.05) 100%);
        border: 2px solid rgba(250, 129, 40, 0.2);
        transition: all 0.3s ease;
    }

    .price-tag.free {
        background: linear-gradient(135deg, var(--success-light) 0%, rgba(39, 180, 105, 0.05) 100%);
        border-color: rgba(39, 180, 105, 0.2);
    }

    .price-tag:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(250, 129, 40, 0.15);
    }

    .price-tag.free:hover {
        box-shadow: 0 5px 15px rgba(39, 180, 105, 0.15);
    }

    .price-amount {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1;
    }

    .price-tag.free .price-amount {
        color: var(--success-color);
    }

    .price-label {
        font-size: 0.75rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
    }

    .module-meta {
        background: var(--light-bg);
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid var(--border-color);
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .meta-item:last-child {
        margin-bottom: 0;
    }

    .meta-item i {
        width: 16px;
        text-align: center;
    }

    /* Badge Styles with Theme Colors */
    .badge {
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .bg-primary-soft {
        background-color: var(--primary-light);
        color: var(--primary-color);
    }

    .bg-warning-soft {
        background-color: var(--warning-light);
        color: var(--warning-color);
    }

    /* Module Footer */
    .module-footer-modern {
        padding: 1rem 1.5rem;
        background: var(--light-bg);
        border-top: 1px solid var(--border-color);
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
    }

    /* Status Indicator with Theme Colors */
    .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
    }

    .status-active {
        background-color: var(--success-color);
        box-shadow: 0 0 0 2px rgba(39, 180, 105, 0.3);
        animation: pulse 2s infinite;
    }

    .status-inactive {
        background-color: var(--secondary-color);
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(39, 180, 105, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(39, 180, 105, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(39, 180, 105, 0);
        }
    }

    /* Animation for filtering */
    .fade-out {
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }

    .module-card {
        transition: all 0.3s ease;
    }

    /* Enhanced Modal Styles with Theme Colors */
    .modal-content {
        border-radius: 20px;
        overflow: hidden;
    }

    .modal-header.bg-gradient-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    }

    .modal-header.bg-gradient-info {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    /* Store Modal Features */
    .store-placeholder {
        padding: 2rem;
    }

    .coming-soon-features .feature-item {
        padding: 1.5rem;
        border-radius: 15px;
        background: var(--light-bg);
        transition: all 0.3s ease;
        height: 100%;
        border: 1px solid var(--border-color);
    }

    .coming-soon-features .feature-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(250, 129, 40, 0.1);
        background: white;
        border-color: var(--primary-color);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .module-header {
            margin: -1.5rem -15px 0 -15px;
            padding: 2rem 1rem;
            text-align: center;
        }

        .header-actions {
            margin-top: 1rem;
        }

        .filter-buttons {
            justify-content: center !important;
        }

        .stats-card-modern .card-body {
            padding: 1.5rem;
        }

        .module-header-modern {
            padding: 1rem;
        }

        .module-body-modern {
            padding: 1rem;
        }

        .module-footer-modern {
            padding: 1rem;
        }

        .price-tag {
            padding: 0.5rem 1rem;
        }

        .price-amount {
            font-size: 1.1rem;
        }
    }

    /* Loading Animation */
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Smooth transitions for all interactive elements */
    .btn, .card, .badge, .form-control, .form-select {
        transition: all 0.3s ease;
    }

    /* Custom scrollbar for modal */
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Category specific colors for filter buttons */
    .category-filter[data-category="analytics"]:hover,
    .category-filter[data-category="analytics"].active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .category-filter[data-category="communication"]:hover,
    .category-filter[data-category="communication"].active {
        background: linear-gradient(135deg, var(--success-color) 0%, #239c5a 100%);
    }

    .category-filter[data-category="security"]:hover,
    .category-filter[data-category="security"].active {
        background: linear-gradient(135deg, var(--danger-color) 0%, #e64c4c 100%);
    }

    .category-filter[data-category="integration"]:hover,
    .category-filter[data-category="integration"].active {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    .category-filter[data-category="marketing"]:hover,
    .category-filter[data-category="marketing"].active {
        background: linear-gradient(135deg, var(--warning-color) 0%, #e6b400 100%);
    }

    .category-filter[data-category="education"]:hover,
    .category-filter[data-category="education"].active {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
    }

    .category-filter[data-category="livestream"]:hover,
    .category-filter[data-category="livestream"].active {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    .category-filter[data-category="utility"]:hover,
    .category-filter[data-category="utility"].active {
        background: linear-gradient(135deg, var(--secondary-color) 0%, #5e6c87 100%);
    }
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Category filter with smooth animations
    $('.category-filter').click(function() {
        $('.category-filter').removeClass('active');
        $(this).addClass('active');
        
        const category = $(this).data('category');
        
        if (category === 'all') {
            $('.module-card').each(function() {
                $(this).removeClass('fade-out').show();
            });
        } else {
            $('.module-card').each(function() {
                if ($(this).data('category') === category) {
                    $(this).removeClass('fade-out').show();
                } else {
                    $(this).addClass('fade-out');
                    setTimeout(() => {
                        if ($(this).hasClass('fade-out')) {
                            $(this).hide();
                        }
                    }, 300);
                }
            });
        }
    });
    
    // Enhanced module toggle with loading state
    $('.module-toggle').change(function() {
        const moduleId = $(this).data('module-id');
        const isEnabled = $(this).is(':checked');
        const card = $(this).closest('.module-card-modern');
        const cardHeader = card.find('.module-header-modern');
        const statusIndicator = card.find('.status-indicator');
        const toggle = $(this);
        
        // Add loading state
        toggle.prop('disabled', true);
        
        $.ajax({
            url: `/admin/modules/${moduleId}/toggle`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                action: isEnabled ? 'enable' : 'disable'
            },
            success: function(response) {
                if (response.success) {
                    showAlert('Thành công', response.message, 'success', 3000);
                    
                    // Update card appearance with smooth transition
                    if (isEnabled) {
                        card.removeClass('disabled').addClass('enabled');
                        cardHeader.removeClass('bg-gradient-secondary').addClass('bg-gradient-success');
                        statusIndicator.html(`
                            <span class="status-dot status-active"></span>
                            <small class="text-success fw-medium">Hoạt động</small>
                        `);
                    } else {
                        card.removeClass('enabled').addClass('disabled');
                        cardHeader.removeClass('bg-gradient-success').addClass('bg-gradient-secondary');
                        statusIndicator.html(`
                            <span class="status-dot status-inactive"></span>
                            <small class="text-muted">Tắt</small>
                        `);
                    }
                    
                    // Update statistics with animation
                    setTimeout(() => {
                        updateStatistics();
                    }, 500);
                } else {
                    showAlert('Lỗi', response.message, 'error');
                    // Revert toggle state
                    toggle.prop('checked', !isEnabled);
                }
            },
            error: function() {
                showAlert('Lỗi', 'Có lỗi xảy ra khi thay đổi trạng thái module.', 'error');
                // Revert toggle state
                toggle.prop('checked', !isEnabled);
            },
            complete: function() {
                toggle.prop('disabled', false);
            }
        });
    });
    
    // Refresh modules functionality
    $('#refreshModulesBtn').click(function() {
        const btn = $(this);
        const originalText = btn.html();
        
        btn.prop('disabled', true).html('<span class="loading-spinner me-2"></span> Đang làm mới...');
        
        // Simulate refresh with animation
        $('.module-card-modern').addClass('fade-out');
        
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
    
    // Enhanced module info modal with better content
    $('#moduleInfoModal').on('show.bs.modal', function(event) {
        const button = $(event.relatedTarget);
        const module = button.data('module');
        
        const statusBadge = module.is_enabled 
            ? '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Đã kích hoạt</span>' 
            : '<span class="badge bg-secondary"><i class="fas fa-pause-circle me-1"></i>Chưa kích hoạt</span>';
            
        const coreBadge = module.is_core 
            ? '<span class="badge bg-warning ms-1"><i class="fas fa-shield-alt me-1"></i>Core</span>' 
            : '';
        
        const content = `
            <div class="row">
                <div class="col-md-8">
                    <div class="module-info-header mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="module-icon-large me-3">
                                <i class="${module.icon}"></i>
                            </div>
                            <div>
                                <h3 class="mb-1">${module.display_name}</h3>
                                <p class="text-muted mb-0">${module.description}</p>
                            </div>
                        </div>
                        <div class="module-badges">
                            ${statusBadge}
                            ${coreBadge}
                        </div>
                    </div>
                    
                    <div class="module-details">
                        <h5 class="mb-3"><i class="fas fa-info-circle text-primary me-2"></i>Thông tin chi tiết</h5>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-bold" style="width: 30%;">Tên module:</td>
                                        <td><code>${module.name}</code></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Phiên bản:</td>
                                        <td><span class="badge bg-secondary">${module.version}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Giá:</td>
                                        <td>
                                            ${module.price > 0 
                                                ? `<span class="badge bg-primary">${new Intl.NumberFormat('vi-VN').format(module.price)} ${module.currency}</span>`
                                                : '<span class="badge bg-success">Miễn phí</span>'
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Danh mục:</td>
                                        <td><span class="badge bg-primary">${module.category}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Thứ tự sắp xếp:</td>
                                        <td>${module.sort_order}</td>
                                    </tr>
                                    ${module.permission ? `
                                    <tr>
                                        <td class="fw-bold">Quyền cần thiết:</td>
                                        <td><span class="badge bg-info">${module.permission}</span></td>
                                    </tr>
                                    ` : ''}
                                    ${module.route ? `
                                    <tr>
                                        <td class="fw-bold">Route:</td>
                                        <td><code>${module.route}</code></td>
                                    </tr>
                                    ` : ''}
                                    <tr>
                                        <td class="fw-bold">Cập nhật lần cuối:</td>
                                        <td>${new Date(module.updated_at).toLocaleDateString('vi-VN')}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="module-config-section">
                        <div class="text-center mb-4">
                            <div class="module-icon-display">
                                <i class="${module.icon}"></i>
                            </div>
                        </div>
                        
                        <h6 class="mb-3"><i class="fas fa-cogs text-warning me-2"></i>Cấu hình Module</h6>
                        ${module.config && Object.keys(module.config).length > 0 
                            ? `<div class="config-content">
                                <pre class="bg-light p-3 rounded"><code>${JSON.stringify(module.config, null, 2)}</code></pre>
                               </div>`
                            : `<div class="no-config text-center py-4">
                                <i class="fas fa-cog fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">Chưa có cấu hình</p>
                               </div>`
                        }
                        
                        ${module.is_enabled && module.route ? `
                        <div class="mt-3">
                            <a href="#" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-external-link-alt me-1"></i>
                                Mở cấu hình
                            </a>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        
        $('#moduleInfoContent').html(content);
    });
    
    // Function to update statistics with animation
    function updateStatistics() {
        $('.stats-card-modern').each(function() {
            $(this).addClass('animate__animated animate__pulse');
            setTimeout(() => {
                $(this).removeClass('animate__animated animate__pulse');
            }, 1000);
        });
    }
    
    // Add hover effects to module cards
    $('.module-card-modern').hover(
        function() {
            $(this).find('.action-buttons .btn').addClass('animate__animated animate__fadeInUp');
        },
        function() {
            $(this).find('.action-buttons .btn').removeClass('animate__animated animate__fadeInUp');
        }
    );
    
    // Smooth scroll to top when filtering
    $('.category-filter').click(function() {
        $('html, body').animate({
            scrollTop: $('.modules-grid').offset().top - 100
        }, 500);
    });
});
</script>

<style>
    /* Additional styles for enhanced modal */
    .module-info-header {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 1rem;
    }
    
    .module-icon-large {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
    }
    
    .module-icon-display {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        margin: 0 auto;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .module-badges {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .config-content pre {
        max-height: 200px;
        overflow-y: auto;
        font-size: 0.8rem;
    }
    
    .no-config {
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .module-config-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        height: fit-content;
    }
    
    /* Animation classes */
    .animate__animated {
        animation-duration: 0.5s;
        animation-fill-mode: both;
    }
    
    .animate__pulse {
        animation-name: pulse;
    }
    
    .animate__fadeInUp {
        animation-name: fadeInUp;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translate3d(0, 20px, 0);
        }
        to {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }
</style>
@endpush 