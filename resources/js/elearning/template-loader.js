const singleChoiceTemplate = `
    <div class="mb-4">
      <p class="text-gray-300 text-sm mb-2">{{description}}</p>
      <h4 class="text-white font-medium border-b border-neutral-200 !pb-5">{{question}}</h4>
    </div>

    <form id="interactionForm" class="space-y-4">
      <div id="optionsContainer">
        <!-- Options will be populated here -->
      </div>

      <div class="flex justify-between items-center mt-6">
        <div id="skipContainer">
          <!-- Skip button will be populated here if allowSkip is true -->
        </div>
        <button
          type="submit"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          id="submitBtn"
        >
          {{submitText}}
        </button>
      </div>
    </form>
  `;
const rateTemplate = `
    <div class="mb-4">
      <p class="text-gray-300 text-sm mb-2">{{description}}</p>
      <h4 class="text-white font-medium border-b border-neutral-200 !pb-5">{{question}}</h4>
    </div>

    <form id="interactionForm" class="space-y-4">
      <div class="space-y-4">
        <div class="flex justify-between items-center" id="ratingContainer">
          <!-- Rating options will be populated here -->
        </div>
      </div>

      <div class="flex justify-between items-center mt-6">
        <div id="skipContainer">
          <!-- Skip button will be populated here if allowSkip is true -->
        </div>
        <button
          type="submit"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          id="submitBtn"
        >
          {{submitText}}
        </button>
      </div>
    </form>
  `;

const surveyTemplate = `
    <div class="mb-4">
      <p class="text-gray-300 text-sm mb-2">{{description}}</p>
      <h4 class="text-white font-medium border-b border-neutral-200 !pb-5">{{question}}</h4>
    </div>

    <form id="interactionForm" class="space-y-4">
      <div>
        <textarea
          name="survey"
          placeholder="{{placeholder}}"
          class="w-full p-3 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
          rows="4"
          maxlength="{{maxLength}}"
          id="surveyTextarea"
        ></textarea>
        <div class="text-right text-sm text-gray-400 mt-1">
          <span class="char-count">0</span>/{{maxLength}} ký tự
        </div>
      </div>

      <div class="flex justify-between items-center mt-6">
        <div id="skipContainer">
          <!-- Skip button will be populated here if allowSkip is true -->
        </div>
        <button
          type="submit"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          id="submitBtn"
        >
          {{submitText}}
        </button>
      </div>
    </form>

  `;
const multipleTemplate = `
    <div class="mb-4">
      <p class="text-gray-300 text-sm mb-2">{{description}}</p>
      <h4 class="text-white font-medium border-b border-neutral-200 !pb-5">{{question}}</h4>
    </div>

    <form id="interactionForm" class="space-y-4">
      <div class="space-y-3">
        <div id="optionsContainer">
          <!-- Multiple select options will be populated here -->
        </div>
      </div>

      <div class="flex justify-between items-center mt-6">
        <div id="skipContainer">
          <!-- Skip button will be populated here if allowSkip is true -->
        </div>
        <button
          type="submit"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          id="submitBtn"
        >
          {{submitText}}
        </button>
      </div>
    </form>
  `;

const templates = {
    "single-choice": singleChoiceTemplate,
    "true-false": singleChoiceTemplate,
    rating: rateTemplate,
    "open-survey": surveyTemplate,
    open_survey: surveyTemplate,
    "multiple-choice": multipleTemplate,
};

/**
 * TemplateLoader Class
 * Handles loading and processing HTML templates for interaction modals
 * Supports template caching and variable substitution
 */
class TemplateLoader {
    constructor() {
        this.templateCache = new Map();
    }

    /**
     * Load template by type
     * @param {string} type - Template type (e.g., 'multiple_choice', 'true_false')
     * @returns {Promise<string>} Template HTML content
     */
    async loadTemplate(type) {
        // Check cache first
        if (this.templateCache.has(type)) {
            return this.templateCache.get(type);
        }

        // Get template from templates object
        const template = this.getTemplate(type);
        this.templateCache.set(type, template);
        return template;
    }

    /**
     * Get template from templates object
     * @param {string} type - Template type
     * @returns {string} Template content
     */
    getTemplate(type) {
        if (!templates[type]) {
            throw new Error(`Template ${type} not found in templates object`);
        }
        return templates[type];
    }

    /**
     * Process template with data substitution
     * @param {string} template - Template HTML content
     * @param {Object} data - Data for variable substitution
     * @returns {string} Processed HTML
     */
    processTemplate(template, data = {}) {
        let processedTemplate = template;

        // Replace template variables {{variable}}
        Object.keys(data).forEach((key) => {
            const regex = new RegExp(`{{${key}}}`, "g");
            const value = data[key] !== undefined ? data[key] : "";
            processedTemplate = processedTemplate.replace(regex, value);
        });

        // Clean up any remaining unreplaced variables
        processedTemplate = processedTemplate.replace(/{{\w+}}/g, "");

        return processedTemplate;
    }

    /**
     * Load and process template in one call
     * @param {string} type - Template type
     * @param {Object} data - Data for variable substitution
     * @returns {Promise<string>} Processed HTML
     */
    async loadAndProcess(type, data = {}) {
        const template = await this.loadTemplate(type);
        return this.processTemplate(template, data);
    }

    /**
     * Preload multiple templates
     * @param {string[]} types - Array of template types to preload
     * @returns {Promise<void>}
     */
    async preloadTemplates(types) {
        types.forEach((type) => {
            if (!this.templateCache.has(type)) {
                const template = this.getTemplate(type);
                this.templateCache.set(type, template);
            }
        });
    }

    /**
     * Clear template cache
     */
    clearCache() {
        this.templateCache.clear();
    }

    /**
     * Get cached template types
     * @returns {string[]} Array of cached template types
     */
    getCachedTypes() {
        return Array.from(this.templateCache.keys());
    }

    /**
     * Check if template is cached
     * @param {string} type - Template type
     * @returns {boolean}
     */
    isCached(type) {
        return this.templateCache.has(type);
    }

    /**
     * Generate options HTML for choice-based templates
     * @param {Array} options - Array of option objects
     * @param {string} inputType - Type of input ('radio' or 'checkbox')
     * @param {string} inputName - Name attribute for inputs
     * @returns {string} Options HTML
     */
    generateOptionsHTML(options, inputType = "radio", inputName = "answer") {
        return options
            .map(
                (option) => `
      <label class="flex items-center space-x-3 cursor-pointer p-2 rounded-lg hover:bg-gray-700 transition-colors">
        <input
          type="${inputType}"
          name="${inputName}"
          value="${option.value}"
          class="text-blue-600 bg-gray-600 border-gray-500 focus:ring-blue-500"
        />
        <span class="text-gray-200 text-label">${option.label}</span>
      </label>
    `
            )
            .join("");
    }

    /**
     * Generate rating scale HTML with star interface
     * @param {Array} options - Rating options array
     * @returns {string} Rating scale HTML
     */
    generateRatingHTML(options) {
        let html = "";
        options.forEach((option) => {
            html += `
        <label class="flex flex-col items-center cursor-pointer">
          <input type="radio" name="rating" value="${option.value}" class="mb-2">
          <span class="text-lg">⭐</span>
          <span class="text-lg text-gray-400 text-center text-label">${option.label}</span>
        </label>
      `;
        });
        return html;
    }

    /**
     * Generate skip button HTML if needed
     * @param {boolean} allowSkip - Whether skip is allowed
     * @param {string} skipText - Skip button text
     * @returns {string} Skip button HTML
     */
    generateSkipButton(allowSkip, skipText = "Bỏ qua") {
        if (!allowSkip) return "";

        return `
      <button
        type="button"
        class="text-gray-400 hover:text-gray-200 px-4 py-2 transition-colors"
        id="skipBtn"
      >
        ${skipText}
      </button>
    `;
    }
}

// Export for use
window.TemplateLoader = TemplateLoader;
