/**
 * InteractionModal Class
 * Handles different types of interactive modals for e-learning platform
 * Supports: multiple choice, rating scale, open survey, multiple select
 */
class InteractionModal {
    constructor(modalSelector = "#questionPopup") {
        this.config = {
            defaultTimeout: 30000,
            retryAttempts: 3,
            showProgress: true,
            allowSkip: false,
            autoClose: true,
            closeDelay: 3000,
            apiBaseUrl: "/api",
            submitEndpoint: "/api/submit",
            userId: "user_123",
            sessionId: "session_456",
            trackingEnabled: true,
        };
        this.modalSelector = modalSelector;
        this.currentModal = null;
        this.modalUtil = null;
        this.templateLoader = null;
        this.responses = {};
        this.questionCode = null;
        this.init();
    }

    init() {
        // Initialize template loader
        this.templateLoader = new TemplateLoader();

        // Initialize modal utility
        this.modalUtil = new ModalUtil(this.modalSelector, {
            onOpen: () => this.onModalOpen(),
            onClose: () => this.onModalClose(),
        });

        // Preload common templates
        this.preloadTemplates();

        // Bind trigger events
        this.bindTriggerEvents();
    }

    onClose(hard = true) {
        if (hard) {
            localStorage.setItem(`question_${this.questionCode}_expired`, true);
        }
        this.modalUtil.close();
    }

    preloadTemplates() {
        const templateTypes = [
            "single-choice",
            "rating",
            "open-survey",
            "multiple-choice",
            "true-false",
        ];
        try {
            this.templateLoader.preloadTemplates(templateTypes);
            console.log("Templates preloaded successfully");
        } catch (error) {
            console.warn("Failed to preload some templates:", error);
        }
    }

    bindTriggerEvents() {
        // Bind manual trigger buttons using data-id
        $("[data-id]").on("click", (e) => {
            const modalId = $(e.target).data("id");
            this.showModalById(modalId);
        });
    }

    showModalByType(type) {
        if (!this.config || !this.config.modals) {
            console.warn("Config not loaded yet");
            return;
        }
        const modal = this.config.modals.find(
            (m) => m.trigger.condition === type
        );
        if (modal) {
            this.showModal(modal);
        } else {
            console.warn(`No modal found for trigger type: ${type}`);
        }
    }

    // Method to show modal by ID
    showModalById(modalId) {
        if (!this.config || !this.config.modals) {
            console.warn("Config not loaded yet");
            return;
        }
        const modal = this.config.modals.find((m) => m.id === modalId);
        if (modal) {
            this.showModal(modal);
        } else {
            console.warn(`No modal found for ID: ${modalId}`);
        }
    }

    // Method to show modal directly with modal config (for external use)
    showModalDirectly(modalConfig, questionCode = "") {
        const lastAdCode = sessionStorage.getItem("lastClosedQuestionCode");
        this.questionCode = questionCode;
        if (lastAdCode === questionCode) {
            // Do not show the same ad again
            return;
        }

        const expired = localStorage.getItem(
            `question_${questionCode}_expired`
        );
        if (expired) {
            // Do not show the same ad again
            return;
        }

        if (modalConfig) {
            sessionStorage.setItem("lastClosedQuestionCode", questionCode);
            this.showModal(modalConfig);
        } else {
            console.warn("No modal config provided");
        }
    }

    async showModal(modalConfig) {
        this.currentModal = modalConfig;
        await this.renderModalContent(modalConfig);
        this.modalUtil.open();
    }

    async renderModalContent(modal) {
        try {
            const content = await this.generateModalHTML(modal);
            $(".interaction-modal-content").html(content);
            $(".modal-title").html(
                `<span class="ani-icon">${modal.icon || "❓"}</span> ${
                    modal.title
                }`
            );

            // Bind form events
            this.bindFormEvents(modal);
        } catch (error) {
            console.error("Error rendering modal content:", error);
            this.showFallbackContent(modal);
        }
    }

    showFallbackContent(modal) {
        const fallbackContent = `
      <div class="modal-body text-white">
        <div class="mb-4">
          <div class="flex items-center mb-3">
            <span class="text-2xl mr-2 ani-icon">${modal.icon || "❓"}</span>
            <h3 class="text-lg font-semibold">${modal.title}</h3>
          </div>
          <p class="text-gray-300 mb-2">${modal.description}</p>
          <p class="text-white font-medium border-b border-neutral-200 !pb-5">${
              modal.question
          }</p>
        </div>
        <div class="text-center text-red-400">
          <p>Không thể tải giao diện tương tác. Vui lòng thử lại sau.</p>
          <button class="btn-cancel mt-4 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
            Đóng
          </button>
        </div>
      </div>
    `;
        $(".interaction-modal-content").html(fallbackContent);
        $(".btn-cancel").on("click", () => this.onClose());
    }

    async generateModalHTML(modal) {
        try {
            // Prepare template data
            const templateData = {
                description: modal.description || "",
                question: modal.question || "",
                submitText: modal.submitText || "Gửi",
                placeholder: modal.placeholder || "Nhập câu trả lời của bạn...",
                maxLength: modal.maxLength || 500,
                minSelections: modal.minSelections || 1,
                maxSelections:
                    modal.maxSelections ||
                    (modal.options ? modal.options.length : 10),
            };

            // Load and process template
            let templateContent = await this.templateLoader.loadAndProcess(
                modal.type,
                templateData
            );

            // Add dynamic content based on modal type
            templateContent = this.populateDynamicContent(
                templateContent,
                modal
            );

            // Wrap in modal structure
            const html = `
        <div class="modal-body text-white">
          ${templateContent}
        </div>
      `;

            return html;
        } catch (error) {
            console.error("Error generating modal HTML:", error);
            throw error;
        }
    }

    populateDynamicContent(templateContent, modal) {
        let content = templateContent;

        // Populate options container for choice-based modals
        if (["single-choice", "true-false"].includes(modal.type)) {
            const optionsHTML = this.templateLoader.generateOptionsHTML(
                modal.options,
                "radio",
                "answer"
            );
            content = content.replace(
                '<div id="optionsContainer">',
                `<div id="optionsContainer">${optionsHTML}`
            );
        }

        // Populate multiple select options
        if (["multiple-choice"].includes(modal.type)) {
            const optionsHTML = this.templateLoader.generateOptionsHTML(
                modal.options,
                "checkbox",
                "multiselect"
            );
            content = content.replace(
                '<div id="optionsContainer">',
                `<div id="optionsContainer">${optionsHTML}`
            );
        }

        // Populate rating scale
        if (modal.type === "rating") {
            const ratingHTML = this.templateLoader.generateRatingHTML(
                modal.options
            );
            content = content.replace(
                '<div class="flex justify-between items-center" id="ratingContainer">',
                `<div class="flex justify-between items-center" id="ratingContainer">${ratingHTML}`
            );
        }

        // Add skip button if allowed
        const skipHTML = this.templateLoader.generateSkipButton(
            modal.allowSkip,
            modal.skipText
        );
        content = content.replace(
            '<div id="skipContainer">',
            `<div id="skipContainer">${skipHTML}`
        );

        // Set required attribute for textarea if needed
        if (modal.type === "open-survey" && modal.required) {
            content = content.replace(
                'id="surveyTextarea"',
                'id="surveyTextarea" required'
            );
        }

        return content;
    }

    // Legacy methods removed - now using template system

    bindFormEvents(modal) {
        // Skip button
        $("#skipBtn").on("click", () => {
            this.handleSkip(modal);
        });

        // Form submission
        $("#interactionForm").on("submit", (e) => {
            e.preventDefault();
            this.handleFormSubmit(modal);
        });

        // Character counter for textarea
        $('textarea[name="survey"]').on("input", function () {
            const length = $(this).val().length;
            $(".char-count").text(length);
        });

        // Slider value update
        $('input[name="slider"]').on("input", function () {
            const value = $(this).val();
            const unit = modal.slider?.unit || "";
            $(".slider-value").text(value + unit);
        });

        // Multiple select validation
        if (modal.type === "multiple-choice") {
            $('input[name="multiselect"]').on("change", () => {
                this.validateMultipleSelect(modal);
            });
        }
    }

    handleSkip(modal) {
        if (modal.allowSkip) {
            console.log("Skipped modal:", modal.id);
            this.onClose();

            // Store skip response
            this.responses[modal.id] = {
                modalId: modal.id,
                type: modal.type,
                skipped: true,
                timestamp: new Date().toISOString(),
            };
        }
    }

    validateMultipleSelect(modal) {
        const selected = $('input[name="multiselect"]:checked').length;
        const min = modal.minSelections || 1;
        const max = modal.maxSelections || modal.options.length;

        if (selected > max) {
            // Uncheck the last checked item
            $('input[name="multiselect"]:checked')
                .last()
                .prop("checked", false);
            this.showMessage(`Chỉ được chọn tối đa ${max} đáp án`, "warning");
        }
    }

    handleFormSubmit(modal) {
        const formData = this.collectFormData(modal);

        if (!this.validateFormData(formData, modal)) {
            return;
        }

        this.submitResponse(modal, formData);
    }

    collectFormData(modal) {
        const formData = {
            modalId: modal.id,
            type: modal.type,
            timestamp: new Date().toISOString(),
            userId: this.config?.userId || "anonymous",
            sessionId: this.config?.sessionId || "session_" + Date.now(),
        };

        switch (modal.type) {
            case "single-choice":
            case "true-false":
                formData.value = $('input[type="radio"]:checked').val();
                formData.label = $('input[type="radio"]:checked')
                    .parent()
                    .find(".text-label")
                    .text()
                    .trim();
                break;
            case "rating":
                formData.value = $('input[name="rating"]:checked').val();
                formData.label = $('input[type="radio"]:checked')
                    .parent()
                    .find(".text-label")
                    .text()
                    .trim();
                break;
            case "open-survey":
            case "open_survey":
                formData.value = $('textarea[name="survey"]').val().trim();
                break;
            case "multiple-choice":
                formData.value = $('input[name="multiselect"]:checked')
                    .map(function () {
                        return {
                            label: $(this)
                                .parent()
                                .find(".text-label")
                                .text()
                                .trim(),
                            value: $(this).val(),
                        };
                    })
                    .get();
                break;
        }

        return formData;
    }

    validateFormData(formData, modal) {
        if (modal.required) {
            switch (modal.type) {
                case "single-choice":
                case "rating":
                    if (!formData.value) {
                        this.showMessage("Vui lòng chọn một đáp án", "error");
                        return false;
                    }
                    break;
                case "open-survey":
                    if (
                        !formData.value ||
                        formData.value.length < (modal.minLength || 1)
                    ) {
                        this.showMessage(
                            `Vui lòng nhập ít nhất ${
                                modal.minLength || 1
                            } ký tự`,
                            "error"
                        );
                        return false;
                    }
                    break;
                case "multiple-choice":
                    const min = modal.minSelections || 1;
                    if (!formData.value || formData.value.length < min) {
                        this.showMessage(
                            `Vui lòng chọn ít nhất ${min} đáp án`,
                            "error"
                        );
                        return false;
                    }
                    break;
            }
        }
        return true;
    }

    async submitResponse(modal, formData) {
        try {
            console.log("formData", formData);
            // Show loading state
            $("#submitBtn").prop("disabled", true).text("Đang gửi...");
            // Simulate API call (replace with actual API call)
            const { data } = await axios.post(
                apiEndpoint.submitQuestion(modal.id),
                {
                    type: formData?.type,
                    label: formData?.label,
                    option_key: formData?.value,
                }
            );

            if (data.error) {
                this.showMessage(
                    data?.message || "Có lỗi xảy ra. Vui lòng thử",
                    "error"
                );
            } else {
                this.showMessage(data?.message, "success");
                this.onClose();
                this.currentModal = null;
            }
        } catch (error) {
            this.showMessage(
                data?.message || "Có lỗi xảy ra. Vui lòng thử",
                "error"
            );
        } finally {
            $("#submitBtn")
                .prop("disabled", false)
                .text(modal.submitText || "Gửi");
        }
    }

    // Mock API call - replace with actual implementation
    mockApiCall(endpoint, data) {
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log("API Call:", endpoint, data);
                resolve({ success: true, data: data });
            }, 1000);
        });
    }

    showMessage(message, type = "info") {
        const colors = {
            success: "bg-green-600",
            error: "bg-red-600",
            warning: "bg-yellow-600",
            info: "bg-blue-600",
        };

        const messageEl = $(`
      <div class="message-toast fixed top-4 right-4 z-[99999999] ${colors[type]} text-white px-4 py-2 rounded shadow-lg">
        ${message}
      </div>
    `);

        $("body").append(messageEl);

        setTimeout(() => {
            messageEl.fadeOut(() => messageEl.remove());
        }, 3000);
    }

    onModalOpen() {
        console.log("Modal opened:", this.currentModal?.id);
    }

    onModalClose() {
        console.log("Modal closed:", this.currentModal?.id);
        this.currentModal = null;
        this.onClose();
    }

    // Public methods
    getResponses() {
        return this.responses;
    }

    clearResponses() {
        this.responses = {};
    }

    showModalById(modalId) {
        const modal = this.config.modals.find((m) => m.id === modalId);
        if (modal) {
            this.showModal(modal);
        }
    }
}

// Export for use
window.InteractionModal = InteractionModal;
