function FakeSeeding(options = {}) {
    // Create container if not exists
    this.containerId = options.containerId || "noti-seeding";
    this.container = $("#" + this.containerId);
    if (this.container.length === 0) {
        $("body").append(
            `<div class="noti-seeding" id="${this.containerId}"></div>`
        );
        this.container = $("#" + this.containerId);
    }

    // Configuration
    this.showDuration = options.showDuration || 3000; // How long notifications stay visible
    this.minInterval = options.minInterval || 3000; // Minimum time between notifications
    this.maxInterval = options.maxInterval || 10000; // Maximum time between notifications
    this.messages = {
        qr: {
            text:
                options.seedingMessages.qr ||
                "Chúc mừng bạn vừa chuyển khoản thành công!",
            badge: null,
            type: "qr",
        },
        form: {
            text: options.seedingMessages.form || "<PERSON><PERSON> đăng ký nhận tư vấn",
            badge: null,
            type: "form",
        },
        url: {
            text: options.seedingMessages.url || "",
            badge: null,
            type: "url",
        },
    };

    // Data
    this.firstNames = [
        "nguyen",
        "hoang",
        "bui",
        "nguyen4",
        "le",
        "pham",
        "luu",
        "nguyen2",
        "luong",
        "pham2",
        "tran",
        "ngo",
        "vuong",
        "vu",
        "ly",
        "phi",
        "ha",
        "doan",
        "la",
        "nguyen3",
        "ngo3",
        "nguyen11",
        "phan",
        "dang2",
        "vu2",
        "vi",
        "nguyen7",
        "phung",
        "dang",
        "nguyen12",
        "to",
        "duong",
        "mai",
        "nguyen5",
        "do",
        "le2",
        "ninh",
        "Nguyen5",
        "nguyen13",
        "ho",
        "ta",
        "nguyen6",
        "ngoc",
        "nguyen14",
        "hoang2",
        "ha2",
    ];

    this.lastNames = [
        "huyentrang",
        "ngoc",
        "hoa",
        "anh",
        "huong",
        "nam",
        "viet",
        "thuy",
        "hanh",
        "giang",
        "ha",
        "hung",
        "ninh",
        "loc",
        "mai",
        "tan",
        "thoa",
        "trang",
        "bich",
        "ngan",
        "phuong",
        "tung",
        "nghia",
        "minh",
        "thanh",
        "hai",
        "duyen",
        "thuong",
        "thinh",
        "duy",
        "trung",
        "rosie",
        "vui",
        "manh",
        "khai",
        "emma",
        "tuan",
        "dang",
        "ty",
        "huyen",
        "luc",
        "tien",
        "yen",
        "thang",
        "hang",
        "toan",
        "cuong",
        "nhan",
        "an",
        "thien",
        "duong",
        "binh",
        "Tuan",
        "chung",
        "chi",
        "sen",
        "tam",
        "vu",
        "quyet",
        "duc",
        "Huyen",
        "giap",
        "uyen",
        "thai",
        "thao",
        "kien",
        "Ban",
        "hien",
        "hoi",
        "truong",
        "dung",
        "my",
        "nga",
        "luu",
        "linh",
        "khanh",
        "xuan",
    ];

    this.domains = [
        "outlook.com",
        "gmail.com",
        "hotmail.com",
        "mail.com",
        "icloud.com",
    ];

    this.avatarColors = ["purple", "blue", "green"];

    this.badges = [{ type: "pro", text: "Học viên Pro", class: "badge-pro" }];
}

// Prototype methods
FakeSeeding.prototype = {
    // Generate random email
    generateRandomEmail: function () {
        const firstName =
            this.firstNames[Math.floor(Math.random() * this.firstNames.length)];
        const lastName =
            this.lastNames[Math.floor(Math.random() * this.lastNames.length)];
        const domain =
            this.domains[Math.floor(Math.random() * this.domains.length)];

        // Create email with masked characters
        const fullName = firstName + lastName;
        const firstChar = fullName.charAt(0);

        // Show first 2-3 characters then mask with asterisks
        const visibleLength = Math.floor(Math.random() * 2) + 2; // 2-3 visible chars
        const maskedEmail =
            fullName.substring(0, visibleLength) + "***@" + domain;

        return {
            firstChar: firstChar,
            email: maskedEmail,
        };
    },

    // Generate random phone number with prefix 09 or 03
    generateRandomPhoneNumber: function () {
        const prefixes = [
            "039",
            "038",
            "037",
            "036",
            "035",
            "034",
            "033",
            "032",
            "031",
            "030",
            "090",
            "091",
            "092",
            "093",
            "094",
            "095",
            "096",
            "097",
            "098",
            "099",
        ];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];

        // Generate 8 random digits for the remaining part
        let suffix = "";
        for (let i = 0; i < 8; i++) {
            suffix += Math.floor(Math.random() * 10);
        }

        const phoneNumber = prefix + suffix;

        // Create masked version (show first 3 digits, mask middle, show last 3)
        const maskedPhone =
            phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);

        return {
            fullNumber: phoneNumber,
            maskedNumber: maskedPhone,
        };
    },

    // Show a single notification, type: form/qr
    showNotification: function (popupType = "form") {
        const email = this.generateRandomEmail();
        const phone = this.generateRandomPhoneNumber();
        const messageObj = this.messages[popupType] || this.messages.form; // Default to form if type not found
        if (!messageObj.text) return;

        const avatarColor =
            this.avatarColors[
                Math.floor(Math.random() * this.avatarColors.length)
            ];
        const badgeObj = messageObj.badge
            ? this.badges.find((b) => b.type === messageObj.badge)
            : null;

        const notification = $('<div class="notification"></div>');

        // Create avatar
        const avatar = $(
            `<div class="avatar avatar-${avatarColor}">${email.firstChar.toUpperCase()}</div>`
        );

        // Create content
        const content = $('<div class="content"></div>');
        const emailElement = $(
            '<div class="email" style="color: #000;"></div>'
        ).html(`<div>${phone.maskedNumber}</div>`);

        // Create message with optional badge
        const messageElement = $('<div class="message"></div>').text(
            messageObj.text
        );

        // Append elements
        content.append(emailElement);
        content.append(messageElement);

        // Add badge if exists
        if (badgeObj) {
            const badgeElement = $(
                `<span class="badge-seeding ${badgeObj.class}">${badgeObj.text}</span>`
            );
            emailElement.append(badgeElement);
        }

        notification.append(avatar);
        notification.append(content);

        this.container.prepend(notification);

        // Show with animation
        setTimeout(() => {
            notification.addClass("show");
        }, 10);

        // Hide and remove after duration
        setTimeout(() => {
            notification.removeClass("show");
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, this.showDuration);
    },
};

window.FakeSeeding = FakeSeeding;

// Example usage
// const seedingUtil = new FakeSeeding();
// seedingUtil.showNotification("form");
// seedingUtil.showNotification("qr");
