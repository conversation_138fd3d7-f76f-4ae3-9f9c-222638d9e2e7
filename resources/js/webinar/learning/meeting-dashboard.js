// Meeting Dashboard Real-time System with AJAX
class MeetingDashboard {
    constructor() {
        this.currentQuestion = null;
        this.responseChart = null;
        this.totalParticipants = 156;
        this.responseStream = [];
        this.realtimeInterval = null;
        this.currentDemoType = 'multiple-choice';
        this.updateInterval = null;
        this.api = window.meetingAPI; // Reference to AJAX API

        this.questionIcons = {
            'multiple-choice': 'fas fa-list',
            'single-choice': 'fas fa-list',
            'rating': 'fas fa-star',
            'open-survey': 'fas fa-edit'
        };

        this.init();
    }

    async init() {
        this.initializeChart();
        this.startHeartbeat();
        this.simulateOnlineParticipants();

        // Load initial meeting data from API
        await this.loadMeetingInfo();

        // Start with first demo question
        await this.switchDemoQuestion('multiple-choice');

        // Start real-time updates
        this.startRealtimeUpdates();
    }

    async switchDemoQuestion(type) {
        try {
            // Show loading state
            this.showLoading(true);

            // Call API to switch question
            const response = await this.api.switchQuestionType(type);

            if (response.success) {
                this.currentDemoType = type;
                this.currentQuestion = response.data;
                this.currentQuestion.icon = this.questionIcons[type];

                // Update all displays
                await this.refreshAllData();

                this.showNotification('Đã chuyển đổi loại câu hỏi thành công!', 'success');
            } else {
                this.showNotification('Lỗi khi chuyển đổi câu hỏi: ' + response.error, 'danger');
            }
        } catch (error) {
            console.error('Error switching question:', error);
            this.showNotification('Lỗi kết nối khi chuyển đổi câu hỏi!', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    initializeChart() {
        const ctx = document.getElementById('realtimeChart').getContext('2d');
        this.responseChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Số phản hồi',
                    data: [],
                    backgroundColor: [
                        'rgba(79, 70, 229, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(139, 69, 19, 0.8)'
                    ],
                    borderColor: [
                        'rgba(79, 70, 229, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(139, 69, 19, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                animation: {
                    duration: 500
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // Load meeting information from API
    async loadMeetingInfo() {
        try {
            const response = await this.api.getMeetingInfo();
            if (response.success) {
                const meetingData = response.data;
                this.totalParticipants = meetingData.totalParticipants;

                // Update meeting header
                $('.meeting-header h3').text(`Meeting: ${meetingData.title}`);
                $('.meeting-header p').text(`Chủ đề: ${meetingData.topic}`);
                $('#totalParticipants').text(meetingData.onlineParticipants);
            }
        } catch (error) {
            console.error('Error loading meeting info:', error);
        }
    }

    // Refresh all data from API
    async refreshAllData() {
        try {
            // Load current question
            await this.loadCurrentQuestion();

            // Load response stream
            await this.loadResponseStream();

            // Load statistics
            await this.loadStatistics();

            // Update displays
            this.updateCurrentQuestionInfo();
            this.updateChart();
            this.updateResponseDetails();
            this.updateResponseStream();

        } catch (error) {
            console.error('Error refreshing data:', error);
        }
    }

    // Load current question from API
    async loadCurrentQuestion() {
        try {
            const response = await this.api.getCurrentQuestion();
            if (response.success && response.data) {
                this.currentQuestion = response.data;
                this.currentQuestion.icon = this.questionIcons[this.currentQuestion.type];
            }
        } catch (error) {
            console.error('Error loading current question:', error);
        }
    }

    // Load response stream from API
    async loadResponseStream() {
        try {
            const response = await this.api.getResponseStream();
            if (response.success) {
                this.responseStream = response.data;
            }
        } catch (error) {
            console.error('Error loading response stream:', error);
        }
    }

    // Load statistics from API
    async loadStatistics() {
        try {
            const response = await this.api.getStatistics();
            if (response.success) {
                const stats = response.data;
                $('#totalResponses').text(stats.totalResponses);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    // Start real-time updates
    startRealtimeUpdates() {
        // Stop existing interval
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        this.updateInterval = setInterval(async () => {
            try {
                // Simulate new responses on backend
                const newResponse = this.api.simulateResponse();

                if (newResponse) {
                    // Refresh data from API
                    await this.loadResponseStream();
                    await this.loadStatistics();

                    // Update current question data
                    await this.loadCurrentQuestion();

                    // Update displays
                    this.updateChart();
                    this.updateResponseDetails();
                    this.updateResponseStream();
                }

            } catch (error) {
                console.error('Error in real-time update:', error);
            }
        }, 1500); // Update every 1.5 seconds
    }

    // Show/hide loading state
    showLoading(show) {
        if (show) {
            // Add loading spinner to charts and data areas
            $('.realtime-chart').addClass('loading-state');
            $('#demoQuestionType').prop('disabled', true);
        } else {
            $('.realtime-chart').removeClass('loading-state');
            $('#demoQuestionType').prop('disabled', false);
        }
    }

    startQuestion() {
        // Show active question panel
        document.getElementById('activeQuestionPanel').style.display = 'block';
        document.getElementById('activeQuestionText').textContent = this.currentQuestion.text;

        // Update chart
        this.updateChart();

        // Start timer
        this.startQuestionTimer();

        // Clear previous response details
        this.updateResponseDetails();

        // Reset counters
        document.getElementById('totalResponses').textContent = '0';
        document.getElementById('currentResponses').textContent = '0';
    }

    startQuestionTimer() {
        let timeLeft = this.currentQuestion.duration;
        const totalTime = this.currentQuestion.duration;

        this.questionTimer = setInterval(() => {
            timeLeft--;

            // Update display
            document.getElementById('timeRemaining').textContent = `${timeLeft}s`;
            const progressPercent = (timeLeft / totalTime) * 100;
            document.getElementById('timeProgress').style.width = `${progressPercent}%`;

            // Change color when time is running out
            if (timeLeft <= 10) {
                document.getElementById('timeProgress').className = 'progress-bar bg-danger';
                document.getElementById('timeRemaining').className = 'badge bg-danger';
            } else if (timeLeft <= 30) {
                document.getElementById('timeProgress').className = 'progress-bar bg-warning';
                document.getElementById('timeRemaining').className = 'badge bg-warning';
            }

            if (timeLeft <= 0) {
                this.endQuestion();
            }
        }, 1000);
    }

    endQuestion() {
        if (this.questionTimer) {
            clearInterval(this.questionTimer);
            this.questionTimer = null;
        }

        // Hide active question panel
        document.getElementById('activeQuestionPanel').style.display = 'none';

        // Save to history
        this.questionHistory.push({
            ...this.currentQuestion,
            endTime: Date.now(),
            totalResponses: this.getTotalResponses()
        });

        this.showNotification('Câu hỏi đã kết thúc! Kết quả đã được lưu.', 'info');

        // Clear form
        this.clearQuestionForm();

        this.currentQuestion = null;
    }

    simulateStudentResponses() {
        if (!this.currentQuestion) return;

        // Simulate responses coming in over time
        const responseInterval = setInterval(() => {
            if (!this.currentQuestion) {
                clearInterval(responseInterval);
                return;
            }

            // Random number of new responses (1-5)
            const newResponses = Math.floor(Math.random() * 5) + 1;

            for (let i = 0; i < newResponses; i++) {
                // Random option selection
                const randomOption = this.currentQuestion.options[
                    Math.floor(Math.random() * this.currentQuestion.options.length)
                ];

                this.currentQuestion.responses[randomOption]++;

                // Add to response stream
                this.addToResponseStream(randomOption);
            }

            // Update displays
            this.updateChart();
            this.updateResponseDetails();
            this.updateCounters();

        }, 2000); // New responses every 2 seconds

        // Stop simulation when question ends
        setTimeout(() => {
            clearInterval(responseInterval);
        }, this.currentQuestion.duration * 1000);
    }

    updateCurrentQuestionInfo() {
        const container = document.getElementById('currentQuestionInfo');
        const question = this.currentQuestion;

        container.innerHTML = `
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${question.icon} text-primary me-2"></i>
                        <h6 class="mb-0">${this.getQuestionTypeName(this.currentDemoType)}</h6>
                        <span class="badge bg-success ms-auto">
                            <i class="fas fa-circle me-1"></i>Real-time
                        </span>
                    </div>
                    <p class="mb-0">${question.text}</p>
                </div>
            </div>
        `;
    }

    addToResponseStream(answer) {
        const timestamp = new Date().toLocaleTimeString('vi-VN');
        const studentNames = ['HS001', 'HS023', 'HS045', 'HS067', 'HS089', 'HS102', 'HS125', 'HS143', 'HS156', 'HS178', 'HS199', 'HS200'];
        const studentId = studentNames[Math.floor(Math.random() * studentNames.length)];

        this.responseStream.unshift({
            studentId,
            answer,
            timestamp,
            type: this.currentDemoType
        });

        // Keep only last 15 responses
        if (this.responseStream.length > 15) {
            this.responseStream = this.responseStream.slice(0, 15);
        }

        this.updateResponseStream();
    }

    updateResponseStream() {
        const container = document.getElementById('responseStream');
        container.innerHTML = '';

        if (this.responseStream.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                    <p>Đang chờ phản hồi từ học sinh...</p>
                </div>
            `;
            return;
        }

        this.responseStream.forEach((response, index) => {
            const item = document.createElement('div');
            item.className = 'response-item';
            item.style.animationDelay = `${index * 0.1}s`;

            // Different badge colors for different answer types
            const badgeClass = this.getBadgeClassForAnswer(response.answer, response.type);

            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${response.studentId}</strong> đã chọn:
                        <span class="badge ${badgeClass}">${response.answer}</span>
                    </div>
                    <small class="text-muted">${response.timestamp}</small>
                </div>
            `;
            container.appendChild(item);
        });
    }

    getBadgeClassForAnswer(answer, type) {
        // Return different colors for variety, no indication of correctness
        const colors = ['bg-primary', 'bg-success', 'bg-warning', 'bg-info', 'bg-secondary'];
        const hash = answer.length % colors.length;
        return colors[hash];
    }

    updateChart() {
        if (!this.currentQuestion || !this.responseChart) return;

        const labels = this.currentQuestion.options;
        const data = labels.map(option => this.currentQuestion.responses[option]);

        this.responseChart.data.labels = labels;
        this.responseChart.data.datasets[0].data = data;
        this.responseChart.update('none');
    }

    updateResponseDetails() {
        if (!this.currentQuestion) return;

        const container = document.getElementById('responseDetails');
        const total = this.getTotalResponses();

        if (total === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-hourglass-half fa-3x mb-3"></i>
                    <p>Đang chờ học sinh trả lời...</p>
                </div>
            `;
            return;
        }

        let html = '<div class="row">';

        this.currentQuestion.options.forEach((option, index) => {
            const count = this.currentQuestion.responses[option];
            const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;

            const colors = ['primary', 'success', 'warning', 'danger', 'info'];
            const color = colors[index % colors.length];

            html += `
                <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="mb-2">${option}</h6>
                            <div class="response-counter text-${color}">${count}</div>
                            <span class="badge bg-${color} mb-2">${percentage}%</span>
                            <div class="progress mt-2" style="height: 8px;">
                                <div class="progress-bar bg-${color}" style="width: ${percentage}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';

        // Add participation stats
        const participationRate = ((total / this.totalParticipants) * 100).toFixed(1);
        html += `
            <div class="mt-3 p-3 bg-light rounded">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="text-primary fw-bold">${total}</div>
                        <small class="text-muted">Tổng phản hồi</small>
                    </div>
                    <div class="col-md-4">
                        <div class="text-success fw-bold">${participationRate}%</div>
                        <small class="text-muted">Tỷ lệ tham gia</small>
                    </div>
                    <div class="col-md-4">
                        <div class="text-info fw-bold">${(Math.random() * 3 + 1).toFixed(1)}s</div>
                        <small class="text-muted">Thời gian phản hồi TB</small>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }



    updateTotalResponses() {
        const total = this.getTotalResponses();
        document.getElementById('totalResponses').textContent = total;
    }

    updateCounters() {
        const totalResponses = this.getTotalResponses();
        document.getElementById('totalResponses').textContent = totalResponses;
        document.getElementById('currentResponses').textContent = totalResponses;
    }

    getTotalResponses() {
        if (!this.currentQuestion) return 0;
        return Object.values(this.currentQuestion.responses).reduce((sum, count) => sum + count, 0);
    }

    clearQuestionForm() {
        document.getElementById('questionText').value = '';
        document.getElementById('questionType').selectedIndex = 0;
        document.getElementById('questionTimer').selectedIndex = 1;
        this.handleQuestionTypeChange('multiple-choice');
    }

    simulateOnlineParticipants() {
        // Simulate participants joining/leaving
        setInterval(() => {
            const change = Math.floor(Math.random() * 6) - 3; // -3 to +3
            this.totalParticipants = Math.max(100, Math.min(200, this.totalParticipants + change));
            document.getElementById('totalParticipants').textContent = this.totalParticipants;
        }, 10000);
    }

    startHeartbeat() {
        // Simulate connection heartbeat
        setInterval(() => {
            const indicator = document.querySelector('.live-indicator');
            indicator.style.opacity = '0.5';
            setTimeout(() => {
                indicator.style.opacity = '1';
            }, 200);
        }, 3000);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show notification-badge`;
        notification.innerHTML = `
            <i class="fas fa-${this.getIconForType(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 4000);
    }

    getIconForType(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }



    getQuestionTypeName(type) {
        const typeNames = {
            'single-choice': 'Trắc nghiệm 1 đáp án',
            'multiple-choice': 'Trắc nghiệm nhiều đáp án',
            'rating': 'Đánh giá thang điểm',
            'open-survey': 'Tự luận'
        };
        return typeNames[type] || type;
    }



    exportResults() {
        if (!this.currentQuestion && this.questionHistory.length === 0) {
            this.showNotification('Không có dữ liệu để xuất!', 'warning');
            return;
        }

        // Create CSV data
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Câu hỏi,Loại,Lựa chọn,Số phản hồi,Phần trăm,Thời gian\n";

        const questionsToExport = this.currentQuestion ?
            [...this.questionHistory, this.currentQuestion] :
            this.questionHistory;

        questionsToExport.forEach(question => {
            const total = Object.values(question.responses).reduce((sum, count) => sum + count, 0);
            question.options.forEach(option => {
                const count = question.responses[option];
                const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;
                const time = new Date(question.startTime).toLocaleString('vi-VN');
                csvContent += `"${question.text}","${question.type}","${option}",${count},${percentage}%,"${time}"\n`;
            });
        });

        // Download CSV
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `meeting-results-${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showNotification('Đã xuất kết quả thành công!', 'success');
    }
}

// Global functions
function addOption() {
    const optionsList = document.getElementById('optionsList');
    const newInput = document.createElement('input');
    newInput.type = 'text';
    newInput.className = 'form-control mb-2';
    newInput.placeholder = `Lựa chọn ${optionsList.children.length + 1}`;
    optionsList.appendChild(newInput);
}

function sendQuestionToStudents() {
    meetingDashboard.sendQuestionToStudents();
}

function endQuestion() {
    meetingDashboard.endQuestion();
}

function exportResults() {
    meetingDashboard.exportResults();
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function saveSession() {
    const sessionData = {
        timestamp: new Date().toISOString(),
        questionHistory: meetingDashboard.questionHistory,
        currentQuestion: meetingDashboard.currentQuestion
    };

    localStorage.setItem('meeting-session', JSON.stringify(sessionData));
    meetingDashboard.showNotification('Đã lưu phiên họp!', 'success');
}

// Initialize
let meetingDashboard;
document.addEventListener('DOMContentLoaded', function() {
    meetingDashboard = new MeetingDashboard();
});
