// AJAX Handler for Meeting Dashboard
// This simulates backend API calls for easy integration later

class MeetingAPI {
    constructor() {
        this.baseUrl = '/api/meeting'; // Base API URL
        this.isSimulated = true; // Set to false when connecting to real backend
        this.simulatedData = this.initializeSimulatedData();
        this.responseDelay = 500; // Simulate network delay
    }

    initializeSimulatedData() {
        return {
            meeting: {
                id: 'meeting_123',
                title: 'Lớp Tiếng Anh 10A1',
                topic: 'Past Tense & Vocabulary',
                status: 'active',
                startTime: new Date().toISOString(),
                totalParticipants: 156,
                onlineParticipants: 156
            },
            currentQuestion: null,
            questions: {
                'multiple-choice': {
                    id: 'q_1',
                    type: 'multiple-choice',
                    text: 'Which word has the same meaning as "beautiful"?',
                    options: ['Ugly', 'Pretty', 'Sad', 'Fast'],
                    responses: {'Ugly': 0, 'Pretty': 0, 'Sad': 0, 'Fast': 0},
                    startTime: null,
                    endTime: null,
                    isActive: false
                },
                'true-false': {
                    id: 'q_2',
                    type: 'true-false',
                    text: 'The sentence "She go to school every day" is grammatically correct.',
                    options: ['True', 'False'],
                    responses: {'True': 0, 'False': 0},
                    startTime: null,
                    endTime: null,
                    isActive: false
                },
                'poll': {
                    id: 'q_3',
                    type: 'poll',
                    text: 'What is your favorite way to practice English?',
                    options: ['Watching movies', 'Reading books', 'Listening to music', 'Speaking with friends'],
                    responses: {'Watching movies': 0, 'Reading books': 0, 'Listening to music': 0, 'Speaking with friends': 0},
                    startTime: null,
                    endTime: null,
                    isActive: false
                },
                'rating': {
                    id: 'q_4',
                    type: 'rating',
                    text: 'How difficult was today\'s English lesson?',
                    options: ['1 star', '2 stars', '3 stars', '4 stars', '5 stars'],
                    responses: {'1 star': 0, '2 stars': 0, '3 stars': 0, '4 stars': 0, '5 stars': 0},
                    startTime: null,
                    endTime: null,
                    isActive: false
                },
                'quick-poll': {
                    id: 'q_5',
                    type: 'quick-poll',
                    text: 'Do you understand today\'s lesson about past tense?',
                    options: ['Yes', 'No'],
                    responses: {'Yes': 0, 'No': 0},
                    startTime: null,
                    endTime: null,
                    isActive: false
                }
            },
            responseStream: [],
            statistics: {
                totalQuestions: 0,
                totalResponses: 0,
                participationRate: 0,
                averageResponseTime: 0
            }
        };
    }

    // Simulate AJAX call
    async makeRequest(endpoint, method = 'GET', data = null) {
        if (this.isSimulated) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve(this.handleSimulatedRequest(endpoint, method, data));
                }, this.responseDelay);
            });
        } else {
            // Real AJAX call - replace with actual implementation
            return $.ajax({
                url: this.baseUrl + endpoint,
                method: method,
                data: data,
                dataType: 'json'
            });
        }
    }

    handleSimulatedRequest(endpoint, method, data) {
        switch (endpoint) {
            case '/meeting-info':
                return {
                    success: true,
                    data: this.simulatedData.meeting
                };

            case '/switch-question':
                if (method === 'POST' && data && data.questionType) {
                    return this.switchQuestion(data.questionType);
                }
                break;

            case '/current-question':
                return {
                    success: true,
                    data: this.simulatedData.currentQuestion
                };

            case '/response-stream':
                return {
                    success: true,
                    data: this.simulatedData.responseStream.slice(0, 15)
                };

            case '/statistics':
                return {
                    success: true,
                    data: this.calculateStatistics()
                };

            case '/participants':
                return {
                    success: true,
                    data: {
                        total: this.simulatedData.meeting.totalParticipants,
                        online: this.simulatedData.meeting.onlineParticipants
                    }
                };

            default:
                return {
                    success: false,
                    error: 'Endpoint not found'
                };
        }
    }

    switchQuestion(questionType) {
        const question = this.simulatedData.questions[questionType];
        if (!question) {
            return {
                success: false,
                error: 'Question type not found'
            };
        }

        // Reset previous question
        if (this.simulatedData.currentQuestion) {
            this.simulatedData.currentQuestion.isActive = false;
            this.simulatedData.currentQuestion.endTime = new Date().toISOString();
        }

        // Set new question
        question.startTime = new Date().toISOString();
        question.isActive = true;
        
        // Reset responses
        question.options.forEach(option => {
            question.responses[option] = 0;
        });

        this.simulatedData.currentQuestion = question;
        this.simulatedData.responseStream = []; // Clear response stream

        return {
            success: true,
            data: question
        };
    }

    calculateStatistics() {
        const current = this.simulatedData.currentQuestion;
        if (!current) {
            return {
                totalResponses: 0,
                participationRate: 0,
                averageResponseTime: 0
            };
        }

        const totalResponses = Object.values(current.responses).reduce((sum, count) => sum + count, 0);
        const participationRate = (totalResponses / this.simulatedData.meeting.totalParticipants * 100).toFixed(1);

        return {
            totalResponses: totalResponses,
            participationRate: parseFloat(participationRate),
            averageResponseTime: (Math.random() * 3 + 1).toFixed(1)
        };
    }

    // Simulate real-time response updates
    simulateResponse() {
        if (!this.simulatedData.currentQuestion || !this.simulatedData.currentQuestion.isActive) {
            return null;
        }

        const question = this.simulatedData.currentQuestion;
        const weights = this.getResponseWeights(question.type);
        const randomOption = this.getWeightedRandomOption(question.options, weights);
        
        // Update response count
        question.responses[randomOption]++;

        // Add to response stream
        const studentNames = ['HS001', 'HS023', 'HS045', 'HS067', 'HS089', 'HS102', 'HS125', 'HS143', 'HS156', 'HS178', 'HS199', 'HS200'];
        const newResponse = {
            id: Date.now() + Math.random(),
            studentId: studentNames[Math.floor(Math.random() * studentNames.length)],
            answer: randomOption,
            timestamp: new Date().toLocaleTimeString('vi-VN'),
            questionId: question.id
        };

        this.simulatedData.responseStream.unshift(newResponse);
        
        // Keep only last 15 responses
        if (this.simulatedData.responseStream.length > 15) {
            this.simulatedData.responseStream = this.simulatedData.responseStream.slice(0, 15);
        }

        return newResponse;
    }

    getResponseWeights(questionType) {
        const weights = {
            'multiple-choice': [0.15, 0.45, 0.25, 0.15],
            'true-false': [0.35, 0.65],
            'poll': [0.35, 0.25, 0.25, 0.15],
            'rating': [0.05, 0.15, 0.45, 0.25, 0.1],
            'quick-poll': [0.75, 0.25]
        };
        return weights[questionType] || [];
    }

    getWeightedRandomOption(options, weights) {
        const random = Math.random();
        let sum = 0;
        
        for (let i = 0; i < options.length; i++) {
            sum += weights[i] || (1 / options.length);
            if (random <= sum) {
                return options[i];
            }
        }
        return options[0];
    }

    // API Methods for frontend to call
    async getMeetingInfo() {
        return await this.makeRequest('/meeting-info');
    }

    async switchQuestionType(questionType) {
        return await this.makeRequest('/switch-question', 'POST', { questionType });
    }

    async getCurrentQuestion() {
        return await this.makeRequest('/current-question');
    }

    async getResponseStream() {
        return await this.makeRequest('/response-stream');
    }

    async getStatistics() {
        return await this.makeRequest('/statistics');
    }

    async getParticipants() {
        return await this.makeRequest('/participants');
    }

    // Export current state (for debugging or real backend sync)
    exportData() {
        return JSON.stringify(this.simulatedData, null, 2);
    }

    // Import data (for testing with specific data)
    importData(jsonData) {
        try {
            this.simulatedData = JSON.parse(jsonData);
            return true;
        } catch (e) {
            console.error('Failed to import data:', e);
            return false;
        }
    }
}

// Initialize API instance
window.meetingAPI = new MeetingAPI(); 