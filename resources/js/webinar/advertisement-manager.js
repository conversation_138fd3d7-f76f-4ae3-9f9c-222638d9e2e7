/**
 * Advertisement Manager for webinar platform
 * Handles displaying advertisements, seeding, and payment flows
 */
class AdvertisementManager {
    constructor(modalId, advertisements, webinarInfo, options = {}) {
        // Configuration
        this.advertisements = (advertisements || []).map((item, index) => ({
            ...item,
            detail: {
                ...item.detail,
                id: item?.detail?.id || new Date().getTime() + index,
                quantity:
                    item?.type === "image"
                        ? 9999999999
                        : item?.detail?.quantity || 0,
                totalSold:
                    item?.type === "image" ? 0 : item?.detail?.totalSold || 0,
            },
        }));
        this.isMobile = window.innerWidth < 768;
        this.webinarInfo = webinarInfo;
        this.isManualMode = options.isManualMode || false; // Flag for manual mode
        this.options = {
            seedingInterval: options.seedingInterval || { min: 6, max: 20 },
            paymentCheckInterval: options.paymentCheckInterval || 5000,
            paymentTimeout: options.paymentTimeout || 15 * 60, // 15 minutes in seconds
            showSeeding: options.showSeeding || true,
            ...options,
        };

        // State
        this.currentAd = null;
        this.currentAdTimer = null;
        this.seedingTimer = null;
        this.paymentCheckTimer = null;
        this.countdownTimer = null;
        this.paymentCountdownTimer = null;

        // DOM elements
        this.modal = $(modalId);
        this.offerModal = new ModalUtil(modalId);

        // Cache all DOM elements for better performance
        this.el = {
            // Modal forms
            registerForm: this.modal.find(".registerForm"),
            paymentForm: this.modal.find(".paymentForm"),
            submitFormButton: this.modal.find(".submitFormButton"),
            redirectUrlButton: this.modal.find(".redirectUrlButton"),

            // Buttons
            reopenPopupButton: $("#reopenPopupButton"),
            closePopupButton: this.modal.find(".closePopupButton"),
            reopenOffer: $("#reopenOffer"),

            // Form elements
            form: this.modal.find(".registerForm form"),
            fullName: this.modal.find(".fullName"),
            phoneNumber: this.modal.find(".phoneNumber"),

            // Product display elements
            modalTitle: this.modal.find(".modal-title"),
            modalImageProduct: this.modal.find(".modal-image-product"),
            modalImageImg: this.modal.find(".modal-image-img"),
            modalDescription: this.modal.find(".modal-description"),
            modalSubdescription: this.modal.find(".modal-subdescription"),
            modalPrice: this.modal.find(".modal-price"),
            modalOldPrice: this.modal.find(".modal-old-price"),
            modalDiscount: this.modal.find(".modal-discount"),
            modalTotalSold: this.modal.find(".modal-total-sold"),
            modalRemaining: this.modal.find(".modal-remaining"),

            // Ad type containers
            productTypeAd: this.modal.find(".product-type-ad"),
            imageTypeAd: this.modal.find(".image-type-ad"),

            // Payment elements
            bankTransactionContent: this.modal.find(
                ".bank_transaction_content"
            ),
            bankTotalAmount: this.modal.find(".bank_total_amount"),
            imgQrCode: this.modal.find(".img-qr-code"),
            timerContainer: this.modal.find(".timer-container"),
            timer: this.modal.find(".timer"),
            statusContainer: this.modal.find(".status-container"),

            // Countdown elements
            adCountdown: $("#adCountdown"),
        };

        this.seedingUtil = new FakeSeeding({
            seedingMessages: {
                qr: "Vừa thanh toán thành công",
                form: "Vừa gửi form đăng ký thành công",
            },
        });

        // Storage key prefix
        this.storageKeyPrefix = `webinar_ad_${this.webinarInfo?.webinarId}}`;

        // Initialize
        this.initialize();
    }

    /**
     * Initialize the advertisement manager
     */
    initialize() {
        // Initialize event handlers
        this.initializeEventHandlers();

        // Load product data from localStorage
        this.loadProductData();
    }

    /**
     * Set up event handlers for the UI
     */
    initializeEventHandlers() {
        // Reopen popup button handler
        this.el.reopenPopupButton.on("click", () => {
            if (this.currentAd) {
                this.offerModal.open();
            }
        });

        // Close popup button handler
        this.el.closePopupButton.on("click", () => {
            this.offerModal.close();
            // Save closed ad code to sessionStorage
            sessionStorage.setItem("lastClosedAdCode", this.lastClosedAdCode);

            // Show the reopen button if an ad is active
            if (this.currentAd) {
                this.el.reopenOffer.show();
            }
        });

        // Form submission handler
        this.el.form.on("submit", (e) => {
            e.preventDefault();
            this.handleFormSubmission();
        });
    }

    /**
     * Check if an advertisement should be shown at the current time
     * @param {string} currentTime - Current video time in HH:mm:ss format
     */
    checkForAdvertisements(currentTime) {
        // If there's already an active ad, check if it should be closed
        if (this.currentAd) {
            const elapsedTime =
                this.getTimeInSeconds(currentTime) -
                this.getTimeInSeconds(this.currentAd.at);

            // If the ad duration has passed, close it
            if (elapsedTime >= this.currentAd.showIn) {
                this.closeCurrentAd();
            }
            return;
        }

        // Check if any ad should be shown at the current time
        for (const ad of this.advertisements) {
            if (currentTime === ad.at) {
                this.showAdvertisement(ad);
                break;
            }
        }
    }

    /**
     * Display an advertisement
     * @param {Object} ad - The advertisement object to display
     */
    showAdvertisement(ad) {
        this.currentAd = ad;

        // Update product info in the modal
        this.updateProductInfo(ad);
        // Open the modal
        this.offerModal.open();

        // Show the appropriate form based on ad order_method
        switch (ad.order_method) {
            case "form":
                this.el.registerForm.show();
                this.el.paymentForm.hide();
                this.el.submitFormButton.text("Nhận tư vấn");
                this.el.redirectUrlButton.hide();
                break;
            case "qr":
                this.el.registerForm.show();
                this.el.paymentForm.hide();
                this.el.submitFormButton.text("Thanh toán ngay");
                this.el.redirectUrlButton.hide();
                break;
            case "url":
            default:
                this.el.registerForm.show();
                this.el.paymentForm.hide();
                this.el.form.hide();
                this.el.redirectUrlButton.show();
                this.el.redirectUrlButton.attr(
                    "href",
                    ad.redirect_url_ads || "#"
                );
                break;
        }

        this.el.submitFormButton
            .prop("disabled", false)
            .removeClass("bg-red-500");

        if (ad?.type === "product") {
            this.el.productTypeAd.show();
            this.el.imageTypeAd.hide();
        } else if (ad?.type === "image") {
            this.el.imageTypeAd.show();
            this.el.productTypeAd.hide();
        } else {
            //
        }

        // Reset all fields in the form
        this.el.form.trigger("reset");
        // Start seeding
        if (!this.isMobile && this.options.showSeeding) {
            this.startSeeding();
        }

        // Start countdown timer for the reopen button (only for non-manual mode)
        if (ad.showIn) {
            this.startAdCountdown(ad.showIn);
        }

        // Show the reopen button
        this.el.reopenOffer.show();
        this.el.reopenPopupButton.show();
    }

    /**
     * Close the current advertisement
     */
    closeCurrentAd() {
        // Clear timers
        if (this.seedingTimer) {
            clearTimeout(this.seedingTimer);
            this.seedingTimer = null;
        }

        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }

        if (this.paymentCheckTimer) {
            clearInterval(this.paymentCheckTimer);
            this.paymentCheckTimer = null;
        }

        if (this.paymentCountdownTimer) {
            clearInterval(this.paymentCountdownTimer);
            this.paymentCountdownTimer = null;
        }

        // Close the modal
        // this.offerModal.close();

        this.el.submitFormButton.prop("disabled", true).addClass("bg-red-500");

        // Hide the reopen button
        this.el.reopenOffer.hide();

        // Reset current ad
        this.currentAd = null;
    }

    /**
     * Handle form submission
     */
    async handleFormSubmission() {
        try {
            this.el.submitFormButton
                .prop("disabled", true)
                .text("Đang xử lý...");

            const fullName = this.el.fullName.val();
            const phoneNumber = this.el.phoneNumber.val();

            // Validate form
            if (!fullName || !phoneNumber) {
                alert("Vui lòng điền đầy đủ thông tin!");
                return;
            }
            const formData = {
                is_live: webinarConfig.isLivestream ? 1 : 0,
                webinar_id: this.webinarInfo.id,
                advertisement_id: this.currentAd.id,
                order_type: this.currentAd.order_method,
                payment_note: "noted",
                name: fullName,
                phone: phoneNumber,
                email: "",
            };

            const { data: response } = await axios.post(
                apiEndpoint.checkout,
                formData
            );

            if (response?.error) {
                throw new Error(response?.message);
            }
            // If this is a QR ad, show payment form
            if (this.currentAd && this.currentAd.order_method === "qr") {
                this.el.registerForm.hide();
                this.el.paymentForm.show();

                const transaction_note = response?.data?.transaction_note;

                // config QR bank
                this.el.bankTransactionContent.text(transaction_note);
                this.el.bankTotalAmount.text(this.currentAd?.detail?.price);
                const priceString = this.currentAd?.detail?.price || "0 ₫";
                const amount = priceString.replace(/[^\d]/g, ""); // Remove all non-digit characters
                const addInfo = response.data.transaction_note;
                const qrCode = `https://api.vietqr.io/image/${webinarConfig?.sepay_bank_code}-${webinarConfig?.sepay_account_number}-1.jpg?accountName=${fullName}&amount=${amount}&addInfo=${addInfo}`;
                this.el.imgQrCode.attr("src", qrCode);

                this.el.timerContainer.show();

                // Start checking payment status
                this.startPaymentCheck(transaction_note);

                // Start payment countdown
                this.startPaymentCountdown();
            } else {
                Swal.fire({
                    icon: "success",
                    title: "Thành công",
                    text: "Đăng ký thành công!",
                });
                // If it's a form ad, close the modal
                this.offerModal.close();

                // Show the reopen button if the ad is still active
                if (this.currentAd) {
                    this.el.reopenOffer.show();
                }
            }
        } catch (error) {
            console.log("error checkout", error);
            Swal.fire({
                icon: "error",
                title: "Lỗi",
                text: "Có lỗi xảy ra khi đăng ký. Vui lòng thử lại sau.",
            });
        } finally {
            this.el.submitFormButton
                .prop("disabled", false)
                .removeClass("opacity-50 bg-red-500")
                .text("Nhận tư vấn");
        }
    }

    /**
     * Update product information in the UI
     * @param {Object} productInfo - Product information object
     */
    updateProductInfo(ad) {
        const productInfo = ad.detail;
        // Update product details in the modal
        this.el.modalTitle.text(ad.title);
        this.el.modalImageProduct.attr("src", productInfo.image);
        this.el.modalImageImg.attr("src", ad.image);
        this.el.modalDescription.text(productInfo.title);
        this.el.modalSubdescription.text(productInfo.description);
        this.el.modalPrice.text(productInfo.price);
        this.el.modalOldPrice.text(productInfo.oldPrice);
        this.el.modalDiscount.text(`Giảm ${productInfo.discount}`);

        // Update total sold and remaining quantity
        this.updateProductQuantity(productInfo);
    }

    /**
     * Start fake seeding for current advertisement
     */
    startSeeding() {
        if (!this.currentAd) return;

        const scheduleNextSeeding = () => {
            // Get random interval between min and max
            const interval =
                Math.floor(
                    Math.random() *
                        (this.options.seedingInterval.max -
                            this.options.seedingInterval.min +
                            1) +
                        this.options.seedingInterval.min
                ) * 1000;

            this.seedingTimer = setTimeout(() => {
                // Check if the product is still available
                const productInfo = this.getProductInfo(
                    this.currentAd.detail.id
                );
                if (
                    productInfo.quantity > 0 ||
                    this.currentAd.type !== "product"
                ) {
                    // Update product quantity
                    this.incrementSoldCount(this.currentAd.detail.id);

                    // Show seeding notification
                    this.seedingUtil.showNotification(
                        this.currentAd.order_method
                    );

                    // Schedule next seeding
                    scheduleNextSeeding();
                } else {
                    this.handleSoldOut();
                }
            }, interval);
        };

        // Start the seeding process
        scheduleNextSeeding();
    }

    /**
     * Start countdown timer for advertisement duration
     * @param {number} duration - Duration in seconds
     */
    startAdCountdown(duration) {
        let remainingTime = duration;

        const updateCountdown = () => {
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            const formatedTime = `${String(minutes).padStart(2, "0")}:${String(
                seconds
            ).padStart(2, "0")}`;

            this.el.adCountdown.text(formatedTime);

            if (remainingTime <= 0) {
                // Store expired state in localStorage
                localStorage.setItem(
                    `ad_${this.currentAdCode}_expired`,
                    "true"
                );

                clearInterval(this.countdownTimer);
                this.el.submitFormButton
                    .prop("disabled", true)
                    .addClass("bg-red-500");
                this.el.submitFormButton.text("Đã hết hạn");
                this.el.reopenPopupButton.hide();

                if (this.seedingTimer) {
                    clearTimeout(this.seedingTimer);
                    this.seedingTimer = null;
                }
            } else {
                remainingTime--;
            }
        };

        // Initial update
        updateCountdown();

        // Update every second
        this.countdownTimer = setInterval(updateCountdown, 1000);
    }

    /**
     * Start countdown timer for payment
     */
    startPaymentCountdown() {
        let remainingTime = this.options.paymentTimeout;

        const updateCountdown = () => {
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            this.el.timer.text(
                `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
                    2,
                    "0"
                )}`
            );

            if (remainingTime <= 0) {
                clearInterval(this.paymentCountdownTimer);
                // Handle payment timeout
                this.el.statusContainer.html(
                    '<strong>Trạng thái:</strong> <span class="text-danger">Đã hết hạn</span>'
                );
            } else {
                remainingTime--;
            }
        };

        // Initial update
        updateCountdown();

        // Update every second
        this.paymentCountdownTimer = setInterval(updateCountdown, 1000);
    }

    /**
     * Start checking payment status
     */
    startPaymentCheck(transaction_note) {
        this.paymentCheckTimer = setInterval(() => {
            axios
                .get(apiEndpoint.verifyPayment, {
                    params: {
                        transaction_note,
                        webinar_id: this.webinarInfo.id,
                    },
                })
                .then(({ data: response }) => {
                    if (response?.message === "ok") {
                        clearInterval(this.paymentCheckTimer);

                        // Update payment status
                        this.el.statusContainer.html(
                            '<strong>Trạng thái:</strong> <span class="text-success font-bold">Thanh toán thành công <i class="fas fa-check-circle text-success ml-2"></i></span>'
                        );

                        // Clear payment countdown
                        if (this.paymentCountdownTimer) {
                            clearInterval(this.paymentCountdownTimer);
                        }
                        this.el.timerContainer.hide();

                        // Show success alert
                        Swal.fire({
                            icon: "success",
                            title: "Thành công",
                            text: "Chúc mừng bạn vừa thanh toán thành công!",
                        });

                        // Close the modal
                        this.offerModal.close();
                    }
                });
        }, this.options.paymentCheckInterval);
    }

    /**
     * Increment the sold count and decrement available quantity
     * @param {number|string} productId - Product ID
     */
    incrementSoldCount(productId) {
        // Get current product info
        const productInfo = this.getProductInfo(productId);

        // Update counts
        productInfo.totalSold++;
        productInfo.quantity--;

        // Save to localStorage
        this.saveProductInfo(productId, productInfo);

        // Update UI
        this.updateProductQuantity(productInfo);

        // Check if product is sold out
        if (productInfo.quantity <= 0) {
            this.handleSoldOut();
        }
    }

    /**
     * Update product quantity display in the UI
     * @param {Object} productInfo - Product information
     */
    updateProductQuantity(productInfo) {
        this.el.modalTotalSold.text(`${productInfo.totalSold} người đã mua`);
        this.el.modalRemaining.text(`Còn lại ${productInfo.quantity} sản phẩm`);
    }

    /**
     * Handle sold out scenario
     */
    handleSoldOut() {
        // Disable CTA buttons
        this.el.submitFormButton
            .prop("disabled", true)
            .addClass("opacity-50")
            .addClass("bg-red-500");

        // Update product status
        this.el.modalRemaining.html(
            '<span class="text-red-500">Đã hết hàng</span>'
        );
    }

    /**
     * Get product information from storage or defaults
     * @param {number|string} productId - Product ID
     * @returns {Object} Product information
     */
    getProductInfo(productId) {
        const storageKey = `${this.storageKeyPrefix}${productId}`;
        const storedData = localStorage.getItem(storageKey);

        if (storedData) {
            return JSON.parse(storedData);
        }

        // If no stored data, find the product in advertisements
        // for (const ad of this.advertisements) {
        //     if (ad.detail.id == productId) {
        //         return { ...ad.detail };
        //     }
        // }
        if (this.currentAd) {
            return { ...this.currentAd.detail };
        }

        // Fallback
        return {
            quantity: 0,
            totalSold: 0,
        };
    }

    /**
     * Save product information to localStorage
     * @param {number|string} productId - Product ID
     * @param {Object} productInfo - Product information
     */
    saveProductInfo(productId, productInfo) {
        const storageKey = `${this.storageKeyPrefix}${productId}`;
        localStorage.setItem(storageKey, JSON.stringify(productInfo));
    }

    /**
     * Load product data from localStorage for all advertisements
     */
    loadProductData() {
        this.advertisements.forEach((ad) => {
            const productInfo = this.getProductInfo(ad.id);
            // Update advertisement detail with stored values
            // ad.detail.quantity = productInfo.quantity;
            // ad.detail.totalSold = productInfo.totalSold;
        });
    }

    /**
     * Convert HH:mm:ss time format to seconds
     * @param {string} timeString - Time in HH:mm:ss format
     * @returns {number} Time in seconds
     */
    getTimeInSeconds(timeString) {
        const [hours, minutes, seconds] = timeString.split(":").map(Number);
        return hours * 3600 + minutes * 60 + seconds;
    }
}

/**
 * Manual Advertisement Manager
 * Handles displaying advertisements that are triggered manually without time-based logic
 * Extends AdvertisementManager but removes countdown and reopen functionality
 */
class ManualAdvertisementManager extends AdvertisementManager {
    constructor(modalId, webinarInfo, options = {}) {
        // Set manual mode flag and pass empty advertisements array to parent
        super(modalId, [], webinarInfo, { ...options, isManualMode: true });
        this.currentAd = null;
        this.lastClosedAdCode =
            sessionStorage.getItem("lastClosedAdCode") || "";
        this.currentAdCode = "";
    }

    /**
     * Display a manual advertisement
     * @param {Object} ad - The advertisement object to display
     */
    showManualAdvertisement(ad, adCode = "ssssss") {
        if (ad) {
            const lastAdCode = sessionStorage.getItem("lastClosedAdCode");
            if (lastAdCode === adCode) {
                // Do not show the same ad again
                return;
            }

            const expired = localStorage.getItem(`ad_${adCode}_expired`);
            if (expired) {
                // Do not show the same ad again
                return;
            }

            this.currentAd = ad;
            if (this.currentAdCode !== adCode) {
                this.closeCurrentAd();
                this.offerModal.close();
                setTimeout(() => {
                    this.lastClosedAdCode = this.currentAdCode;
                    // Store the last closed ad code in sessionStorage
                    sessionStorage.setItem(
                        "lastClosedAdCode",
                        this.lastClosedAdCode
                    );
                    this.currentAdCode = adCode;
                    this.showAdvertisement(ad);
                }, 1000);
            }
        } else {
            this.currentAd = null;
            this.closeCurrentAd();
        }
    }

    /**
     * Override checkForAdvertisements to do nothing
     * Manual ads don't need time-based checking
     */
    checkForAdvertisements(currentTime) {
        // Do nothing - manual ads are not time-based
        return;
    }
}

window.AdvertisementManager = AdvertisementManager;
window.ManualAdvertisementManager = ManualAdvertisementManager;
