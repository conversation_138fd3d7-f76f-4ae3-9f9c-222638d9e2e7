class ModalUtil {
    constructor(selector, options = {}) {
        this.modal = $(selector);
        this.overlay = options.overlay || $("#popupOverlay");
        this.closeBtn = this.modal.find(
            options.closeBtn || "[id$='closePopupButton']"
        );
        this.isOpen = false;
        this.options = {
            animationDuration: options.animationDuration || 200,
            openClass: options.openClass || "modal-open",
            closeClass: options.closeClass || "modal-close",
            overlayClass: options.overlayClass || "active",
            onOpen: options.onOpen || function () {},
            onClose: options.onClose || function () {},
            ...options,
        };

        this.init();
    }

    init() {
        // Set up event listeners
        this.closeBtn.on("click", () => this.close());
        this.overlay.on("click", () => this.close());

        // Close on ESC key
        $(document).on("keydown", (e) => {
            if (e.key === "Escape" && this.isOpen) this.close();
        });
    }

    open() {
        if (this.isOpen) return;

        this.overlay.css("display", "block");
        this.modal.css("display", "block");

        // Trigger reflow to ensure transitions work
        this.modal[0].offsetHeight;

        this.overlay.addClass(this.options.overlayClass);
        this.modal.addClass(this.options.openClass);
        this.isOpen = true;

        // Call onOpen callback
        this.options.onOpen.call(this);

        return this;
    }

    close() {
        if (!this.isOpen) return;

        this.modal
            .removeClass(this.options.openClass)
            .addClass(this.options.closeClass);
        this.overlay.removeClass(this.options.overlayClass);
        setTimeout(() => {
            this.modal.css("display", "none").removeClass(this.options.closeClass);
            this.overlay.css("display", "none");
            this.isOpen = false;
            this.options.onClose.call(this);
        }, this.options.animationDuration);

        return this;
    }

    toggle() {
        return this.isOpen ? this.close() : this.open();
    }
}

window.ModalUtil = ModalUtil;
