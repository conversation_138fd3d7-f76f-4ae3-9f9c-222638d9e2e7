document.addEventListener("DOMContentLoaded", function () {
    const downloadButton = document.querySelector(".download-qr-btn");

    downloadButton.addEventListener("click", function () {
        // Get the QR code image
        const qrImage = document.querySelector("#qrcode img");
        if (!qrImage) {
            alert("QR code image not found!");
            return;
        }

        // Create a temporary link element
        const downloadLink = document.createElement("a");
        downloadLink.href = qrImage.src;
        downloadLink.download = "payment-qr-code.png";

        // Append to body, click, and remove
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    });
});
