# Hệ thống Quản lý Học sinh Mới - Sử dụng Bảng webinar_participants

## Tổng quan thay đổi

Hệ thống đã được cập nhật để sử dụng bảng `webinar_participants` thay vì `webinar_students` + `users` để quản lý học sinh.

## Cấu trúc Database mới

### Bảng webinar_participants (đã được mở rộng)

```sql
-- <PERSON><PERSON><PERSON> cột mới được thêm vào:
ALTER TABLE webinar_participants ADD COLUMN password VARCHAR(255) NULL COMMENT 'Mật khẩu cho tài khoản học sinh (nullable)';
ALTER TABLE webinar_participants ADD COLUMN is_student BOOLEAN DEFAULT 0 COMMENT '0: Participant thường, 1: Học sinh';
ALTER TABLE webinar_participants ADD COLUMN note TEXT NULL COMMENT '<PERSON><PERSON> chú về học sinh';
```

### Cấu trúc hoàn chỉnh:
- `id` - Primary key
- `webinar_id` - Foreign key đến webinars
- `name` - Tên participant/học sinh
- `email` - Email (nullable)
- `phone` - Số điện thoại (nullable)
- `password` - Mật khẩu (nullable, chỉ dành cho học sinh)
- `is_student` - 0: Participant thường, 1: Học sinh
- `note` - Ghi chú (nullable)
- `ip_address` - IP address
- `joined_at` - Thời gian tham gia
- `join_count` - Số lần tham gia
- `view_duration` - Thời gian xem
- `device_type` - Loại thiết bị
- `utm_*` - Các thông tin tracking

## Luồng hoạt động mới

### 1. Tìm kiếm Participants để chuyển thành học sinh
- **Trước**: Tìm trong bảng `users` những user chưa có trong `webinar_students`
- **Sau**: Tìm trong bảng `webinar_participants` những participant có `is_student = 0`

### 2. Thêm học sinh từ participant có sẵn
- **Trước**: Tạo record mới trong `webinar_students` với `user_id`
- **Sau**: Cập nhật `is_student = 1` cho participant đã chọn

### 3. Tạo học sinh mới
- **Trước**: Tạo user mới trong `users`, sau đó tạo record trong `webinar_students`
- **Sau**: Tạo participant mới với `is_student = 1` và `password` được hash

### 4. Cập nhật thông tin học sinh
- **Trước**: Cập nhật trong `users` và `webinar_students`
- **Sau**: Cập nhật trực tiếp trong `webinar_participants`

### 5. Xóa học sinh
- **Trước**: Xóa record trong `webinar_students` (giữ lại user)
- **Sau**: Xóa hoàn toàn participant hoặc chuyển về `is_student = 0`

## Ưu điểm của luồng mới

1. **Đơn giản hóa**: Chỉ cần 1 bảng thay vì 2 bảng
2. **Linh hoạt**: Có thể chuyển đổi participant thành học sinh dễ dàng
3. **Tách biệt**: Mỗi webinar có học sinh riêng, không chia sẻ với webinar khác
4. **Bảo mật**: Mật khẩu chỉ được tạo khi cần thiết (cho học sinh)

## Cách sử dụng

### Truy cập trang quản lý học sinh
```
/webinars/{webinar_id}/students
```

### Các chức năng có sẵn:

1. **Tìm participant để chuyển thành học sinh**
   - Tìm kiếm theo tên, email, số điện thoại
   - Chỉ hiển thị participants có `is_student = 0`
   - Click "Chuyển thành học sinh" để convert

2. **Tạo học sinh mới**
   - Điền form với thông tin đầy đủ
   - Hệ thống sẽ tạo participant mới với `is_student = 1`
   - Mật khẩu được hash tự động

3. **Xem danh sách học sinh**
   - Hiển thị tất cả participants có `is_student = 1`
   - Phân trang và tìm kiếm
   - Hiển thị thông tin: tên, email, phone, ngày tham gia, ghi chú

4. **Chỉnh sửa học sinh**
   - Cập nhật thông tin cá nhân
   - Đổi mật khẩu (optional)
   - Cập nhật ghi chú

5. **Xóa học sinh**
   - Xóa hoàn toàn khỏi hệ thống
   - Hoặc có thể chuyển về participant thường (comment trong code)

## API Endpoints

```php
// Tìm participants để chuyển thành học sinh
GET /webinars/{webinar}/students/search?q={query}

// Danh sách học sinh
GET /webinars/{webinar}/students/list?page={page}&search={search}

// Chuyển participant thành học sinh
POST /webinars/{webinar}/students/add
Body: { user_id: participant_id, note: "optional" }

// Tạo học sinh mới
POST /webinars/{webinar}/students/create
Body: { name, email, phone, password, note }

// Cập nhật học sinh
PUT /webinars/{webinar}/students/update
Body: { student_id, name, email, phone, password?, note }

// Xóa học sinh
DELETE /webinars/{webinar}/students/remove
Body: { student_id }
```

## Dữ liệu Test

Đã tạo seeder `TestWebinarParticipantsSeeder` để tạo dữ liệu test:
- 5 participants thường (is_student = 0)
- 3 học sinh (is_student = 1)

Chạy seeder:
```bash
php artisan db:seed --class=TestWebinarParticipantsSeeder
```

## Migration

Migration đã được tạo và chạy:
```bash
php artisan migrate
```

File migration: `2025_06_07_000001_add_password_and_is_student_to_webinar_participants_table.php`

## Lưu ý quan trọng

1. **Validation**: Email và phone chỉ cần unique trong cùng 1 webinar
2. **Password**: Chỉ được hash khi tạo học sinh, không lưu plain text
3. **Security**: Password được ẩn trong API response (hidden attribute)
4. **Backward Compatibility**: Bảng `webinar_students` cũ vẫn tồn tại, có thể migration dữ liệu nếu cần

## Kiểm tra hoạt động

1. Truy cập `/webinars/{webinar_id}/students`
2. Thử tìm kiếm participants để chuyển thành học sinh
3. Thử tạo học sinh mới
4. Kiểm tra danh sách học sinh
5. Thử chỉnh sửa và xóa học sinh 