<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng <PERSON><PERSON><PERSON></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8b5fbf 100%);
            min-height: 100vh;
            padding: 40px 20px;
            color: #333;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
        }

        h1 {
            text-align: center;
            color: white;
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 60px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .pricing-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1),
            0 8px 25px rgba(0,0,0,0.08),
            inset 0 1px 0 rgba(255,255,255,0.9);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 32px 80px rgba(102, 126, 234, 0.25),
            0 16px 40px rgba(0,0,0,0.15),
            inset 0 1px 0 rgba(255,255,255,0.9);
        }

        .pricing-card:hover::before {
            opacity: 1;
        }

        .popular-badge, .recommended-badge {
            position: absolute;
            top: -12px;
            right: 30px;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            z-index: 2;
        }

        .popular-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .recommended-badge {
            background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
            color: white;
        }

        .plan-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 12px;
            text-align: center;
            letter-spacing: -0.02em;
        }

        .plan-duration {
            text-align: center;
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .plan-features {
            text-align: center;
            color: #64748b;
            margin-bottom: 24px;
            font-size: 1rem;
            font-weight: 500;
        }

        .price-section {
            text-align: center;
            margin: 30px 0;
        }

        .original-price {
            text-decoration: line-through;
            color: #94a3b8;
            font-size: 1rem;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .current-price {
            font-size: 3rem;
            font-weight: 800;
            color: #1e293b;
            margin: 12px 0;
            letter-spacing: -0.02em;
        }

        .setup-fee {
            text-align: center;
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .total-section {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 24px;
            margin: 24px 0;
            border: 1px solid #cbd5e1;
            text-align: center;
        }

        .total-label {
            font-size: 1rem;
            color: #64748b;
            font-weight: 600;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .total-price {
            font-size: 2rem;
            font-weight: 800;
            color: #1e40af;
            letter-spacing: -0.02em;
        }

        .savings {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 16px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: center;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
        }

        .feature-highlight {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border: 1px solid #a78bfa;
            border-radius: 12px;
            padding: 16px;
            margin: 20px 0;
            color: #5b21b6;
            font-weight: 600;
            text-align: center;
            font-size: 0.95rem;
        }

        .why-choose {
            background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
            border: 1px solid #38bdf8;
            border-radius: 12px;
            padding: 18px;
            margin: 20px 0;
        }

        .why-choose h4 {
            color: #0369a1;
            margin-bottom: 8px;
            font-weight: 700;
            font-size: 1rem;
        }

        .why-choose p {
            color: #0c4a6e;
            font-size: 0.95rem;
            line-height: 1.5;
            font-weight: 500;
        }

        .cta-button {
            width: 100%;
            padding: 18px 24px;
            border: none;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            margin-top: 20px;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 12px 30px rgba(102,126,234,0.4);
        }

        .btn-popular {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            box-shadow: 0 12px 30px rgba(255,107,107,0.4);
        }

        .btn-pro {
            background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
            color: white;
            box-shadow: 0 12px 30px rgba(0,210,255,0.4);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .cta-button:active {
            transform: translateY(-1px);
        }

        .supplementary-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 50px;
            margin-top: 60px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1),
            inset 0 1px 0 rgba(255,255,255,0.9);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .supplementary-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: #1e293b;
            margin-bottom: 40px;
            letter-spacing: -0.02em;
        }

        .supplementary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .supplement-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            padding: 35px;
            color: white;
            text-align: center;
            box-shadow: 0 16px 40px rgba(240,147,251,0.4);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .supplement-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
            transition: all 0.4s ease;
        }

        .supplement-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 24px 60px rgba(240,147,251,0.6);
        }

        .supplement-card:hover::before {
            top: -20%;
            right: -20%;
        }

        .setup-notice {
            margin: 60px 0;
            padding: 0 20px;
        }

        .setup-notice-card {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border: 2px solid #86efac;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(134, 239, 172, 0.3);
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .setup-notice-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .setup-notice-title {
            font-size: 2rem;
            font-weight: 700;
            color: #059669;
            margin-bottom: 30px;
            letter-spacing: -0.02em;
        }

        .setup-notice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .setup-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.7);
            padding: 15px 20px;
            border-radius: 12px;
            font-weight: 600;
            color: #047857;
            transition: all 0.3s ease;
        }

        .setup-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
        }

        .setup-item-icon {
            font-size: 1.5rem;
        }

        .supplement-card h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .supplement-price {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 20px 0;
            position: relative;
            z-index: 1;
        }

        .supplement-features {
            text-align: left;
            margin: 25px 0;
            list-style: none;
            position: relative;
            z-index: 1;
        }

        .supplement-features li {
            margin: 12px 0;
            padding-left: 28px;
            position: relative;
            font-weight: 500;
            font-size: 1rem;
        }

        .supplement-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #ffd700;
            font-weight: bold;
            font-size: 1.2rem;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .pricing-card {
            animation: fadeInUp 0.8s ease-out;
        }

        .pricing-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .pricing-card:nth-child(3) {
            animation-delay: 0.4s;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
                margin-bottom: 40px;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .pricing-card {
                padding: 30px 25px;
            }

            .supplementary-section {
                padding: 30px 25px;
            }

            .supplementary-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 20px 15px;
            }

            h1 {
                font-size: 2rem;
            }

            .pricing-card {
                padding: 25px 20px;
            }

            .current-price {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Bảng Giá Dịch Vụ Kênh Webinar</h1>

    <div class="pricing-grid">
        <!-- Gói Tối Ưu -->
        <div class="pricing-card">
            <h2 class="plan-title">Gói Tối Ưu</h2>
            <p class="plan-duration">Thời hạn: 3 tháng</p>
            <p class="plan-features">1 kênh webinar</p>

            <div class="price-section">
                <div class="original-price">Giá gốc: 6.190.000đ</div>
                <div class="current-price">4.190.000đ</div>
            </div>

            <div class="setup-fee">Phí setup: 2.000.000đ</div>

            <div class="total-section">
                <div class="total-label">Tổng thanh toán</div>
                <div class="total-price">6.190.000đ</div>
            </div>

            <div class="savings">
                ⭐ Gói khởi điểm lý tưởng cho doanh nghiệp
            </div>

            <div class="feature-highlight">
                💡 Chỉ 2.063.000đ/tháng (bao gồm setup) - Giá tốt cơ bản cho doanh nghiệp!
            </div>



        </div>

        <!-- Gói 6 Tháng -->
        <div class="pricing-card">
            <div class="popular-badge">⭐ PHỔ BIẾN NHẤT</div>
            <h2 class="plan-title" style="color: #ff6b6b;">Gói 6 Tháng</h2>
            <p class="plan-duration">Thời hạn: 6 tháng</p>
            <p class="plan-features">1 kênh webinar</p>

            <div class="price-section">
                <div class="original-price">Giá gốc: 10.340.000đ</div>
                <div class="current-price" style="color: #ff6b6b;">5.990.000đ</div>
            </div>

            <div class="setup-fee">Phí setup: 2.000.000đ</div>

            <div class="total-section">
                <div class="total-label">Tổng thanh toán</div>
                <div class="total-price" style="color: #ff6b6b;">7.990.000đ</div>
            </div>

            <div class="savings">
                ⭐ Tiết kiệm: 2.350.000đ - Lựa chọn cân bằng tối ưu
            </div>

            <div class="feature-highlight">
                🔥 Chỉ 1.332.000đ/tháng (bao gồm setup) - Khuyến khích sử dụng e-learning TỐI ƯU được giảm 10% còn 7.191.000đ
            </div>


        </div>

        <!-- Gói 12 Tháng Pro -->
        <div class="pricing-card">
            <div class="recommended-badge">💎 ƯU ĐÃI NHẤT</div>
            <h2 class="plan-title" style="color: #00d2ff;">Gói 12 Tháng Pro</h2>
            <p class="plan-duration">Thời hạn: 12 tháng</p>
            <p class="plan-features">1 kênh webinar</p>

            <div class="price-section">
                <div class="original-price">Giá gốc: 18.000.000đ</div>
                <div class="current-price" style="color: #00d2ff;">12.990.000đ</div>
            </div>

            <div class="setup-fee" style="color: #00d2ff;">Phí setup: MIỄN PHÍ</div>

            <div class="total-section">
                <div class="total-label">Tổng thanh toán</div>
                <div class="total-price" style="color: #00d2ff;">12.990.000đ</div>
            </div>

            <div class="savings">
                💎 Tiết kiệm: 5.690.000đ
            </div>

            <div class="feature-highlight">
                🏆Khuyến khích dùng e-learning TỐI ƯU được giảm 10% còn 11.650.000đ
            </div>


        </div>
    </div>

    <!-- Lưu ý Setup -->
    <div class="setup-notice">
        <div class="setup-notice-card">
            <div class="setup-notice-icon">🎁</div>
            <h3 class="setup-notice-title">Phí Setup Bao Gồm</h3>
            <div class="setup-notice-grid">
<!--                <div class="setup-item">-->
<!--                    <span class="setup-item-icon">🎨</span>-->
<!--                    <span>Tích hợp logo thương hiệu</span>-->
<!--                </div>-->
                <div class="setup-item">
                    <span class="setup-item-icon">🌐</span>
                    <span>Tên miền riêng, logo thương hiệu</span>
                </div>
                <div class="setup-item">
                    <span class="setup-item-icon">💳</span>
                    <span>Hệ thống thanh toán QR tự động</span>
                </div>
                <div class="setup-item">
                    <span class="setup-item-icon">📱</span>
                    <span>Thông báo qua Telegram miễn phí</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Chi Phí Bổ Sung Section -->
    <div class="supplementary-section">
        <h2 class="supplementary-title">Chi Phí Bổ Sung</h2>

        <div class="supplementary-grid">
            <!-- Kênh Bổ Sung Webinar -->
            <div class="supplement-card">
                <h3>📺 Kênh Bổ Sung Webinar</h3>
                <div class="supplement-price">990.000đ/tháng</div>
                <ul class="supplement-features">
                    <li>Thêm kênh webinar phụ</li>
                    <li>Quản lý độc lập hoàn toàn</li>
                    <li>Tương tác đầy đủ tính năng</li>
                    <li>Phân tích chỉ số riêng biệt</li>
                </ul>
            </div>

            <!-- Tính năng Gọi Nhóm -->
            <div class="supplement-card">
                <h3>📞 Tính năng livestream trực tiếp</h3>
                <div class="supplement-price">4.000.000đ</div>
                <ul class="supplement-features">
                    <li>Kích hoạt dùng vĩnh viễn</li>
                    <li>Tương tác form trực tiếp trong cuộc gọi</li>
                    <li>Bán hàng & thanh toán tự động trong meeting</li>
                    <li>Nhúng gọi điện nhóm như Zoom</li>
                    <li>Chỉ trả 1 lần - sử dụng mãi mãi</li>
                </ul>
            </div>

            <!-- Topid Meeting -->
            <div class="supplement-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <h3>💻 Topid Meeting</h3>
                <div class="supplement-price">350.000đ/tháng</div>
                <ul class="supplement-features">
                    <li>Gọi không giới hạn phút</li>
                    <li>Không giới hạn người tham gia</li>
                    <li>Tính năng ghi lại cuộc gọi</li>
                    <li>Chia sẻ màn hình</li>
                    <li>Tích hợp sẵn vào webinar Topid</li>
                    <li>Các tính năng cao cấp tương tự Zoom</li>
                </ul>
                <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 15px; margin-top: 20px;">
                    <div style="font-size: 1rem; font-weight: 600; margin-bottom: 8px;">🎁 Khuyến mãi đặc biệt:</div>
                    <div style="font-size: 0.9rem; line-height: 1.4;">
                        • Thanh toán 6 tháng: Tặng thêm 1 tháng<br>
                        • Thanh toán 12 tháng: Tặng thêm 2 tháng
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced card interactions
        const cards = document.querySelectorAll('.pricing-card, .supplement-card');

        cards.forEach((card, index) => {
            // Staggered animation
            card.style.animationDelay = `${index * 0.2}s`;

            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-12px) scale(1.02)';
                this.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Enhanced button interactions
        const buttons = document.querySelectorAll('.cta-button');

        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Ripple effect
                const ripple = document.createElement('div');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.6);
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        pointer-events: none;
                    `;

                this.appendChild(ripple);

                setTimeout(() => ripple.remove(), 600);

                // Button animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);

                // Success message
                const planName = this.closest('.pricing-card, .supplement-card').querySelector('h2, h3').textContent;
                alert(`🎉 Cảm ơn bạn đã quan tâm đến "${planName}"!\n\nChúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để tư vấn chi tiết.`);
            });
        });

        // Add CSS animation for ripple effect
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    });
</script>

</body>
</html>
