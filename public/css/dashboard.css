/* Dashboard Specific Styles */

/* Stats Cards */
.stats-card {
    border-radius: 1rem;
    padding: 0;
    position: relative;
    overflow: hidden;
    min-height: 140px;
    color: white;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-card .stats-content {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    height: 100%;
    position: relative;
    z-index: 2;
}

.stats-card .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stats-card .stats-info {
    flex: 1;
}

.stats-card .card-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-card .stats-value {
    color: #fff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stats-card small {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.7rem;
    font-weight: 500;
}

/* Gradient Backgrounds */
.stats-card.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.bg-gradient-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.stats-card.bg-gradient-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.stats-card.bg-gradient-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.stats-card.bg-gradient-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stats-card.bg-gradient-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* Legacy support */
.stats-card.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.bg-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

/* Main Title */
.main-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #3E4B64;
    display: flex;
    align-items: center;
}

.main-title i {
    background-color: #4176F9;
    color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

/* Date Display */
.date-display {
    background-color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 30px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    display: inline-flex;
    align-items: center;
    color: #6B7A99;
    font-weight: 600;
}

.date-display i {
    margin-right: 0.5rem;
    color: #4176F9;
}

/* Card Styles */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-header i.text-primary {
    color: #4176F9 !important;
}

/* Badge Styles */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 600;
    border-radius: 30px;
}

.badge-primary {
    background-color: rgba(65, 118, 249, 0.1);
    color: #4176F9;
}

.badge-secondary {
    background-color: rgba(107, 122, 153, 0.1);
    color: #6B7A99;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        min-height: 120px;
        margin-bottom: 1rem;
    }

    .stats-card .stats-content {
        padding: 1.25rem;
    }

    .stats-card .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }

    .stats-card .stats-value {
        font-size: 1.5rem;
    }

    .stats-card .card-title {
        font-size: 0.7rem;
    }
}

/* Row spacing improvements */
.row.g-4 {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 1.5rem;
}

/* Animation for stats cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }
