document.addEventListener("DOMContentLoaded", function () {
    // Hide all loading containers after 5 seconds
    const loadingContainers = [
        document.getElementById("loading-container"),
        document.getElementById("loading-container-live"),
        document.getElementById("loading-container-empty"),
        // document.getElementById('loading-container-default')
    ];

    setTimeout(function () {
        loadingContainers.forEach((container) => {
            if (container) {
                container.style.display = "none";
            }
        });
    }, 500);

    // Initialize native livestream video if it exists
    const liveVideoElement = document.getElementById("native-live-video");
    const regularVideoElement = document.getElementById("native-video");
    const volumeControl = document.getElementById("volume-control");
    const currentTimeEl = document.getElementById("current-time");
    const totalTimeEl = document.getElementById("total-time");

    // Hide any possible end overlays on page load
    const endOverlays = [
        document.getElementById("webinar-ended-overlay"),
        document.getElementById("webinar-ended-overlay-regular"),
    ];

    endOverlays.forEach((overlay) => {
        if (overlay) {
            overlay.style.display = "none";
        }
    });

    // Later on in the code, remove this line:
    // Initialize standard video player for recorded content
    if (regularVideoElement) {
        console.log("Found regular video element");
    }

    if (liveVideoElement) {
        console.log("Found live video element");

        // Add event listener for video ended
        liveVideoElement.addEventListener("ended", function (e) {
            console.log("Live video ended event triggered");

            // Prevent default behavior
            e.preventDefault();

            // Force pause the video
            liveVideoElement.pause();

            // Show the ended overlay
            const endedOverlay = document.getElementById(
                "webinar-ended-overlay"
            );
            if (endedOverlay) {
                endedOverlay.style.display = "flex";
                console.log("Showing live video ended overlay");
            }

            // Remove the entire video element from the DOM
            if (liveVideoElement.parentNode) {
                liveVideoElement.parentNode.removeChild(liveVideoElement);
                console.log("Removed live video element from DOM");
            }
        });

        // More aggressive check for video near the end
        liveVideoElement.addEventListener("timeupdate", function () {
            // Check if video is near the end (within 0.5 seconds)
            if (
                liveVideoElement.duration > 0 &&
                liveVideoElement.currentTime >= liveVideoElement.duration - 0.5
            ) {
                console.log("Video near end detected via timeupdate");

                // Force pause the video
                liveVideoElement.pause();

                // Show the ended overlay
                const endedOverlay = document.getElementById(
                    "webinar-ended-overlay"
                );
                if (endedOverlay) {
                    endedOverlay.style.display = "flex";
                    console.log(
                        "Showing live video ended overlay (timeupdate)"
                    );
                }

                // Remove the entire video element from the DOM
                if (liveVideoElement.parentNode) {
                    liveVideoElement.parentNode.removeChild(liveVideoElement);
                    console.log("Removed live video element from DOM");
                }
            }
        });

        // Add event listener for regular video ended
        if (regularVideoElement) {
            regularVideoElement.addEventListener("ended", function (e) {
                console.log("Regular video ended event triggered");

                // Prevent default behavior
                e.preventDefault();

                // Force pause the video
                regularVideoElement.pause();

                // Show the ended overlay
                const endedOverlay = document.getElementById(
                    "webinar-ended-overlay-regular"
                );
                if (endedOverlay) {
                    endedOverlay.style.display = "flex";
                    console.log("Showing regular video ended overlay");
                }

                // Remove the entire video element from the DOM
                if (regularVideoElement.parentNode) {
                    regularVideoElement.parentNode.removeChild(
                        regularVideoElement
                    );
                    console.log("Removed regular video element from DOM");
                }
            });

            // More aggressive check for regular video near the end
            regularVideoElement.addEventListener("timeupdate", function () {
                // Check if video is near the end (within 0.5 seconds)
                if (
                    regularVideoElement.duration > 0 &&
                    regularVideoElement.currentTime >=
                        regularVideoElement.duration - 0.5
                ) {
                    console.log(
                        "Regular video near end detected via timeupdate"
                    );

                    // Force pause the video
                    regularVideoElement.pause();

                    // Show the ended overlay
                    const endedOverlay = document.getElementById(
                        "webinar-ended-overlay-regular"
                    );
                    if (endedOverlay) {
                        endedOverlay.style.display = "flex";
                        console.log(
                            "Showing regular video ended overlay (timeupdate)"
                        );
                    }

                    // Remove the entire video element from the DOM
                    if (regularVideoElement.parentNode) {
                        regularVideoElement.parentNode.removeChild(
                            regularVideoElement
                        );
                        console.log("Removed regular video element from DOM");
                    }
                }
            });
        }

        // Calculate the elapsed time since webinar started to position the video
        const webinarStartTime = new Date(webinarConfig.webinarRawStartDate);
        const currentTime = new Date();

        // Get the timestamp passed from server - this is how many seconds have passed since the webinar started
        let elapsedTimeInSeconds = webinarConfig.videoTimestamp;

        console.log("Initial elapsed time from server:", elapsedTimeInSeconds);

        // Show debug info if debug parameter is in URL
        const debugInfoEl = document.getElementById("debug-info");
        const debugTimestampEl = document.getElementById("debug-timestamp");
        const debugElapsedEl = document.getElementById("debug-elapsed");
        const streamDurationContainer = document.getElementById(
            "stream-duration-container"
        );
        const urlParams = new URLSearchParams(window.location.search);

        if ((urlParams.has("debug") || webinarConfig.isDebug) && debugInfoEl) {
            debugInfoEl.style.display = "block";
            debugElapsedEl.textContent = elapsedTimeInSeconds;

            // Also show the stream duration element
            if (streamDurationContainer) {
                streamDurationContainer.style.display = "flex";
            }
        }

        // Update stream duration indicator
        const streamDurationEl = document.getElementById("stream-duration");
        if (streamDurationEl) {
            // Get configured duration for checking max display time
            const configuredDurationMinutes =
                liveVideoElement.getAttribute("data-duration");
            const maxDurationSeconds = configuredDurationMinutes
                ? parseInt(configuredDurationMinutes) * 60
                : Infinity;

            // Initial update
            streamDurationEl.textContent =
                formatTimeWithHours(elapsedTimeInSeconds);

            // Update every second
            setInterval(function () {
                elapsedTimeInSeconds++;
                // Don't show elapsed time greater than the configured duration
                const displayTime =
                    maxDurationSeconds &&
                    elapsedTimeInSeconds > maxDurationSeconds
                        ? maxDurationSeconds
                        : elapsedTimeInSeconds;

                streamDurationEl.textContent = formatTimeWithHours(displayTime);
                if (debugElapsedEl) {
                    debugElapsedEl.textContent = elapsedTimeInSeconds;
                }
            }, 1000);
        }

        // For looping videos, make sure we don't exceed video duration
        liveVideoElement.addEventListener("loadedmetadata", function () {
            // Get duration from HTML data attribute if set, otherwise use actual video duration
            let videoDuration;
            const configuredDuration =
                liveVideoElement.getAttribute("data-duration");

            if (configuredDuration) {
                // Convert from minutes to seconds
                videoDuration = parseInt(configuredDuration) * 60;
                console.log(
                    "Using configured duration:",
                    videoDuration,
                    "seconds"
                );
            } else {
                videoDuration = liveVideoElement.duration;
                console.log(
                    "Using actual video duration:",
                    videoDuration,
                    "seconds"
                );
            }

            if (videoDuration > 0) {
                // For looping videos, calculate the appropriate position in the video based on current time
                let positionInLoop = elapsedTimeInSeconds;

                // If timestamp is greater than video duration, loop it appropriately
                if (positionInLoop > videoDuration) {
                    positionInLoop = positionInLoop % videoDuration;
                    console.log(
                        "Video looped:",
                        Math.floor(elapsedTimeInSeconds / videoDuration),
                        "times"
                    );
                }

                console.log(
                    "Starting video at position:",
                    positionInLoop,
                    "seconds (",
                    Math.floor(positionInLoop / 60),
                    "minutes",
                    positionInLoop % 60,
                    "seconds)"
                );

                // Update debug info
                if (debugTimestampEl) {
                    debugTimestampEl.textContent = positionInLoop.toFixed(1);
                }

                // Set the current time to simulate a livestream
                liveVideoElement.currentTime = positionInLoop;

                // Handle loop handling to maintain correct position when video loops
                liveVideoElement.addEventListener("timeupdate", function () {
                    // This ensures the video position stays in sync with elapsed time
                    const expectedPosition =
                        elapsedTimeInSeconds % videoDuration;
                    const actualPosition = liveVideoElement.currentTime;

                    // Only adjust if there's a significant drift (more than 5 seconds)
                    if (Math.abs(expectedPosition - actualPosition) > 5) {
                        console.log(
                            "Adjusting video position to:",
                            expectedPosition
                        );
                        liveVideoElement.currentTime = expectedPosition;
                    }

                    if (debugTimestampEl) {
                        debugTimestampEl.textContent =
                            liveVideoElement.currentTime.toFixed(1);
                    }
                });

                // Immediately try to play after setting position
                playVideo();
            }
        });

        // Try to play the video immediately with extra handling for browser policies
        const playVideo = function () {
            liveVideoElement
                .play()
                .then(() => {
                    console.log("Video play succeeded");
                })
                .catch((err) => {
                    console.error("Video play error:", err);
                });
        };

        // Try multiple strategies to ensure autoplay
        setTimeout(playVideo, 500);

        // Force autoplay on first user interaction with the page
        const forcePlayOnInteraction = function () {
            playVideo();
            document.removeEventListener("click", forcePlayOnInteraction);
            document.removeEventListener("touchstart", forcePlayOnInteraction);
            document.removeEventListener("keydown", forcePlayOnInteraction);
        };

        document.addEventListener("click", forcePlayOnInteraction, {
            once: true,
        });
        document.addEventListener("touchstart", forcePlayOnInteraction, {
            once: true,
        });
        document.addEventListener("keydown", forcePlayOnInteraction, {
            once: true,
        });

        // Handle volume control click
        if (volumeControl) {
            volumeControl.addEventListener("click", function () {
                if (liveVideoElement.muted) {
                    liveVideoElement.muted = false;
                    volumeControl.innerHTML = `
                                <div class="volume-notification">
                                    <i class="fas fa-volume-up"></i>
                                    <span class="volume-text">Âm thanh đang bật. Nhấn để tắt âm thanh</span>
                                </div>
                            `;
                } else {
                    liveVideoElement.muted = true;
                    volumeControl.innerHTML = `
                                <div class="volume-notification">
                                    <i class="fas fa-volume-mute"></i>
                                    <span class="volume-text">Âm thanh đang tắt. Nhấn để bật âm thanh</span>
                                </div>
                            `;
                }
            });

            // Hide volume control notification after 5 seconds
            setTimeout(function () {
                volumeControl.style.opacity = "0";
                setTimeout(function () {
                    volumeControl.style.opacity = "0.7";
                    volumeControl.style.transform = "scale(0.8)";
                    volumeControl.innerHTML =
                        '<i class="fas fa-volume-mute"></i>';
                }, 500);
            }, 5000);

            // Handle hover effects
            volumeControl.addEventListener("mouseenter", function () {
                volumeControl.style.opacity = "1";
                volumeControl.style.transform = "scale(1)";
                if (
                    volumeControl.querySelector(".volume-notification") === null
                ) {
                    if (liveVideoElement.muted) {
                        volumeControl.innerHTML = `
                                    <div class="volume-notification">
                                        <i class="fas fa-volume-mute"></i>
                                        <span class="volume-text">Âm thanh đang tắt. Nhấn để bật âm thanh</span>
                                    </div>
                                `;
                    } else {
                        volumeControl.innerHTML = `
                                    <div class="volume-notification">
                                        <i class="fas fa-volume-up"></i>
                                        <span class="volume-text">Âm thanh đang bật. Nhấn để tắt âm thanh</span>
                                    </div>
                                `;
                    }
                }
            });

            // Hide notification on mouse leave
            volumeControl.addEventListener("mouseleave", function () {
                setTimeout(function () {
                    if (!volumeControl.matches(":hover")) {
                        volumeControl.style.opacity = "0.7";
                        volumeControl.style.transform = "scale(0.8)";
                        if (liveVideoElement.muted) {
                            volumeControl.innerHTML =
                                '<i class="fas fa-volume-mute"></i>';
                        } else {
                            volumeControl.innerHTML =
                                '<i class="fas fa-volume-up"></i>';
                        }
                    }
                }, 1000);
            });
        }

        // Update time display
        if (currentTimeEl && totalTimeEl) {
            // For livestream, show elapsed time since start
            const startTime = new Date(webinarConfig.webinarRawStartDate);
            const endTime = new Date(webinarConfig.webinarRawEndDate);
            const now = new Date();

            // Total duration in seconds
            const totalDuration = (endTime - startTime) / 1000;
            totalTimeEl.textContent = formatTime(totalDuration);

            // Update current time every second
            setInterval(function () {
                const currentTime = (new Date() - startTime) / 1000;
                currentTimeEl.textContent = formatTime(
                    Math.min(currentTime, totalDuration)
                );
            }, 1000);
        }

        // Ensure the video keeps playing - livestream effect
        liveVideoElement.addEventListener("pause", function () {
            console.log("Video paused, attempting to resume");
            setTimeout(() => {
                liveVideoElement
                    .play()
                    .catch((err) => console.log("Cannot resume:", err));
            }, 100);
        });

        // View tracking for analytics
        let duration = 0;
        setInterval(function () {
            if (!liveVideoElement.paused) {
                duration += 10;
                updateViewDuration(duration);
            }
        }, 10000);

        // Seeded comments are now handled by the ChatManager class
    }

    // Initialize standard video player for recorded content
    if (regularVideoElement) {
        console.log("Found regular video element");

        // Add event listener for video ended
        regularVideoElement.addEventListener("ended", function () {
            console.log("Regular video ended");

            // Show the ended overlay
            const endedOverlay = document.getElementById(
                "webinar-ended-overlay-regular"
            );
            if (endedOverlay) {
                // Only show if video has truly ended
                if (
                    regularVideoElement.currentTime >=
                    regularVideoElement.duration - 0.5
                ) {
                    endedOverlay.style.display = "flex";
                    console.log("Showing regular video ended overlay");
                }
            }
        });

        // Also prevent unintended looping by handling the timeupdate event
        regularVideoElement.addEventListener("timeupdate", function () {
            // Check if video is near the end
            if (
                regularVideoElement.duration > 0 &&
                regularVideoElement.currentTime >=
                    regularVideoElement.duration - 0.5
            ) {
                // Pause at the end
                regularVideoElement.pause();

                // Show the ended overlay
                const endedOverlay = document.getElementById(
                    "webinar-ended-overlay-regular"
                );
                if (endedOverlay) {
                    endedOverlay.style.display = "flex";
                    console.log(
                        "Showing regular video ended overlay (timeupdate)"
                    );
                }
            }
        });

        // Hide loading screen once video is ready
        const loadingContainer = document.getElementById("loading-container");

        regularVideoElement.addEventListener("canplay", function () {
            if (loadingContainer) {
                loadingContainer.style.display = "none";
            }
        });

        // Also hide loading container if video starts playing
        regularVideoElement.addEventListener("playing", function () {
            if (loadingContainer) {
                loadingContainer.style.display = "none";
            }
        });

        // Force hide loading container after 5 seconds (fallback)
        setTimeout(function () {
            if (loadingContainer) {
                loadingContainer.style.display = "none";
            }
        }, 5000);

        // Force autoplay for regular video as well
        setTimeout(function () {
            regularVideoElement
                .play()
                .catch((err) =>
                    console.log("Regular video autoplay error:", err)
                );
        }, 500);

        // Time display for regular video
        const currentTimeRecording = document.getElementById(
            "current-time-recording"
        );
        const totalTimeRecording = document.getElementById(
            "total-time-recording"
        );
        const videoDurationEl = document.getElementById("video-duration");

        if (currentTimeRecording && totalTimeRecording) {
            regularVideoElement.addEventListener("loadedmetadata", function () {
                const formattedDuration = formatTime(
                    regularVideoElement.duration
                );
                totalTimeRecording.textContent = formattedDuration;
                // Update duration in the info section as well
                if (videoDurationEl) {
                    videoDurationEl.textContent = formattedDuration;
                }
            });

            regularVideoElement.addEventListener("timeupdate", function () {
                currentTimeRecording.textContent = formatTime(
                    regularVideoElement.currentTime
                );
            });
        }

        // View tracking for analytics
        let duration = 0;
        regularVideoElement.addEventListener("play", function () {
            console.log("Regular video started playing");

            // Track viewing duration
            setInterval(function () {
                if (!regularVideoElement.paused) {
                    duration += 10;
                    updateViewDuration(duration);
                }
            }, 10000);
        });
    }

    // Update view duration function
    function updateViewDuration(seconds) {
        const csrfToken = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        fetch(apiEndpoint.updateDuration, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": csrfToken,
            },
            body: JSON.stringify({
                participant_id: webinarConfig.participantId,
                duration: seconds,
            }),
        })
            .then((response) => response.json())
            .catch((error) =>
                console.error("Error updating view duration:", error)
            );
    }

    // Khởi tạo đồng hồ đếm ngược
    const countdownHours = document.getElementById("hours");
    const countdownMinutes = document.getElementById("minutes");
    const countdownSeconds = document.getElementById("seconds");

    // Nếu đang trong thời gian chờ, bắt đầu đếm ngược
    if (webinarConfig.inWaitingPeriod) {
        let totalSeconds = webinarConfig.waitingTimeRemaining;

        function updateCountdown() {
            if (totalSeconds <= 0) {
                // Hết thời gian đếm ngược, ẩn loading-container-waiting và tải lại trang
                const waitingContainer = document.getElementById(
                    "loading-container-waiting"
                );
                if (waitingContainer) {
                    waitingContainer.style.display = "none";
                }
                console.log("Countdown finished, reloading page");
                window.location.reload();
                return;
            }

            // Chuyển đổi giây thành giờ:phút:giây
            let hours = Math.floor(totalSeconds / 3600);
            let minutes = Math.floor((totalSeconds % 3600) / 60);
            let seconds = Math.floor(totalSeconds % 60); // Sửa tại đây, ép kiểu số nguyên

            // Cập nhật giao diện
            if (countdownHours && countdownMinutes && countdownSeconds) {
                countdownHours.textContent = hours < 10 ? "0" + hours : hours;
                countdownMinutes.textContent =
                    minutes < 10 ? "0" + minutes : minutes;
                countdownSeconds.textContent =
                    seconds < 10 ? "0" + seconds : seconds;
            }

            // Giảm thời gian
            totalSeconds--;
        }

        // Cập nhật ngay lần đầu
        updateCountdown();

        // Cập nhật mỗi giây
        const countdownInterval = setInterval(updateCountdown, 1000);
    }

    // Share functionality
    const shareBtn = document.getElementById("share-btn");
    const shareDialog = document.getElementById("share-dialog");
    const shareBackdrop = document.getElementById("share-backdrop");
    const closeShareBtn = document.getElementById("close-share");
    const copyLinkBtn = document.getElementById("copy-link");
    const shareUrlInput = document.getElementById("share-url");

    if (shareBtn && shareDialog && shareBackdrop) {
        // Open share dialog
        shareBtn.addEventListener("click", () => {
            shareDialog.style.display = "block";
            shareBackdrop.style.display = "block";

            // Select the URL text for easier copying
            if (shareUrlInput) {
                shareUrlInput.select();
            }
        });

        // Close share dialog
        closeShareBtn.addEventListener("click", () => {
            shareDialog.style.display = "none";
            shareBackdrop.style.display = "none";
        });

        // Close on backdrop click
        shareBackdrop.addEventListener("click", () => {
            shareDialog.style.display = "none";
            shareBackdrop.style.display = "none";
        });

        // Copy link functionality
        copyLinkBtn.addEventListener("click", () => {
            // Use modern Clipboard API if available, fallback to execCommand
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard
                    .writeText(shareUrlInput.value)
                    .then(() => {
                        showCopySuccess();
                    })
                    .catch((err) => {
                        console.error("Could not copy text: ", err);
                        // Fallback to old method
                        fallbackCopy();
                    });
            } else {
                fallbackCopy();
            }
        });

        // Fallback copy method using execCommand
        function fallbackCopy() {
            shareUrlInput.select();
            try {
                const successful = document.execCommand("copy");
                if (successful) {
                    showCopySuccess();
                } else {
                    console.error("Fallback: Copying text failed");
                }
            } catch (err) {
                console.error("Fallback: Oops, unable to copy", err);
            }
        }

        // Show success feedback
        function showCopySuccess() {
            const originalText = copyLinkBtn.innerHTML;
            copyLinkBtn.innerHTML = '<i class="fas fa-check"></i>';
            copyLinkBtn.style.backgroundColor = "#28a745";

            // Optional: show tooltip or toast
            Swal.fire({
                title: "Đã sao chép!",
                text: "Đường dẫn đã được sao chép vào clipboard",
                icon: "success",
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
            });

            setTimeout(() => {
                copyLinkBtn.innerHTML = originalText;
                copyLinkBtn.style.backgroundColor = "";
            }, 2000);
        }
    }
});
