/**
 * Advertisement Manager for webinar platform
 * Handles displaying advertisements, seeding, and payment flows
 */
class AdvertisementManager {
    constructor(advertisements, webinarInfo, options = {}) {
        // Configuration
        this.advertisements = advertisements || [];
        this.webinarInfo = webinarInfo;
        this.options = {
            seedingInterval: options.seedingInterval || { min: 5, max: 10 },
            paymentCheckInterval: options.paymentCheckInterval || 5000,
            paymentTimeout: options.paymentTimeout || 15 * 60, // 15 minutes in seconds
            ...options,
        };

        // State
        this.currentAd = null;
        this.currentAdTimer = null;
        this.seedingTimer = null;
        this.paymentCheckTimer = null;
        this.countdownTimer = null;
        this.paymentCountdownTimer = null;

        // DOM elements
        this.offerModal = new ModalUtil("#offerPopup");
        this.seedingUtil = new FakeSeeding({
            seedingMessages: {
                qr: "Vừa thanh toán thành công",
                form: "Vừa gửi form đăng ký thành công",
            },
        });

        // Storage key prefix
        this.storageKeyPrefix = "webinar_ad_";

        // Initialize
        this.initialize();
    }

    /**
     * Initialize the advertisement manager
     */
    initialize() {
        // Initialize event handlers
        this.initializeEventHandlers();

        // Load product data from localStorage
        this.loadProductData();
    }

    /**
     * Set up event handlers for the UI
     */
    initializeEventHandlers() {
        // Reopen popup button handler
        $("#reopenPopupButton").on("click", () => {
            if (this.currentAd) {
                this.offerModal.open();
            }
        });

        // Close popup button handler
        $("#closePopupButton").on("click", () => {
            this.offerModal.close();

            // Show the reopen button if an ad is active
            if (this.currentAd) {
                $("#reopenOffer").show();
            }
        });

        // Form submission handler
        $("#registerForm form").on("submit", (e) => {
            e.preventDefault();
            this.handleFormSubmission();
        });
    }

    /**
     * Check if an advertisement should be shown at the current time
     * @param {string} currentTime - Current video time in HH:mm:ss format
     */
    checkForAdvertisements(currentTime) {
        // If there's already an active ad, check if it should be closed
        if (this.currentAd) {
            const elapsedTime =
                this.getTimeInSeconds(currentTime) -
                this.getTimeInSeconds(this.currentAd.at);

            // If the ad duration has passed, close it
            if (elapsedTime >= this.currentAd.showIn) {
                this.closeCurrentAd();
            }
            return;
        }

        // Check if any ad should be shown at the current time
        for (const ad of this.advertisements) {
            if (currentTime === ad.at) {
                this.showAdvertisement(ad);
                break;
            }
        }
    }

    /**
     * Display an advertisement
     * @param {Object} ad - The advertisement object to display
     */
    showAdvertisement(ad) {
        this.currentAd = ad;

        // Update product info in the modal
        this.updateProductInfo(ad);

        // Open the modal
        this.offerModal.open();

        // Show the appropriate form based on ad order_method
        if (ad.order_method === "form") {
            $("#registerForm").show();
            $("#paymentForm").hide();
            $("#submitFormButton").text("Nhận tư vấn");
        } else if (ad.order_method === "qr") {
            $("#registerForm").show();
            $("#paymentForm").hide();
            $("#submitFormButton").text("Thanh toán ngay");
        }

        if (ad?.type === "product") {
            $("#product-type-ad").show();
            $("#image-type-ad").hide();
        } else if (ad?.type === "image") {
            $("#image-type-ad").show();
            $("#product-type-ad").hide();
        } else {
            //
        }

        // Reset all fields in the form
        $("#registerForm form").trigger("reset");
        // Start seeding
        this.startSeeding();

        // Start countdown timer for the reopen button
        this.startAdCountdown(ad.showIn);

        // Show the reopen button
        $("#reopenOffer").show();
    }

    /**
     * Close the current advertisement
     */
    closeCurrentAd() {
        // Clear timers
        if (this.seedingTimer) {
            clearTimeout(this.seedingTimer);
            this.seedingTimer = null;
        }

        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }

        if (this.paymentCheckTimer) {
            clearInterval(this.paymentCheckTimer);
            this.paymentCheckTimer = null;
        }

        if (this.paymentCountdownTimer) {
            clearInterval(this.paymentCountdownTimer);
            this.paymentCountdownTimer = null;
        }

        // Close the modal
        this.offerModal.close();

        // Hide the reopen button
        $("#reopenOffer").hide();

        // Reset current ad
        this.currentAd = null;
    }

    /**
     * Handle form submission
     */
    async handleFormSubmission() {
        try {
            $("#submitFormButton")
                .prop("disabled", true)
                .addClass("opacity-50")
                .text("Đang xử lý...");

            const fullName = $("#fullName").val();
            const phoneNumber = $("#phoneNumber").val();

            // Validate form
            if (!fullName || !phoneNumber) {
                alert("Vui lòng điền đầy đủ thông tin!");
                return;
            }

            const formData = {
                webinar_id: this.webinarInfo.id,
                advertisement_id: this.currentAd.id,
                order_type: this.currentAd.order_method,
                payment_note: "noted",
                name: fullName,
                phone: phoneNumber,
                email: "",
            };

            console.log("formData", formData);

            const { data: response } = await axios.post(
                apiEndpoint.checkout,
                formData
            );

            if (response?.error) {
                throw new Error(response?.message);
            }
            // If this is a QR ad, show payment form
            if (this.currentAd && this.currentAd.order_method === "qr") {
                $("#registerForm").hide();
                $("#paymentForm").show();

                const transaction_note = response?.data?.transaction_note;

                // config QR bank
                $(".bank_transaction_content").text(transaction_note);
                $(".bank_total_amount").text(this.currentAd?.detail?.price);
                const priceString = this.currentAd?.detail?.price || "0 ₫";
                const amount = priceString.replace(/[^\d]/g, ""); // Remove all non-digit characters
                const addInfo = response.data.transaction_note;
                const qrCode = `https://api.vietqr.io/image/${webinarConfig?.sepay_bank_code}-${webinarConfig?.sepay_account_number}-1.jpg?accountName=${fullName}&amount=${amount}&addInfo=${addInfo}`;
                $(".img-qr-code").attr("src", qrCode);

                // Start checking payment status
                this.startPaymentCheck(transaction_note);

                // Start payment countdown
                this.startPaymentCountdown();
            } else {
                Swal.fire({
                    icon: "success",
                    title: "Thành công",
                    text: "Đăng ký thành công!",
                });
                // If it's a form ad, close the modal
                this.offerModal.close();

                // Show the reopen button if the ad is still active
                if (this.currentAd) {
                    $("#reopenOffer").show();
                }
            }
        } catch (error) {
            console.log("error checkout", error);
            Swal.fire({
                icon: "error",
                title: "Lỗi",
                text: "Có lỗi xảy ra khi đăng ký. Vui lòng thử lại sau.",
            });
        } finally {
            $("#submitFormButton")
                .prop("disabled", false)
                .removeClass("opacity-50")
                .text("Nhận tư vấn");
        }
    }

    /**
     * Update product information in the UI
     * @param {Object} productInfo - Product information object
     */
    updateProductInfo(ad) {
        const productInfo = ad.detail;
        // Update product details in the modal
        $(".modal-title").text(ad.title);
        $(".modal-image-product").attr("src", productInfo.image);
        $(".modal-image-img").attr("src", ad.image);
        $(".modal-description").text(productInfo.title);
        $(".modal-subdescription").text(productInfo.description);
        $(".modal-price").text(productInfo.price);
        $(".modal-old-price").text(productInfo.oldPrice);
        $(".modal-discount").text(`Giảm ${productInfo.discount}`);

        // Update total sold and remaining quantity
        this.updateProductQuantity(productInfo);
    }

    /**
     * Start fake seeding for current advertisement
     */
    startSeeding() {
        if (!this.currentAd) return;

        const scheduleNextSeeding = () => {
            // Get random interval between min and max
            const interval =
                Math.floor(
                    Math.random() *
                        (this.options.seedingInterval.max -
                            this.options.seedingInterval.min +
                            1) +
                        this.options.seedingInterval.min
                ) * 1000;

            this.seedingTimer = setTimeout(() => {
                // Check if the product is still available
                const productInfo = this.getProductInfo(
                    this.currentAd.detail.id
                );
                if (productInfo.quantity > 0) {
                    // Update product quantity
                    this.incrementSoldCount(this.currentAd.detail.id);

                    // Show seeding notification
                    this.seedingUtil.showNotification(
                        this.currentAd.order_method
                    );

                    // Schedule next seeding
                    scheduleNextSeeding();
                } else {
                    this.handleSoldOut();
                }
            }, interval);
        };

        // Start the seeding process
        scheduleNextSeeding();
    }

    /**
     * Start countdown timer for advertisement duration
     * @param {number} duration - Duration in seconds
     */
    startAdCountdown(duration) {
        let remainingTime = duration;

        const updateCountdown = () => {
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            $("#adCountdown").text(
                `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
                    2,
                    "0"
                )}`
            );

            if (remainingTime <= 0) {
                clearInterval(this.countdownTimer);
            } else {
                remainingTime--;
            }
        };

        // Initial update
        updateCountdown();

        // Update every second
        this.countdownTimer = setInterval(updateCountdown, 1000);
    }

    /**
     * Start countdown timer for payment
     */
    startPaymentCountdown() {
        let remainingTime = this.options.paymentTimeout;

        const updateCountdown = () => {
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            $(".timer").text(
                `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
                    2,
                    "0"
                )}`
            );

            if (remainingTime <= 0) {
                clearInterval(this.paymentCountdownTimer);
                // Handle payment timeout
                $(".status-container").html(
                    '<strong>Trạng thái:</strong> <span class="text-danger">Đã hết hạn</span>'
                );
            } else {
                remainingTime--;
            }
        };

        // Initial update
        updateCountdown();

        // Update every second
        this.paymentCountdownTimer = setInterval(updateCountdown, 1000);
    }

    /**
     * Start checking payment status
     */
    startPaymentCheck(transaction_note) {
        this.paymentCheckTimer = setInterval(() => {
            axios
                .get(apiEndpoint.verifyPayment, {
                    params: {
                        transaction_note,
                        webinar_id: this.webinarInfo.id,
                    },
                })
                .then(({ data: response }) => {
                    if (response?.error) {
                        return;
                    }

                    if (response?.data?.message === "ok") {
                        clearInterval(this.paymentCheckTimer);

                        // Update payment status
                        $(".status-container").html(
                            '<strong>Trạng thái:</strong> <span class="text-success">Thanh toán thành công</span>'
                        );

                        // Clear payment countdown
                        if (this.paymentCountdownTimer) {
                            clearInterval(this.paymentCountdownTimer);
                        }
                    }
                });
        }, this.options.paymentCheckInterval);
    }

    /**
     * Increment the sold count and decrement available quantity
     * @param {number|string} productId - Product ID
     */
    incrementSoldCount(productId) {
        // Get current product info
        const productInfo = this.getProductInfo(productId);

        // Update counts
        productInfo.totalSold++;
        productInfo.quantity--;

        // Save to localStorage
        this.saveProductInfo(productId, productInfo);

        // Update UI
        this.updateProductQuantity(productInfo);

        // Check if product is sold out
        if (productInfo.quantity <= 0) {
            this.handleSoldOut();
        }
    }

    /**
     * Update product quantity display in the UI
     * @param {Object} productInfo - Product information
     */
    updateProductQuantity(productInfo) {
        $(".modal-total-sold").text(`${productInfo.totalSold} người đã mua`);
        $(".modal-remaining").text(`Còn lại ${productInfo.quantity} sản phẩm`);
    }

    /**
     * Handle sold out scenario
     */
    handleSoldOut() {
        // Disable CTA buttons
        $("#submitFormButton").prop("disabled", true).addClass("opacity-50");
        $("#buyNowButton").prop("disabled", true).addClass("opacity-50");

        // Update product status
        $(".modal-remaining").html(
            '<span class="text-red-500">Đã hết hàng</span>'
        );
    }

    /**
     * Get product information from storage or defaults
     * @param {number|string} productId - Product ID
     * @returns {Object} Product information
     */
    getProductInfo(productId) {
        const storageKey = `${this.storageKeyPrefix}${productId}`;
        const storedData = localStorage.getItem(storageKey);

        if (storedData) {
            return JSON.parse(storedData);
        }

        // If no stored data, find the product in advertisements
        for (const ad of this.advertisements) {
            if (ad.detail.id == productId) {
                return { ...ad.detail };
            }
        }

        // Fallback
        return {
            quantity: 0,
            totalSold: 0,
        };
    }

    /**
     * Save product information to localStorage
     * @param {number|string} productId - Product ID
     * @param {Object} productInfo - Product information
     */
    saveProductInfo(productId, productInfo) {
        const storageKey = `${this.storageKeyPrefix}${productId}`;
        localStorage.setItem(storageKey, JSON.stringify(productInfo));
    }

    /**
     * Load product data from localStorage for all advertisements
     */
    loadProductData() {
        this.advertisements.forEach((ad) => {
            const productInfo = this.getProductInfo(ad.id);
            // Update advertisement detail with stored values
            ad.detail.quantity = productInfo.quantity;
            ad.detail.totalSold = productInfo.totalSold;
        });
    }

    /**
     * Convert HH:mm:ss time format to seconds
     * @param {string} timeString - Time in HH:mm:ss format
     * @returns {number} Time in seconds
     */
    getTimeInSeconds(timeString) {
        const [hours, minutes, seconds] = timeString.split(":").map(Number);
        return hours * 3600 + minutes * 60 + seconds;
    }
}

window.AdvertisementManager = AdvertisementManager;
