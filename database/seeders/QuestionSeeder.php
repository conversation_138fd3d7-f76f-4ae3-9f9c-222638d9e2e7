<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Question;
use App\Models\Webinar;

class QuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $webinar = Webinar::first();

        if (!$webinar) {
            $this->command->error('No webinar found. Please create a webinar first.');
            return;
        }

        $questions = [
            [
                'webinar_id' => $webinar->id,
                'title' => 'Bạn đánh giá sản phẩm này như thế nào?',
                'type' => 'multiple-choice',
                'answers' => ['Rất tốt', 'Tốt', 'Trung bình', 'Kém'],
                'status' => false,
            ],
            [
                'webinar_id' => $webinar->id,
                'title' => 'Bạn sẽ giới thiệu sản phẩm này cho bạn bè không?',
                'type' => 'rating',
                'answers' => ['1', '2', '3', '4', '5'],
                'status' => true,
            ],
        ];

        foreach ($questions as $questionData) {
            Question::create($questionData);
        }

        $this->command->info('Created ' . count($questions) . ' sample questions.');
    }
}
