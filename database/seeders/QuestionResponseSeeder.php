<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Question;
use App\Models\QuestionResponse;
use App\Models\Webinar;
use Carbon\Carbon;

class QuestionResponseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $questions = Question::all();

        if ($questions->isEmpty()) {
            $this->command->error('No questions found. Please seed questions first.');
            return;
        }

        $sampleNames = [
            'Nguyễn <PERSON>n <PERSON>', 'Trần Thị B<PERSON>nh', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>n <PERSON>', '<PERSON><PERSON> T<PERSON>ị Fang', 'Đỗ <PERSON>ă<PERSON>', '<PERSON>ô T<PERSON>ị Ho<PERSON>',
            '<PERSON><PERSON><PERSON>ăn <PERSON>', '<PERSON>ý T<PERSON>ị Kim', 'Anonymous User', 'Học sinh A',
            'User Demo', 'Participant 01'
        ];

        $sampleEmails = [
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', null, null, '<EMAIL>'
        ];

        foreach ($questions as $question) {
            // Tạo 20-50 phản hồi cho mỗi câu hỏi
            $responseCount = rand(20, 50);

            for ($i = 0; $i < $responseCount; $i++) {
                $randomName = $sampleNames[array_rand($sampleNames)];
                $randomEmail = $sampleEmails[array_rand($sampleEmails)];

                // Tạo thời gian phản hồi ngẫu nhiên trong 7 ngày qua
                $respondedAt = Carbon::now()->subDays(rand(0, 7))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

                $response = [
                    'question_id' => $question->id,
                    'webinar_id' => $question->webinar_id,
                    'user_name' => $randomName,
                    'user_email' => $randomEmail,
                    'user_phone' => rand(0, 1) ? '0' . rand(900000000, 999999999) : null,
                    'session_id' => 'session_' . uniqid(),
                    'responded_at' => $respondedAt,
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => 'Mozilla/5.0 (Demo Browser)',
                ];

                // Tạo phản hồi dựa trên loại câu hỏi
                switch ($question->type) {
                    case 'multiple-choice':
                    case 'rating':
                        // Đánh giá từ 1-5 sao
                        $response['rating'] = rand(1, 5);
                        $response['selected_answers'] = [$response['rating'] - 1]; // Index từ 0
                        break;
                }

                // Một số phản hồi có text bổ sung
                if (rand(0, 3) === 0) {
                    $sampleTexts = [
                        'Rất hài lòng với sản phẩm', 'Cần cải thiện thêm',
                        'Tuyệt vời!', 'Chất lượng tốt', 'Giá cả hợp lý',
                        'Sẽ giới thiệu cho bạn bè', 'Cần hỗ trợ thêm'
                    ];
                    $response['answer_text'] = $sampleTexts[array_rand($sampleTexts)];
                }

                QuestionResponse::create($response);
            }
        }

        $this->command->info('Created sample question responses for all questions.');
    }

    private function generateRandomIP()
    {
        return rand(1, 255) . '.' . rand(0, 255) . '.' . rand(0, 255) . '.' . rand(1, 255);
    }
}
