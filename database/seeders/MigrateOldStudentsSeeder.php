<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\WebinarParticipant;
use Illuminate\Support\Facades\Hash;

class MigrateOldStudentsSeeder extends Seeder
{
    /**
     * Migrate data from webinar_students + users to webinar_participants
     */
    public function run(): void
    {
        $this->command->info('Bắt đầu migration dữ liệu từ webinar_students sang webinar_participants...');

        // Kiểm tra xem bảng webinar_students có tồn tại không
        if (!DB::getSchemaBuilder()->hasTable('webinar_students')) {
            $this->command->info('Bảng webinar_students không tồn tại. Bỏ qua migration.');
            return;
        }

        // Lấy tất cả dữ liệu từ webinar_students join với users
        $oldStudents = DB::table('webinar_students')
            ->join('users', 'webinar_students.user_id', '=', 'users.id')
            ->select([
                'webinar_students.webinar_id',
                'users.name',
                'users.email',
                'users.phone',
                'webinar_students.note',
                'webinar_students.created_at'
            ])
            ->get();

        $migratedCount = 0;
        $skippedCount = 0;

        foreach ($oldStudents as $oldStudent) {
            // Kiểm tra xem đã có participant này chưa
            $existingParticipant = WebinarParticipant::where('webinar_id', $oldStudent->webinar_id)
                ->where('email', $oldStudent->email)
                ->first();

            if ($existingParticipant) {
                // Nếu đã có, chỉ cập nhật is_student = 1
                $existingParticipant->update([
                    'is_student' => 1,
                    'note' => $oldStudent->note,
                    'password' => Hash::make('password123') // Default password
                ]);
                $migratedCount++;
                $this->command->info("Cập nhật participant thành học sinh: {$oldStudent->email}");
            } else {
                // Kiểm tra xem có participant với email này trong webinar khác không
                $participantInOtherWebinar = WebinarParticipant::where('email', $oldStudent->email)
                    ->where('webinar_id', '!=', $oldStudent->webinar_id)
                    ->first();

                if ($participantInOtherWebinar) {
                    // Tạo participant mới cho webinar này
                    WebinarParticipant::create([
                        'webinar_id' => $oldStudent->webinar_id,
                        'name' => $oldStudent->name,
                        'email' => $oldStudent->email,
                        'phone' => $oldStudent->phone,
                        'password' => Hash::make('password123'),
                        'is_student' => 1,
                        'note' => $oldStudent->note,
                        'joined_at' => $oldStudent->created_at,
                        'join_count' => 0,
                        'ip_address' => '127.0.0.1'
                    ]);
                    $migratedCount++;
                    $this->command->info("Tạo học sinh mới: {$oldStudent->email}");
                } else {
                    // Tạo participant hoàn toàn mới
                    WebinarParticipant::create([
                        'webinar_id' => $oldStudent->webinar_id,
                        'name' => $oldStudent->name,
                        'email' => $oldStudent->email,
                        'phone' => $oldStudent->phone,
                        'password' => Hash::make('password123'),
                        'is_student' => 1,
                        'note' => $oldStudent->note,
                        'joined_at' => $oldStudent->created_at,
                        'join_count' => 0,
                        'ip_address' => '127.0.0.1'
                    ]);
                    $migratedCount++;
                    $this->command->info("Tạo học sinh mới: {$oldStudent->email}");
                }
            }
        }

        $this->command->info("Migration hoàn thành!");
        $this->command->info("- Đã migrate: {$migratedCount} học sinh");
        $this->command->info("- Bỏ qua: {$skippedCount} học sinh");
        $this->command->info("- Mật khẩu mặc định cho tất cả học sinh: password123");
        $this->command->warn("Lưu ý: Bảng webinar_students cũ vẫn được giữ lại. Bạn có thể xóa sau khi kiểm tra.");
    }
} 