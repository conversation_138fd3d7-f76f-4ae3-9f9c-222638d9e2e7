<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Module;

class ModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $modules = [
         
 
            [
                'name' => 'livestream_marketing',
                'display_name' => 'Livestream Trực Tiếp Bán hàng',
                'description' => 'Hệ thống livestream tích hợp Facebook Live, YouTube Live và TopID.vn với tính năng đẩy quảng cáo và chốt đơn realtime',
                'icon' => 'fas fa-broadcast-tower',
                'route' => null,
                'permission' => 'livestream marketing manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 11,
                'version' => '1.0.0',
                'price' => 4000000,
                'currency' => 'VND',
                'category' => 'livestream',
                'config' => [
                    'facebook_live' => true,
                    'youtube_live' => true,
                    'topid_integration' => true,
                    'realtime_ads' => true,
                    'order_tracking' => true,
                    'auto_response' => true,
                    'analytics_dashboard' => true
                ]
            ],
            [
                'name' => 'livestream_education',
                'display_name' => 'Livestream Trực Tiếp Dạy Học',
                'description' => 'Hệ thống livestream giáo dục tích hợp TopID.vn với tính năng tương tác câu hỏi, quiz và nhận đáp án realtime từ học sinh',
                'icon' => 'fas fa-chalkboard-teacher',
                'route' => null,
                'permission' => 'livestream education manage',
                'is_enabled' => false,
                'is_core' => false,
                'sort_order' => 12,
                'version' => '1.0.0',
                'price' => 4000000,
                'currency' => 'VND',
                'category' => 'education',
                'config' => [
                    'topid_integration' => true,
                    'interactive_quiz' => true,
                    'realtime_qa' => true,
                    'student_tracking' => true,
                    'whiteboard' => true,
                    'screen_sharing' => true,
                    'recording' => true,
                    'attendance_tracking' => true
                ]
            ]
        ];

        foreach ($modules as $moduleData) {
            Module::updateOrCreate(
                ['name' => $moduleData['name']],
                $moduleData
            );
        }
    }
}
