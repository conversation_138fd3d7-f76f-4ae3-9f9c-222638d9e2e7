<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PaymentSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * @throws \Exception
     */
    public function run(): void
    {
        // Add payment settings
        $settings = [
            [
                'key' => 'sepay_api_key',
                'value' => generateApiKey(), // Generate a secure API key by default
                'label' => 'Khóa API',
                'description' => 'Khóa API bảo mật cho tích hợp Sepay',
                'type' => 'text',
                'group' => 'payment',
                'sort_order' => 10,
            ],
            [
                'key' => 'sepay_merchant_name',
                'value' => '',
                'label' => 'Tên tài khoản',
                'description' => 'Tên hiển thị của người nhận thanh toán',
                'type' => 'text',
                'group' => 'payment',
                'sort_order' => 20,
            ],
            [
                'key' => 'sepay_bank',
                'value' => '',
                'label' => 'Ngân hàng',
                'description' => 'Nhà cung cấp dịch vụ ngân hàng',
                'type' => 'text',
                'group' => 'payment',
                'sort_order' => 30,
            ],
            [
                'key' => 'sepay_bank_code',
                'value' => '',
                'label' => 'Mã ngân hàng',
                'description' => 'Mã ngân hàng cho xử lý thanh toán',
                'type' => 'text',
                'group' => 'payment',
                'sort_order' => 35,
            ],
            [
                'key' => 'sepay_account_number',
                'value' => '',
                'label' => 'Số tài khoản',
                'description' => 'Số tài khoản ngân hàng nhận thanh toán',
                'type' => 'text',
                'group' => 'payment',
                'sort_order' => 40,
            ],
            [
                'key' => 'sepay_active',
                'value' => '0',
                'label' => 'Kích hoạt Sepay',
                'description' => 'Bật/tắt cổng thanh toán Sepay',
                'type' => 'boolean',
                'group' => 'payment',
                'sort_order' => 50,
            ],
        ];

        foreach ($settings as $setting) {
            // Check if setting already exists
            $exists = DB::table('site_settings')->where('key', $setting['key'])->exists();

            if (!$exists) {
                DB::table('site_settings')->insert($setting);
            }
        }
    }
}
