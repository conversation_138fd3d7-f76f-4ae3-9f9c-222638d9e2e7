<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\User;
use App\Models\Webinar;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Faker\Factory as Faker;

class OrderSeeder extends Seeder
{
    /**
     * The number of orders to create.
     */
    protected $totalOrders;

    /**
     * The batch size for database inserts.
     */
    protected $batchSize;

    /**
     * Create a new seeder instance.
     */
    public function __construct(int $totalOrders = 200000, int $batchSize = 1000)
    {
        $this->totalOrders = $totalOrders;
        $this->batchSize = $batchSize;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();
        $userIds = User::role('telesales')->pluck('id')->toArray();

        // If no telesale users, create some default ones for assignment
        if (empty($userIds)) {
            $userIds = [1]; // Default to first user if no telesale users
        }

        $webinarIds = Webinar::pluck('id')->toArray();

        // If no webinars, create at least one for testing
        if (empty($webinarIds)) {
            echo "No webinars found. Creating a default webinar for testing...\n";
            $webinar = Webinar::create([
                'title' => 'Test Webinar',
                'slug' => 'test-webinar',
                'description' => 'This is a test webinar created by the seeder',
                'start_time' => now()->addDays(7),
                'duration' => 60,
            ]);
            $webinarIds = [$webinar->id];
        }

        // Remove NULL from care statuses since the column doesn't accept NULL
        $careStatuses = ['new', 'contacted', 'interested', 'not_interested', 'callback', 'converted', 'closed'];
        $paymentStatuses = ['pending', 'paid', 'failed'];
        $orderTypes = ['form', 'qr'];

        $batches = ceil($this->totalOrders / $this->batchSize);

        echo "Generating {$this->totalOrders} orders in {$batches} batches...\n";

        for ($b = 0; $b < $batches; $b++) {
            $orders = [];
            $currentBatchSize = min($this->batchSize, $this->totalOrders - ($b * $this->batchSize));

            for ($i = 0; $i < $currentBatchSize; $i++) {
                $createdAt = $faker->dateTimeBetween('-1 year', 'now');
                $careStatus = $faker->randomElement($careStatuses);

                // Only set follow-up date for certain care statuses
                $nextFollowDate = null;
                if (in_array($careStatus, ['new', 'contacted', 'interested', 'callback'])) {
                    // Create a mix of follow-up dates - past, today, and future
                    $followDateType = $faker->randomElement(['past', 'today', 'future', 'this_week']);
                    switch ($followDateType) {
                        case 'past':
                            $nextFollowDate = $faker->dateTimeBetween('-30 days', '-1 day');
                            break;
                        case 'today':
                            $nextFollowDate = Carbon::today()->addHours($faker->numberBetween(9, 17));
                            break;
                        case 'this_week':
                            $nextFollowDate = Carbon::today()->addDays($faker->numberBetween(1, 7));
                            break;
                        case 'future':
                            $nextFollowDate = $faker->dateTimeBetween('+1 day', '+30 days');
                            break;
                    }
                }

                // Set assigned user based on certain conditions
                $assignedTo = null;
                if (in_array($careStatus, ['new', 'contacted', 'interested', 'callback', 'not_interested'])) {
                    $assignedTo = $faker->randomElement($userIds);
                }

                // Purchase time should be before creation time
                $purchaseTime = (clone $createdAt)->modify('-' . $faker->numberBetween(1, 72) . ' hours');

                $orders[] = [
                    'name' => $faker->name,
                    'phone' => $faker->numerify('09########'),
                    'email' => $faker->email,
                    'product_name' => $faker->randomElement(['Basic Package', 'Pro Package', 'Premium Package', 'Enterprise Package']),
                    'price' => $faker->randomElement([99000, 299000, 499000, 999000, 1999000]),
                    'purchase_time' => $purchaseTime,
                    'order_type' => $faker->randomElement($orderTypes),
                    'transaction_id' => $faker->optional(0.7)->uuid,
                    'payment_note' => $faker->optional(0.3)->sentence,
                    'webinar_id' => $faker->randomElement($webinarIds),
                    'payment_status' => $faker->randomElement($paymentStatuses),
                    'care_status' => $careStatus,
                    'next_follow_date' => $nextFollowDate,
                    'assigned_to' => $assignedTo,
                    'is_priority' => $faker->boolean(20), // 20% chance of being priority
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                ];
            }

            // Insert the batch
            DB::table('orders')->insert($orders);

            $completedCount = min(($b + 1) * $this->batchSize, $this->totalOrders);
            $percentage = round(($completedCount / $this->totalOrders) * 100, 2);
            echo "Progress: {$completedCount}/{$this->totalOrders} orders ({$percentage}%)\n";

            // Free up memory
            unset($orders);
        }

        echo "Order seeding completed successfully!\n";
    }
}
