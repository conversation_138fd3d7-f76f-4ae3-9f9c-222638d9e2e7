<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = ["webinars", "questions", "product", "order", "advertisements", "users", "roles"];
        $last_pers = ["view only you", "index", "create", "update", "delete", "show"];

        $administratorRole = Role::firstOrCreate(['name' => 'administrator']);
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $telesalesRole = Role::firstOrCreate(['name' => 'telesales']);

        Permission::firstOrCreate(['name' => "dashboard"]);
        Permission::firstOrCreate(['name' => "settings"]);
        Permission::firstOrCreate(['name' => "telesales"]);
        Permission::firstOrCreate(['name' => "limit webinars"]);

        Permission::firstOrCreate(['name' => "settings tab general"]);
        Permission::firstOrCreate(['name' => "settings tab appearance"]);
        Permission::firstOrCreate(['name' => "settings tab security"]);
        Permission::firstOrCreate(['name' => "settings tab scripts"]);
        Permission::firstOrCreate(['name' => "settings tab email"]);
        Permission::firstOrCreate(['name' => "settings tab notifications"]);
        Permission::firstOrCreate(['name' => "settings tab payment"]);
        Permission::firstOrCreate(['name' => "settings tab limits"]);

        foreach ($permissions as $permission) {
            foreach ($last_pers as $item) {

                if (in_array($permission, ["order", "telesales", "advertisements", "questions"]) && $item == "show") {
                    break;
                }
                if (!in_array($permission, ["webinars", "product", "advertisements", "questions"]) && $item == "view Only you") {
                    break;
                }
                Permission::firstOrCreate(['name' => $permission . " " . $item]);
            }
        }

        $administratorRole->givePermissionTo(Permission::all());
        $adminRole->givePermissionTo(Permission::whereNotIn("name", [
            "webinars view only you",
            "advertisements view only you",
            "questions view only you",
            "product view only you",
            "limit webinars"])->get());
        $telesalesRole->givePermissionTo(["telesales"]);
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'password' => Hash::make('Quyenanh_2016'),
                'webinar_limit' => 999999, // Unlimited for admin
            ]
        );
        $admin->assignRole('administrator');

        // Create test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('password'),
                'webinar_limit' => 5,
            ]
        );
        $user->assignRole('admin');

        // Create telesale user
        $telesale = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Telesales',
                'password' => Hash::make('password'),
                'webinar_limit' => 0,
            ]
        );
        $telesale->assignRole('telesales');
    }
}
