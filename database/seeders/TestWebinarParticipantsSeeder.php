<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use Illuminate\Support\Facades\Hash;

class TestWebinarParticipantsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the second webinar for testing
        $webinar = Webinar::skip(1)->first();
        
        if (!$webinar) {
            $this->command->info('Không có webinar nào trong database. Vui lòng tạo webinar trước.');
            return;
        }

        $this->command->info("Tạo test data cho webinar: {$webinar->title}");

        // Tạo 5 participants thường (is_student = 0)
        for ($i = 1; $i <= 5; $i++) {
            WebinarParticipant::create([
                'webinar_id' => $webinar->id,
                'name' => "Participant {$i}",
                'email' => "participant{$i}@example.com",
                'phone' => "090000000{$i}",
                'is_student' => 0,
                'joined_at' => now(),
                'join_count' => 1,
                'ip_address' => '127.0.0.1',
            ]);
        }

        // Tạo 3 học sinh (is_student = 1)
        for ($i = 1; $i <= 3; $i++) {
            WebinarParticipant::create([
                'webinar_id' => $webinar->id,
                'name' => "Học sinh {$i}",
                'email' => "student{$i}@example.com",
                'phone' => "091000000{$i}",
                'password' => Hash::make('password123'),
                'is_student' => 1,
                'note' => "Ghi chú cho học sinh {$i}",
                'joined_at' => now(),
                'join_count' => 1,
                'ip_address' => '127.0.0.1',
            ]);
        }

        $this->command->info('Đã tạo thành công 5 participants và 3 học sinh!');
    }
} 