<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->optional()->safeEmail,
            'product_name' => $this->faker->word,
            'price' => $this->faker->randomFloat(2, 100000, 1000000),
            'purchase_time' => $this->faker->dateTimeThisYear(),
            'order_type' => $this->faker->randomElement(['form', 'qr']),
            'transaction_id' => $this->faker->optional()->uuid,
            'transaction_note' => $this->faker->optional()->sentence,
            'payment_note' => $this->faker->optional()->text,
            'webinar_id' => \App\Models\Webinar::factory(),
            'payment_status' => $this->faker->randomElement(['pending', 'paid', 'failed']),
            'care_status' => $this->faker->randomElement(['new', 'contacted', 'interested', 'not_interested', 'callback', 'converted', 'closed']),
            'reminder_type' => $this->faker->optional()->randomElement(['none', 'email', 'notification', 'both']),
            'is_priority' => $this->faker->boolean,
            'next_follow_date' => $this->faker->optional()->dateTimeThisYear(),
            'assigned_to' => \App\Models\User::factory(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
} 