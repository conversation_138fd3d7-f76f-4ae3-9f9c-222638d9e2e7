<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->word,
            'original_price' => $this->faker->randomFloat(2, 100000, 1000000),
            'sale_price' => $this->faker->optional()->randomFloat(2, 50000, 900000),
            'image' => $this->faker->optional()->imageUrl(),
            'stock_quantity' => $this->faker->numberBetween(1, 1000),
            'tags' => json_encode([$this->faker->word, $this->faker->word]),
            'user_id' => \App\Models\User::factory(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
} 