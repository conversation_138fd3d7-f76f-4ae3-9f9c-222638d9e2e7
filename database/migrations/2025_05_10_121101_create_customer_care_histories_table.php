<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_care_histories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('user_id')->nullable(); // Người thực hiện
            $table->enum('contact_type', ['phone', 'email', 'sms', 'zalo', 'other'])->default('phone');
            $table->text('content');
            $table->enum('status', ['success', 'no_answer', 'callback', 'not_interested', 'wrong_number', 'other']);
            $table->dateTime('callback_time')->nullable(); // Thời gian gọi lại nếu có
            $table->timestamps();
            
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_care_histories');
    }
};
