<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->string('product_name');
            $table->decimal('price', 15, 2);
            $table->timestamp('purchase_time');
            $table->enum('order_type', ['form', 'qr'])->default('form');
            $table->string('transaction_id')->nullable();
            $table->string("transaction_note")->nullable()->comment("NDCK");
            $table->text('payment_note')->nullable();
            $table->unsignedBigInteger('webinar_id')->nullable();
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            $table->enum('care_status', [
                'new', 'contacted', 'interested', 'not_interested',
                'callback', 'converted', 'closed'
            ])->default('new');
            $table->enum('reminder_type', ['none', 'email', 'notification', 'both'])->nullable();
            $table->boolean('is_priority')->default(false);
            $table->dateTime('next_follow_date')->nullable();
            $table->unsignedBigInteger('assigned_to')->nullable();
            $table->foreign('assigned_to')->references('id')->on('users')->onDelete('set null');
            $table->index('phone');
            $table->index(['assigned_to', 'next_follow_date']);
            $table->index(['care_status', 'payment_status']);
            $table->index(['webinar_id', 'created_at']);

            $table->timestamps();

            // Foreign key if needed
            $table->foreign('webinar_id')->references('id')->on('webinars')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
