<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_care_notes', function (Blueprint $table) {
            $table->integer("order_id")->after("id");
            $table->integer("user_id")->after("order_id");
            $table->text("content")->after("user_id");
            $table->enum("priority",["high","medium","low"])->after("content");
            $table->boolean("is_pinned")->after("priority");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_care_note', function (Blueprint $table) {
            //
        });
    }
};
