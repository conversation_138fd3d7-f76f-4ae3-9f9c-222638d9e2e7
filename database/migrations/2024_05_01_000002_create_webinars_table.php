<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webinars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('speaker');
            $table->string('virtual_viewers')->default('10');
            $table->integer('waiting_time')->default(30); // Default waiting time in minutes
            $table->string('join_code')->unique();
            $table->string('join_url');
            $table->json('join_settings')->nullable();
            $table->json('notification_settings')->nullable();
            $table->string('video_path')->nullable();
            $table->string('s3_url')->nullable();
            $table->json('schedules')->nullable();
            $table->json('seeded_comments')->nullable()->comment('Stores pre-defined comments for seeding the webinar chat');
            $table->json('advertisement_slots')->nullable();
            $table->integer('video_duration_minutes')->nullable()->comment('Thời lượng video tính bằng phút');
            $table->boolean('allow_replay')->default(true)
                ->comment('Cấu hình cho phép xem lại video sau khi webinar kết thúc');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webinars');
    }
};
