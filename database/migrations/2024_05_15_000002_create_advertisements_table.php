<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advertisements', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->enum('type', ['image', 'product']);
            $table->string('image')->nullable();
            $table->string('url')->nullable();
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('virtual_quantity')->nullable();
            $table->enum('order_method', ['form', 'qr'])->default('form');
            $table->boolean('require_name')->default(false);
            $table->boolean('require_email')->default(false);
            $table->boolean('require_phone')->default(false);
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer("original_price")->default(0);
            $table->integer("sale_price")->default(0);
            $table->integer('display_time')->default(10)
                ->comment('Thời gian hiển thị quảng cáo (giây)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advertisements');
    }
};
