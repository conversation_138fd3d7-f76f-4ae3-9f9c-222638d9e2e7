<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->default(0)->after('version')->comment('Giá module (0 = miễn phí)');
            $table->string('currency', 3)->default('VND')->after('price')->comment('Đơn vị tiền tệ');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            $table->dropColumn(['price', 'currency']);
        });
    }
};
