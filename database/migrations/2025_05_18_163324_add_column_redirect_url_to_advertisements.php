<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            $table->enum('order_method', ['form', 'qr', 'url'])->default('form')->change();
            $table->string("redirect_url")->nullable()->comment("url đích của quảng cáo nếu chọn order_method == url");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            $table->dropColumn("redirect_url");
        });
    }
};
