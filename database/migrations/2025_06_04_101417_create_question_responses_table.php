<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('question_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('question_id')->constrained()->onDelete('cascade');
            $table->foreignId('webinar_id')->constrained()->onDelete('cascade');
            $table->string('user_name')->nullable(); // Tên người trả lời
            $table->string('user_email')->nullable(); // Email người trả lời  
            $table->string('user_phone')->nullable(); // SĐT người trả lời
            $table->string('session_id'); // Session ID để track anonymous users
            $table->json('selected_answers'); // <PERSON><PERSON><PERSON> đáp án được chọn
            $table->text('answer_text')->nullable(); // Nếu có câu trả lời dạng text
            $table->integer('rating')->nullable(); // Cho loại rating (1-5)
            $table->timestamp('responded_at')->useCurrent(); // Thời gian trả lời
            $table->string('ip_address')->nullable(); // IP address
            $table->string('user_agent')->nullable(); // Browser info
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['question_id', 'responded_at']);
            $table->index(['webinar_id', 'responded_at']);
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('question_responses');
    }
};
