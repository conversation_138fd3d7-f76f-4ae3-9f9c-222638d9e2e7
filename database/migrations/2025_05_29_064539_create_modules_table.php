<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('modules', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // Tên module (unique key)
            $table->string('display_name'); // Tên hiển thị
            $table->text('description')->nullable(); // Mô tả module
            $table->string('icon')->default('fas fa-puzzle-piece'); // Icon FontAwesome
            $table->string('route')->nullable(); // Route chính của module
            $table->string('permission')->nullable(); // Permission cần thiết
            $table->boolean('is_enabled')->default(false); // Trạng thái kích hoạt
            $table->boolean('is_core')->default(false); // Module core không thể tắt
            $table->integer('sort_order')->default(0); // Thứ tự sắp xếp
            $table->string('version')->default('1.0.0'); // Phiên bản module
            $table->json('config')->nullable(); // Cấu hình module (JSON)
            $table->string('category')->default('general'); // Danh mục module
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('modules');
    }
};
