<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('smart_link_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('smart_link_id')->constrained()->onDelete('cascade');
            $table->foreignId('webinar_id')->constrained()->onDelete('cascade');
            $table->integer('schedule_index')->comment('Index của lịch trong webinar.schedules array');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['smart_link_id', 'sort_order']);
            $table->index('webinar_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('smart_link_schedules');
    }
};
