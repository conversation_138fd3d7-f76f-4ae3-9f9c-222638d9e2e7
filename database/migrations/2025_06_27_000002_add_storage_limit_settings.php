<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add storage limit settings
        $settings = [
            [
                'key' => 'video_storage_limit_gb',
                'value' => '10',
                'type' => 'text',
                'group' => 'limits',
                'label' => 'Giới hạn dung lượng video (GB)',
                'description' => 'Giới hạn tổng dung lượng video có thể upload (tính bằng GB)',
                'is_public' => false,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'system_storage_limit_gb',
                'value' => '5',
                'type' => 'text',
                'group' => 'limits',
                'label' => 'Giới hạn dung lượng hệ thống (GB)',
                'description' => 'Giới hạn dung lượng code + database (tính bằng GB)',
                'is_public' => false,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'storage_limit_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'limits',
                'label' => 'Kích hoạt kiểm tra giới hạn dung lượng',
                'description' => 'Bật/tắt tính năng kiểm tra giới hạn dung lượng',
                'is_public' => false,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'storage_limit_message',
                'value' => 'Hệ thống đã vượt quá giới hạn dung lượng cho phép. Vui lòng liên hệ admin để nâng cấp dung lượng.',
                'type' => 'textarea',
                'group' => 'limits',
                'label' => 'Thông báo khi vượt quá giới hạn',
                'description' => 'Thông báo hiển thị khi hệ thống vượt quá giới hạn dung lượng',
                'is_public' => false,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('site_settings')->insert($setting);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('site_settings')->whereIn('key', [
            'video_storage_limit_gb',
            'system_storage_limit_gb',
            'storage_limit_enabled',
            'storage_limit_message'
        ])->delete();
    }
};
