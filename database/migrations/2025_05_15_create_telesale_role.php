<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions if they don't exist
        if (!Permission::where('name', 'view telesales')->exists()) {
            Permission::create(['name' => 'view telesales', 'guard_name' => 'web']);
        }

        if (!Permission::where('name', 'manage orders')->exists()) {
            Permission::create(['name' => 'manage orders', 'guard_name' => 'web']);
        }

        // Create telesale role if it doesn't exist
        if (!Role::where('name', 'telesales')->exists()) {
            $telesaleRole = Role::create(['name' => 'telesales', 'guard_name' => 'web']);
            $telesaleRole->givePermissionTo(['view telesales', 'manage orders']);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the telesale role
        $role = Role::where('name', 'telesales')->first();
        if ($role) {
            $role->delete();
        }

        // Optional: Remove the permissions
        /*
        $permission = Permission::where('name', 'view telesales')->first();
        if ($permission) {
            $permission->delete();
        }

        $permission = Permission::where('name', 'manage orders')->first();
        if ($permission) {
            $permission->delete();
        }
        */
    }
};
