<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webinar_participants', function (Blueprint $table) {
            $table->string('password')->nullable()->after('phone')->comment('<PERSON>ật khẩu cho tài khoản học sinh (nullable)');
            $table->boolean('is_student')->default(0)->after('password')->comment('0: Participant thường, 1: Học sinh');
            $table->text('note')->nullable()->after('is_student')->comment('<PERSON>hi chú về học sinh');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webinar_participants', function (Blueprint $table) {
            $table->dropColumn(['password', 'is_student', 'note']);
        });
    }
}; 