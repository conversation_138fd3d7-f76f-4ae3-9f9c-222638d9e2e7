<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('site_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('text');
            $table->string('group')->default('general');
            $table->string('label')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Insert default settings
        $this->insertDefaultSettings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('site_settings');
    }

    /**
     * Insert default settings.
     */
    private function insertDefaultSettings(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_title',
                'value' => 'Hệ Thống Quản Lý Webinar',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Tiêu đề trang web',
                'description' => 'Tiêu đề hiển thị trên trình duyệt và các trang',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site_description',
                'value' => 'Hệ thống quản lý webinar chuyên nghiệp',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'Mô tả trang web',
                'description' => 'Mô tả ngắn gọn về trang web (dùng cho SEO)',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'admin_logo',
                'value' => null,
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'Logo quản trị',
                'description' => 'Logo hiển thị trong trang quản trị (kích thước khuyến nghị: 200x60px)',
                'is_public' => true,
                'sort_order' => 10,
            ],
            [
                'key' => 'favicon',
                'value' => null,
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'Favicon',
                'description' => 'Icon hiển thị trên tab trình duyệt (kích thước khuyến nghị: 32x32px)',
                'is_public' => true,
                'sort_order' => 11,
            ],
            [
                'key' => 'footer_text',
                'value' => '© 2025 Hệ Thống Quản Lý Webinar. Tất cả quyền được bảo lưu.',
                'type' => 'textarea',
                'group' => 'appearance',
                'label' => 'Nội dung footer',
                'description' => 'Văn bản hiển thị ở cuối trang',
                'is_public' => true,
                'sort_order' => 20,
            ],

            // Security Settings
            [
                'key' => 'recaptcha_site_key',
                'value' => null,
                'type' => 'text',
                'group' => 'security',
                'label' => 'Google reCAPTCHA v3 Site Key',
                'description' => 'Site Key từ Google reCAPTCHA',
                'is_public' => true,
                'sort_order' => 30,
            ],
            [
                'key' => 'recaptcha_secret_key',
                'value' => null,
                'type' => 'password',
                'group' => 'security',
                'label' => 'Google reCAPTCHA v3 Secret Key',
                'description' => 'Secret Key từ Google reCAPTCHA',
                'is_public' => false,
                'sort_order' => 31,
            ],

            // Custom Scripts
            [
                'key' => 'header_scripts',
                'value' => null,
                'type' => 'code',
                'group' => 'scripts',
                'label' => 'Scripts trong header',
                'description' => 'Mã JavaScript hoặc CSS được thêm vào phần head của trang',
                'is_public' => false,
                'sort_order' => 40,
            ],
            [
                'key' => 'footer_scripts',
                'value' => null,
                'type' => 'code',
                'group' => 'scripts',
                'label' => 'Scripts trong footer',
                'description' => 'Mã JavaScript được thêm vào cuối trang trước đóng thẻ body',
                'is_public' => false, 
                'sort_order' => 41,
            ],
            
            // Email Settings
            [
                'key' => 'mail_from_address',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'email',
                'label' => 'Địa chỉ email gửi',
                'description' => 'Địa chỉ email dùng để gửi thư từ hệ thống',
                'is_public' => false,
                'sort_order' => 50,
            ],
            [
                'key' => 'mail_from_name',
                'value' => 'Hệ Thống Quản Lý Webinar',
                'type' => 'text',
                'group' => 'email',
                'label' => 'Tên người gửi',
                'description' => 'Tên hiển thị khi gửi email',
                'is_public' => false,
                'sort_order' => 51,
            ],
            [
                'key' => 'mail_mailer',
                'value' => 'smtp',
                'type' => 'text',
                'group' => 'email',
                'label' => 'Driver email',
                'description' => 'Driver email sử dụng (smtp, sendmail, ...)',
                'is_public' => false,
                'sort_order' => 52,
            ],
            [
                'key' => 'mail_host',
                'value' => 'smtp.mailtrap.io',
                'type' => 'text',
                'group' => 'email',
                'label' => 'SMTP Host',
                'description' => 'Địa chỉ máy chủ SMTP',
                'is_public' => false,
                'sort_order' => 53,
            ],
            [
                'key' => 'mail_port',
                'value' => '2525',
                'type' => 'number',
                'group' => 'email',
                'label' => 'SMTP Port',
                'description' => 'Cổng kết nối SMTP',
                'is_public' => false,
                'sort_order' => 54,
            ],
            [
                'key' => 'mail_username',
                'value' => null,
                'type' => 'text',
                'group' => 'email',
                'label' => 'SMTP Username',
                'description' => 'Tên đăng nhập SMTP',
                'is_public' => false,
                'sort_order' => 55,
            ],
            [
                'key' => 'mail_password',
                'value' => null,
                'type' => 'password',
                'group' => 'email',
                'label' => 'SMTP Password',
                'description' => 'Mật khẩu SMTP',
                'is_public' => false,
                'sort_order' => 56,
            ],
            [
                'key' => 'mail_encryption',
                'value' => 'tls',
                'type' => 'text',
                'group' => 'email',
                'label' => 'Mã hóa SMTP',
                'description' => 'Loại mã hóa SMTP (tls, ssl)',
                'is_public' => false,
                'sort_order' => 57,
            ],
            
            // Thông báo Telegram
            [
                'key' => 'telegram_enabled',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'Bật thông báo Telegram',
                'description' => 'Bật/tắt tính năng gửi thông báo qua Telegram',
                'is_public' => false,
                'sort_order' => 70,
            ],
            [
                'key' => 'telegram_bot_token',
                'value' => null,
                'type' => 'password',
                'group' => 'notifications',
                'label' => 'Bot Token Telegram',
                'description' => 'Token của bot Telegram (lấy từ @BotFather)',
                'is_public' => false,
                'sort_order' => 71,
            ],
            [
                'key' => 'telegram_chat_ids',
                'value' => null,
                'type' => 'textarea',
                'group' => 'notifications',
                'label' => 'Danh sách Chat ID',
                'description' => 'Danh sách Chat ID nhận thông báo (mỗi ID một dòng)',
                'is_public' => false,
                'sort_order' => 72,
            ],
            [
                'key' => 'telegram_notify_new_participant',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'Thông báo khi có người tham gia',
                'description' => 'Gửi thông báo khi có người điền form tham gia webinar',
                'is_public' => false,
                'sort_order' => 73,
            ],
            [
                'key' => 'telegram_notify_new_comment',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'Thông báo khi có bình luận mới',
                'description' => 'Gửi thông báo khi có người bình luận trong webinar',
                'is_public' => false,
                'sort_order' => 74,
            ],
            [
                'key' => 'telegram_bot_username',
                'value' => null,
                'type' => 'text',
                'group' => 'notifications',
                'label' => 'Tên người dùng Bot',
                'description' => 'Tên người dùng của bot Telegram (ví dụ: @your_bot_name)',
                'is_public' => false,
                'sort_order' => 75,
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('site_settings')->insert($setting);
        }
    }
};
