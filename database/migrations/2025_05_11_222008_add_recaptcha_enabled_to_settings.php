<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add recaptcha_enabled setting if it doesn't exist
        if (!Setting::where('key', 'recaptcha_enabled')->exists()) {
            Setting::create([
                'key' => 'recaptcha_enabled',
                'value' => '0', // Disabled by default
                'type' => 'boolean',
                'group' => 'security',
                'label' => 'Kích hoạt reCAPTCHA',
                'description' => 'Bật/tắt bảo vệ reCAPTCHA cho các form đăng nhập, đăng ký, v.v.',
                'is_public' => false,
                'sort_order' => 32, // After site key and secret key
            ]);

            // Clear settings cache
            Setting::clearCache();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the setting if needed
        Setting::where('key', 'recaptcha_enabled')->delete();
        
        // Clear settings cache
        Setting::clearCache();
    }
};
