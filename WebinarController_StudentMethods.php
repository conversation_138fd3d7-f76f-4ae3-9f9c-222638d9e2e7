<?php

// <PERSON><PERSON><PERSON> là file demo hiển thị các method cần thêm vào WebinarController
// KHÔNG chạy file này, chỉ copy các method vào WebinarController thực tế

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Webinar;

class WebinarController extends Controller
{
    // ... existing methods ...

    /**
     * Show student management page
     */
    public function studentsManager(Webinar $webinar)
    {
        return view('webinars.students', compact('webinar'));
    }

    /**
     * Search users by email or phone
     */
    public function searchUsers(Request $request, Webinar $webinar)
    {
        $query = $request->get('q');
        
        $users = User::where(function($q) use ($query) {
            $q->where('email', 'like', "%{$query}%")
              ->orWhere('phone', 'like', "%{$query}%")
              ->orWhere('name', 'like', "%{$query}%");
        })
        ->whereNotIn('id', function($q) use ($webinar) {
            $q->select('user_id')
              ->from('webinar_students')
              ->where('webinar_id', $webinar->id);
        })
        ->limit(10)
        ->get();

        return response()->json([
            'success' => true,
            'users' => $users
        ]);
    }

    /**
     * List students in webinar
     */
    public function listStudents(Request $request, Webinar $webinar)
    {
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $perPage = 10;

        $query = DB::table('webinar_students')
            ->join('users', 'webinar_students.user_id', '=', 'users.id')
            ->where('webinar_students.webinar_id', $webinar->id)
            ->select([
                'users.id',
                'users.name',
                'users.email',
                'users.phone',
                'webinar_students.created_at as joined_at',
                'webinar_students.note'
            ]);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('users.name', 'like', "%{$search}%")
                  ->orWhere('users.email', 'like', "%{$search}%")
                  ->orWhere('users.phone', 'like', "%{$search}%");
            });
        }

        $total = $query->count();
        $students = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->orderBy('webinar_students.created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $students,
            'pagination' => [
                'current_page' => (int)$page,
                'last_page' => ceil($total / $perPage),
                'total' => $total
            ]
        ]);
    }

    /**
     * Add existing student to webinar
     */
    public function addStudent(Request $request, Webinar $webinar)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        // Check if student already exists in webinar
        $exists = DB::table('webinar_students')
            ->where('webinar_id', $webinar->id)
            ->where('user_id', $request->user_id)
            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Học sinh đã có trong lớp học này'
            ]);
        }

        DB::table('webinar_students')->insert([
            'webinar_id' => $webinar->id,
            'user_id' => $request->user_id,
            'note' => $request->note ?? '',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã thêm học sinh vào lớp thành công'
        ]);
    }

    /**
     * Create new student and add to webinar
     */
    public function createStudent(Request $request, Webinar $webinar)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|string|max:20|unique:users,phone',
            'password' => 'required|string|min:6',
            'note' => 'nullable|string'
        ]);

        // Create user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'email_verified_at' => now()
        ]);

        // Add to webinar
        DB::table('webinar_students')->insert([
            'webinar_id' => $webinar->id,
            'user_id' => $user->id,
            'note' => $request->note ?? '',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã tạo học sinh mới và thêm vào lớp thành công'
        ]);
    }

    /**
     * Update student information
     */
    public function updateStudent(Request $request, Webinar $webinar)
    {
        $request->validate([
            'student_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $request->student_id,
            'phone' => 'required|string|max:20|unique:users,phone,' . $request->student_id,
            'password' => 'nullable|string|min:6',
            'note' => 'nullable|string'
        ]);

        // Update user
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
        ];

        if ($request->password) {
            $updateData['password'] = Hash::make($request->password);
        }

        User::where('id', $request->student_id)->update($updateData);

        // Update note in webinar_students
        DB::table('webinar_students')
            ->where('webinar_id', $webinar->id)
            ->where('user_id', $request->student_id)
            ->update([
                'note' => $request->note ?? '',
                'updated_at' => now()
            ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật thông tin học sinh thành công'
        ]);
    }

    /**
     * Remove student from webinar
     */
    public function removeStudent(Request $request, Webinar $webinar)
    {
        $request->validate([
            'student_id' => 'required|exists:users,id'
        ]);

        $deleted = DB::table('webinar_students')
            ->where('webinar_id', $webinar->id)
            ->where('user_id', $request->student_id)
            ->delete();

        if ($deleted) {
            return response()->json([
                'success' => true,
                'message' => 'Đã xóa học sinh khỏi lớp thành công'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Không tìm thấy học sinh trong lớp'
        ]);
    }
} 